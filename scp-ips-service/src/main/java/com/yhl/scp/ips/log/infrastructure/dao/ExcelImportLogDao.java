package com.yhl.scp.ips.log.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO;
import com.yhl.scp.ips.log.vo.ExcelImportLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>ExcelImportLogDao</code>
 * <p>
 * Excel导入日志DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-17 13:49:15
 */
public interface ExcelImportLogDao extends BaseDao<ExcelImportLogPO, ExcelImportLogVO> {

    List<ExcelImportLogVO> selectParentLog();


    List<ExcelImportLogVO> selectByParentId(@Param("parentId") String parentId);

}
