package com.yhl.scp.ips.bpm.listener;

import groovyjarjarpicocli.CommandLine;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.variable.api.event.FlowableVariableEvent;
import org.springframework.stereotype.Component;

/**
 * <code>VariableCreateListener</code>
 * <p>
 * 变量创建监听器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 15:23:00
 */
@Component
@Slf4j
public class VariableCreateListener implements FlowableEventListener {

    @Override
    public void onEvent(FlowableEvent event) {
        if (event.getType() == FlowableEngineEventType.VARIABLE_CREATED) {
            FlowableVariableEvent variableEvent = (FlowableVariableEvent) event;
            log.info("变量创建: " + variableEvent.getVariableName() +
                    " = " + variableEvent.getVariableValue());
        }
    }

    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return "";
    }

}