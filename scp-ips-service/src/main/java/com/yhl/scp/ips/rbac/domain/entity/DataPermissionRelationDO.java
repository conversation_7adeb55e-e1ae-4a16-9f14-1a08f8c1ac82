package com.yhl.scp.ips.rbac.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>DataPermissionRelationDO</code>
 * <p>
 * 数据权限配置关联对象DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-28 16:42:30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataPermissionRelationDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 952451394522710241L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 配置ID
     */
    private String configId;
    /**
     * 对象分类
     */
    private String granularityType;
    /**
     * 具体对象
     */
    private String granularityValue;

}