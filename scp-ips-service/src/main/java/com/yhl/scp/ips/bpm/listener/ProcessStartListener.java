package com.yhl.scp.ips.bpm.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * <code>ProcessStartListener</code>
 * <p>
 * 流程启动监听器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 15:14:20
 */
@Component
@Slf4j
public class ProcessStartListener implements ExecutionListener {

    private static final long serialVersionUID = 2700243471514777742L;

    @Override
    public void notify(DelegateExecution execution) {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>> 流程启动: " + execution.getProcessInstanceId());
        execution.setVariable("startTime", System.currentTimeMillis());
    }

}