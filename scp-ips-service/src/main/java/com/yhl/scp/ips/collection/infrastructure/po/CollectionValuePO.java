package com.yhl.scp.ips.collection.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>CollectionValuePO</code>
 * <p>
 * 值集值PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-07 17:38:20
 */
public class CollectionValuePO extends BasePO implements Serializable {

    private static final long serialVersionUID = 918149096219152949L;

    /**
     * 值集ID
     */
    private String collectionId;
    /**
     * 值集值
     */
    private String collectionValue;
    /**
     * 值集含义
     */
    private String valueMeaning;
    /**
     * 排序号
     */
    private Integer valueSequence;
    /**
     * 父级值集值ID
     */
    private String parentValueId;
    /**
     * 标记
     */
    private String mark;
    /**
     * 描述
     */
    private String description;
    /**
     * 有效期起
     */
    private Date effectiveTime;
    /**
     * 有效期止
     */
    private Date expiryTime;

    public String getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(String collectionId) {
        this.collectionId = collectionId;
    }

    public String getCollectionValue() {
        return collectionValue;
    }

    public void setCollectionValue(String collectionValue) {
        this.collectionValue = collectionValue;
    }

    public String getValueMeaning() {
        return valueMeaning;
    }

    public void setValueMeaning(String valueMeaning) {
        this.valueMeaning = valueMeaning;
    }

    public Integer getValueSequence() {
        return valueSequence;
    }

    public void setValueSequence(Integer valueSequence) {
        this.valueSequence = valueSequence;
    }

    public String getParentValueId() {
        return parentValueId;
    }

    public void setParentValueId(String parentValueId) {
        this.parentValueId = parentValueId;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

}
