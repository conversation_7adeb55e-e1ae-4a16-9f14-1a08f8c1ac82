package com.yhl.scp.ips.algorithm;

import com.yhl.scp.ips.system.entity.AlgorithmLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <code>ScheduleAsync</code>
 * <p>
 * ScheduleAsync
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 15:38:05
 */
@Component
public class ScheduleAsync {

    @Autowired
    private FeignFactory feignFactory;

    /**
     * 不同版本之间异步解析结果
     *
     * @param algorithmLog 算法日志
     */
    @Async
    public void result(AlgorithmLog algorithmLog) {
        feignFactory.getAlgorithmFeign(algorithmLog.getModuleCode()).resultAnalysis(algorithmLog);
    }

    /**
     * 异步执行优化算法
     *
     * @param algorithmLog 算法日志
     */
    @Async
    public void schedule(AlgorithmLog algorithmLog) {
        feignFactory.getAlgorithmFeign(algorithmLog.getModuleCode())
                .execute(algorithmLog);
    }

}