package com.yhl.scp.ips.conf.domain.factory;

import com.yhl.scp.ips.conf.domain.entity.UrlTableDO;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.infrastructure.dao.UrlTableDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UrlTableFactory {

    @Resource
    private UrlTableDao urlTableDao;

    UrlTableDO create(UrlTableDTO dto) {
        // TODO
        return null;
    }

}
