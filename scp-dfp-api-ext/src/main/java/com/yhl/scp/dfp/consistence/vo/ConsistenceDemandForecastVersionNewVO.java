package com.yhl.scp.dfp.consistence.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>ConsistenceDemandForecastVersionVO</code>
 * <p>
 * 一致性业务预测版本VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:19:30
 */
@ApiModel(value = "一致性业务预测版本VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConsistenceDemandForecastVersionNewVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 888196524328181710L;

    /**
     * 计划周期
     */
    @ApiModelProperty(value = "计划周期")
    @FieldInterpretation(value = "计划周期")
    private String planPeriod;
    /**
     * 计划跨度
     */
    @ApiModelProperty(value = "计划跨度")
    @FieldInterpretation(value = "计划跨度")
    private Integer planHorizon;
    /**
     * 颗粒度
     */
    @ApiModelProperty(value = "颗粒度")
    @FieldInterpretation(value = "颗粒度")
    private String planGranularity;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private String versionCode;

    /**
     * 父版本ID
     */
    @ApiModelProperty(value = "父版本ID")
    @FieldInterpretation(value = "父版本ID")
    private String parentVersionId;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 关联业务预测版本
     */
    @ApiModelProperty(value = "关联业务预测版本")
    @FieldInterpretation(value = "关联业务预测版本")
    private String demandForecastVersionId;
    /**
     * 关联业务预测版本号
     */
    @ApiModelProperty(value = "关联业务预测版本号")
    @FieldInterpretation(value = "关联业务预测版本号")
    private String demandForecastVersionCode;
    /**
     * 生成方式
     */
    @ApiModelProperty(value = "生成方式")
    @FieldInterpretation(value = "生成方式")
    private String generateType;
    /**
     * 状态：未发布/已发布
     */
    @ApiModelProperty(value = "状态：未发布/已发布")
    @FieldInterpretation(value = "状态：未发布/已发布")
    private String versionStatus;
    
    /**
     * 是否为评审版本
     */
    @ApiModelProperty(value = "是否为评审版本")
    @FieldInterpretation(value = "是否为评审版本")
    private String reviewVersionFlag;

    //子版本
    private List<ConsistenceDemandForecastVersionVO> demandVersionVOList;


    @Override
    public void clean() {

    }

}
