package com.yhl.scp.dfp.stock.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OriginalFilmCurrentBatchQuantityVO</code>
 * <p>
 * 原片库存批次明细数据VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:26:41
 */
@ApiModel(value = "原片库存批次明细数据VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OriginalFilmCurrentBatchQuantityVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 958489403985306756L;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    @FieldInterpretation(value = "组织")
    private String organization;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subInventory;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String location;
    /**
     * 原片编码
     */
    @ApiModelProperty(value = "原片编码")
    @FieldInterpretation(value = "原片编码")
    private String originalFilmCode;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String color;
    /**
     * 产品规格
     */
    @ApiModelProperty(value = "产品规格")
    @FieldInterpretation(value = "产品规格")
    private String productSpec;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal thickness;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @FieldInterpretation(value = "等级")
    private String grade;
    /**
     * 批次等级代码
     */
    @ApiModelProperty(value = "批次等级代码")
    @FieldInterpretation(value = "批次等级代码")
    private String batchGradeCode;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    private String batchNumber;
    /**
     * 片/箱
     */
    @ApiModelProperty(value = "片/箱")
    @FieldInterpretation(value = "片/箱")
    private BigDecimal piecesPerBox;
    /**
     * 吨/箱
     */
    @ApiModelProperty(value = "吨/箱")
    @FieldInterpretation(value = "吨/箱")
    private BigDecimal tonsPerBox;
    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    @FieldInterpretation(value = "客户")
    private String customer;
    /**
     * 现存件数
     */
    @ApiModelProperty(value = "现存件数")
    @FieldInterpretation(value = "现存件数")
    private Integer currentPieces;
    /**
     * 吨数
     */
    @ApiModelProperty(value = "吨数")
    @FieldInterpretation(value = "吨数")
    private BigDecimal currentTons;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @FieldInterpretation(value = "入库时间")
    private Date storageTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateTime;

    @Override
    public void clean() {

    }

}
