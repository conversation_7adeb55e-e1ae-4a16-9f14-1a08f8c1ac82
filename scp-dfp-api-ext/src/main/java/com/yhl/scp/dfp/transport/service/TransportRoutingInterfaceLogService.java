package com.yhl.scp.dfp.transport.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.transport.dto.TransportRoutingInterfaceLogDTO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingInterfaceLogVO;

import java.util.List;

/**
 * <code>TransportRoutingInterfaceLogService</code>
 * <p>
 * mes_运输路径_留存应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 17:37:19
 */
public interface TransportRoutingInterfaceLogService extends BaseService<TransportRoutingInterfaceLogDTO, TransportRoutingInterfaceLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link TransportRoutingInterfaceLogVO}
     */
    List<TransportRoutingInterfaceLogVO> selectAll();

    BaseResponse<Void> syncTransportRoutings(String tenantId);

}
