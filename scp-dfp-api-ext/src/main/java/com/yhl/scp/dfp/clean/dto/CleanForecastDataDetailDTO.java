package com.yhl.scp.dfp.clean.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>CleanForecastDataDetailDTO</code>
 * <p>
 * 滚动预测数据明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 16:40:33
 */
@ApiModel(value = "滚动预测数据明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CleanForecastDataDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 172177659643733632L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String cleanForecastDataId;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private Date forecastTime;
    /**
     * 预测数量
     */
    @ApiModelProperty(value = "预测数量")
    private Integer forecastQuantity;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
