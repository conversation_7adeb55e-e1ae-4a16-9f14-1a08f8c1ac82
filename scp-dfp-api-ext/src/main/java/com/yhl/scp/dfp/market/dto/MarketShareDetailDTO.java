package com.yhl.scp.dfp.market.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MarketShareDetailDTO</code>
 * <p>
 * 市场占有率详情DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-26 17:24:29
 */
@ApiModel(value = "市场占有率详情DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MarketShareDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -23346046234557076L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String marketShareId;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private Date saleTime;
    /**
     * 市场占有率
     */
    @ApiModelProperty(value = "市场占有率")
    private String shareRate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
