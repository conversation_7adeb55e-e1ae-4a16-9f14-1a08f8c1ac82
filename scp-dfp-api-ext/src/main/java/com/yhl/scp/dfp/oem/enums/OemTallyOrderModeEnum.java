package com.yhl.scp.dfp.oem.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <p>
 * 理货单模式枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-27 18:05:17
 */
public enum OemTallyOrderModeEnum implements CommonEnum {

    /**
     * MES
     */
	MES("MES", "MES"),

    /**
     * GRP
     */
	GRP("GRP", "GRP");

    private String code;

    private String desc;

    OemTallyOrderModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}