package com.yhl.scp.dfp.release.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "业务预测发布（核心工序）请求DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReleaseCoreProcessDTO extends ReleaseDTO implements Serializable {

    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;

    @ApiModelProperty(value = "核心工序")
    private List<String> coreProcesses;

    @ApiModelProperty(value = "产品特性")
    private List<String> productSpecials;

    @ApiModelProperty(value = "车型编码")
    private List<String> vehicleModelCodes;

    @ApiModelProperty(value = "对比上次版本")
    private Boolean compareLastVersion;
}
