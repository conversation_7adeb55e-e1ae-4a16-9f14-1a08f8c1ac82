package com.yhl.scp.dfp.inventory.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.inventory.dto.HistoryInventoryBatchDetailDTO;
import com.yhl.scp.dfp.inventory.vo.HistoryInventoryBatchDetailVO;

import java.util.List;

/**
 * <code>HistoryInventoryBatchDetailService</code>
 * <p>
 * 历史库存批次明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-08 16:04:49
 */
public interface HistoryInventoryBatchDetailService extends BaseService<HistoryInventoryBatchDetailDTO, HistoryInventoryBatchDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link HistoryInventoryBatchDetailVO}
     */
    List<HistoryInventoryBatchDetailVO> selectAll();

    void doArchive();

}
