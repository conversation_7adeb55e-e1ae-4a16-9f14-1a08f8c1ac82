package com.yhl.scp.dfp.report.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>CustomerOrderWeeklyReportSimpleVO</code>
 * <p>
 * CustomerOrderWeeklyReportSimpleVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16 13:54:25
 */
@Data
public class CustomerOrderWeeklyReportSimpleVO implements Serializable {

    private static final long serialVersionUID = 5378944598494608921L;

    private String oemCode;

    private String vehicleModelCode;

    private Date timePoint;

    private BigDecimal quantity;

}