package com.yhl.scp.dfp.oem.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.dto.OemStockPointMapDTO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;

import java.util.List;

/**
 * <code>OemStockPointMapService</code>
 * <p>
 * 主机厂库存点关联关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:12:55
 */
public interface OemStockPointMapService extends BaseService<OemStockPointMapDTO, OemStockPointMapVO> {

    /**
     * 查询所有
     *
     * @return list {@link OemStockPointMapVO}
     */
    List<OemStockPointMapVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    List<OemStockPointMapVO> selectOemStockPointByOemCodes(List<String> oemCodes);

    List<LabelValue<String>> selectOemCodetByStockPoinCode(String stockPointCode);

    List<OemStockPointMapVO> selectOemStockPointByStockPointCode(List<String> stockPointByStockList);

    List<OemStockPointMapVO> selectExcludeStockPointCode(String stockPointCode);
}
