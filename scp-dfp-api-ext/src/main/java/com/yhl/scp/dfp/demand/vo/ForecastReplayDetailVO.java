package com.yhl.scp.dfp.demand.vo;

import com.yhl.scp.dfp.demand.enums.RowTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>ForecastReplayDetailVO</code>
 * <p>
 * ForecastReplayDetailVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-29 15:34:28
 */
@ApiModel(value = "预测复盘详情VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForecastReplayDetailVO implements Serializable {

    private static final long serialVersionUID = 3777561374833390650L;

    @ApiModelProperty(value = "预测版本ID")
    private String versionId;

    @ApiModelProperty(value = "预测版本代码")
    private String versionCode;

    @ApiModelProperty(value = "计划周期")
    private String planPeriod;

    @ApiModelProperty(value = "行类型")
    private RowTypeEnum rowType;

    @ApiModelProperty(value = "动态行列")
    private List<DynamicCellVO> cells;

}