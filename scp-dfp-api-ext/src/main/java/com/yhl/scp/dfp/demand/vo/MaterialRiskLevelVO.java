package com.yhl.scp.dfp.demand.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>MaterialRiskLevelVO</code>
 * <p>
 * MaterialRiskLevelVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-24 20:05:41
 */
@ApiModel(value = "高风险物料VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialRiskLevelVO implements Serializable {

    private static final long serialVersionUID = -8643072184813624766L;

    @ApiModelProperty(value = "成品编码")
    private String productCode;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

}