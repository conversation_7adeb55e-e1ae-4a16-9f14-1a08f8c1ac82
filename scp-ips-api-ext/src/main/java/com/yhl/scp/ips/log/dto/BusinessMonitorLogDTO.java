package com.yhl.scp.ips.log.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>BusinessMonitorLogDTO</code>
 * <p>
 * 业务监控日志DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 20:58:12
 */
@ApiModel(value = "业务监控日志DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BusinessMonitorLogDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -12192949154916662L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    private String scenario;
    /**
     * 业务频次
     */
    @ApiModelProperty(value = "业务频次")
    private String businessFrequency;
    /**
     * 业务代码
     */
    @ApiModelProperty(value = "业务代码")
    private String businessCode;
    /**
     * 操作方式
     */
    @ApiModelProperty(value = "操作方式")
    private String operationMethod;
    /**
     * 操作状态
     */
    @ApiModelProperty(value = "操作状态")
    private String operationStatus;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效
     */
    @ApiModelProperty(value = "是否生效")
    private String enabled;
    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private Integer versionValue;

}
