package com.yhl.scp.ips.rbac.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>UserMessageDTO</code>
 * <p>
 * UserMessageDTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:46:54
 */
@ApiModel(value = "用户信息消息表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserMessageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 976565818215260761L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private String roleId;
    /**
     * 消息来源
     */
    @ApiModelProperty(value = "消息来源")
    private String messageSource;
    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    private String messageType;
    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String messageTitle;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;
    /**
     * 消息链接
     */
    @ApiModelProperty(value = "消息链接")
    private String messageLink;
    /**
     * 消息紧急程度
     */
    @ApiModelProperty(value = "消息紧急程度")
    private String messageEmergency;
    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private String readStatus;
    /**
     * 额外信息
     */
    @ApiModelProperty(value = "额外信息")
    private String extraInfo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
