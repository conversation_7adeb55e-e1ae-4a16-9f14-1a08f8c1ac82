package com.yhl.scp.mds.overdeadlineday.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>MdsOverDeadlineDaysBasicDTO</code>
 * <p>
 * 超期界定天数基础DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-07 10:46:19
 */
@ApiModel(value = "超期界定天数基础DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MdsOverDeadlineDaysBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 945145327307950519L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 组织（库存点)
     */
    @ApiModelProperty(value = "组织（库存点)")
    @ExcelProperty(value = "组织代码（库存点)*")
    @ExcelPropertyCheck(required = true)
    private String stockPointCode;
    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    private String companyCode;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @ExcelProperty(value = "物料类型*")
    @ExcelPropertyCheck(required = true)
    private String materialsType;
    /**
     * 物料分类大类
     */
    @ApiModelProperty(value = "物料分类大类")
    @ExcelProperty(value = "物料分类大类*")
    @ExcelPropertyCheck(required = true)
    private String materialsMainClassification;
    /**
     * 物料分类小类
     */
    @ApiModelProperty(value = "物料分类小类")
    @ExcelProperty(value = "物料分类小类*")
    @ExcelPropertyCheck(required = true)
    private String materialsSecondClassification;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @ExcelProperty(value = "产品类型*")
    @ExcelPropertyCheck(required = true)
    private String productType;
    /**
     * 颜色代码
     */
    @ApiModelProperty(value = "颜色代码")
    @ExcelProperty(value = "颜色代码*")
    @ExcelPropertyCheck(required = true)
    private String colorCode;
    /**
     * 超期界定天数
     */
    @ApiModelProperty(value = "超期界定天数")
    @ExcelProperty(value = "超期界定天数*")
    @ExcelPropertyCheck(required = true)
    private BigDecimal overDeadlineDay;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

}
