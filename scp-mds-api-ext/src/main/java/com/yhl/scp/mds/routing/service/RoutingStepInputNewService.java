package com.yhl.scp.mds.routing.service;


import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.routing.dto.RoutingStepInputSyncDTO;

/**
 * <code>RoutingStepInputService</code>
 * <p>
 * 生产路径步骤输入物品应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-12 15:21:12
 */
public interface RoutingStepInputNewService  {

    /**
     * 同步的数据插入
     * @param bomSyncDTOList
     * @return
     */
    BaseResponse<String> doSyncData(List<RoutingStepInputSyncDTO> bomSyncDTOList);

    /**
     * 同步的接口
     * @param beginTime
     * @param orgCode
     * @return
     */
    BaseResponse<Void> syncData(String beginTime, String orgCode, String tenantCode);

    /**
     * 
     * @param params
     * @return
     */
	List<RoutingStepInputVO> selectByParams(Map<String, Object> params);

}
