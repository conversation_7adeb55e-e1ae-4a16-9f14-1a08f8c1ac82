package com.yhl.scp.dcp.externalApi.handler.google;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.ips.api.vo.LlmConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * <code>PdfParseHandler</code>
 * <p>
 * PDF解析处理器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-06 11:28:55
 */
@Slf4j
@Component
public class PdfParseHandler extends GeminiLLMHandler {

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.LLM.getCode(), ApiCategoryEnum.LLM_GEMINI_DEMAND_FILE_PARSE.getCode());
    }

    @Override
    protected String handleInternal(ApiConfigVO apiConfigVO, String prompt, LlmConfigVO llmConfigVO, Map<String, Object> params) {
        try {
            List<File> files = (List<File>) params.get("files");
            GeminiClient geminiClient = this.createClient(apiConfigVO, llmConfigVO);
            return parsePdf(geminiClient, files.get(0).getPath(), prompt);
        } catch (IOException ioe) {
            throw new BusinessException("解析文件异常:" + ioe.getMessage(), ioe);
        }
    }

    /**
     * 解析PDF
     *
     * @param geminiClient
     * @param filePath
     * @param prompt
     * @return
     * @throws IOException
     */
    public String parsePdf(GeminiClient geminiClient, String filePath, String prompt) throws IOException {
        String base64EncodedFile = encodeFileToBase64(filePath);
        String jsonPayload = buildPayload(base64EncodedFile, prompt);
        String apiResponse = geminiClient.sendRequest(jsonPayload);
        return extractResponse(apiResponse);
    }

    /**
     * 转成Base64
     *
     * @param filePath
     * @return
     * @throws IOException
     */
    private String encodeFileToBase64(String filePath) throws IOException {
        Path pdfPath = Paths.get(filePath);
        byte[] fileBytes = Files.readAllBytes(pdfPath);
        return Base64.getEncoder().encodeToString(fileBytes);
    }

    /**
     * 构建请求报文体
     *
     * @param base64EncodedFile 文件Base64
     * @param prompt            提示词
     * @return
     */
    private String buildPayload(String base64EncodedFile, String prompt) {
        JSONObject textPart = new JSONObject();
        textPart.put("text", prompt);

        JSONObject inlineDataPart = new JSONObject();
        inlineDataPart.put("mime_type", "application/pdf");
        inlineDataPart.put("data", base64EncodedFile);

        JSONObject dataPart = new JSONObject();
        dataPart.put("inline_data", inlineDataPart);

        JSONArray parts = new JSONArray();
        parts.add(textPart);
        parts.add(dataPart);

        JSONObject content = new JSONObject();
        content.put("parts", parts);

        JSONArray contents = new JSONArray();
        contents.add(content);

        JSONObject thinkingConfig = new JSONObject();
        thinkingConfig.put("thinking_budget", 0);

        JSONObject generationConfig = new JSONObject();
        generationConfig.put("thinking_config", thinkingConfig);

        JSONObject requestBody = new JSONObject();
        requestBody.put("contents", contents);
        requestBody.put("generationConfig", generationConfig);

        return requestBody.toString();
    }

    /**
     * 解析返回数据
     *
     * @param jsonResponse
     * @return
     */
    private String extractResponse(String jsonResponse) {
        JSONObject responseBody = JSONObject.parseObject(jsonResponse);
        JSONArray candidates = responseBody.getJSONArray("candidates");
        JSONObject firstCandidate = candidates.getJSONObject(0);
        JSONObject contentNode = firstCandidate.getJSONObject("content");
        JSONArray partsArray = contentNode.getJSONArray("parts");
        JSONObject firstPart = partsArray.getJSONObject(0);
        String rawText = firstPart.getString("text");

        // 从返回的文本中提取CSV内容
        String text = rawText.trim();
        if (text.startsWith("```csv")) {
            text = text.substring(6); // 移除 ```csv
        } else if (text.startsWith("```")) {
            text = text.substring(3); // 移除 ```
        }

        if (text.endsWith("```")) {
            text = text.substring(0, text.length() - 3); // 移除结尾的 ```
        }

        return text.trim(); // 返回清理后的CSV内容
    }

    /**
     * 数据转换
     *
     * @param body 响应体
     * @return
     */
    @Override
    protected Object convertData(String body) {
        return body;
    }

    /**
     * 处理结果集
     *
     * @param apiConfigVO 接口对象
     * @param params      请求参数
     * @param o           映射对象
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map params, Object o) {
        return (String) o;
    }

}
