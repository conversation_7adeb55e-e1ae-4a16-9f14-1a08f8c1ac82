package com.yhl.scp.dcp.sync.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dcp.sync.convertor.SyncCtrlConvertor;
import com.yhl.scp.dcp.sync.domain.entity.SyncCtrlDO;
import com.yhl.scp.dcp.sync.domain.service.SyncCtrlDomainService;
import com.yhl.scp.dcp.sync.dto.SyncCtrlDTO;
import com.yhl.scp.dcp.sync.infrastructure.dao.SyncCtrlDao;
import com.yhl.scp.dcp.sync.infrastructure.po.SyncCtrlPO;
import com.yhl.scp.dcp.sync.service.SyncCtrlService;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>SyncCtrlServiceImpl</code>
 * <p>
 * 外部api同步控制表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:17:38
 */
@Slf4j
@Service
public class SyncCtrlServiceImpl extends AbstractService implements SyncCtrlService {

    @Resource
    private SyncCtrlDao syncCtrlDao;

    @Resource
    private SyncCtrlDomainService syncCtrlDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SyncCtrlDTO syncCtrlDTO) {
        // 0.数据转换
        SyncCtrlDO syncCtrlDO = SyncCtrlConvertor.INSTANCE.dto2Do(syncCtrlDTO);
        SyncCtrlPO syncCtrlPO = SyncCtrlConvertor.INSTANCE.dto2Po(syncCtrlDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        syncCtrlDomainService.validation(syncCtrlDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(syncCtrlPO);
        syncCtrlDao.insert(syncCtrlPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SyncCtrlDTO syncCtrlDTO) {
        // 0.数据转换
        SyncCtrlDO syncCtrlDO = SyncCtrlConvertor.INSTANCE.dto2Do(syncCtrlDTO);
        SyncCtrlPO syncCtrlPO = SyncCtrlConvertor.INSTANCE.dto2Po(syncCtrlDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        syncCtrlDomainService.validation(syncCtrlDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(syncCtrlPO);
        syncCtrlDao.update(syncCtrlPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SyncCtrlDTO> list) {
        List<SyncCtrlPO> newList = SyncCtrlConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        syncCtrlDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SyncCtrlDTO> list) {
        List<SyncCtrlPO> newList = SyncCtrlConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        syncCtrlDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return syncCtrlDao.deleteBatch(idList);
        }
        return syncCtrlDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SyncCtrlVO selectByPrimaryKey(String id) {
        SyncCtrlPO po = syncCtrlDao.selectByPrimaryKey(id);
        return SyncCtrlConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXT_API_SYNC_CTRL")
    public List<SyncCtrlVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXT_API_SYNC_CTRL")
    public List<SyncCtrlVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SyncCtrlVO> dataList = syncCtrlDao.selectByCondition(sortParam, queryCriteriaParam);
        SyncCtrlServiceImpl target = SpringBeanUtils.getBean(SyncCtrlServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SyncCtrlVO> selectByParams(Map<String, Object> params) {
        List<SyncCtrlPO> list = syncCtrlDao.selectByParams(params);
        return SyncCtrlConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SyncCtrlVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
//        return ObjectTypeEnum.EXT_API_SYNC_CTRL.getCode();
    }

    @Override
    public List<SyncCtrlVO> invocation(List<SyncCtrlVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }


    /**
     * 查询同步控制记录
     *
     * @param apiConfigVO
     * @param groupValue
     * @return
     */
    @Override
    public SyncCtrlVO getSyncCtrl(ApiConfigVO apiConfigVO, String groupValue) {
        Map<String, Object> querySyncParamMap = new HashMap<>();
        querySyncParamMap.put("apiConfigId", apiConfigVO.getId());
        querySyncParamMap.put("groupValue", groupValue);
        List<SyncCtrlVO> syncCtrlVOList = this.selectByParams(querySyncParamMap);
        return CollUtil.isNotEmpty(syncCtrlVOList) ? syncCtrlVOList.get(0) : null;
    }

    /**
     * 保存同步控制记录
     *
     * @param apiConfigVO
     * @param groupValue
     * @param referValue
     */
    @Override
    public void saveSyncCtrl(ApiConfigVO apiConfigVO, String groupValue, String referValue) {
        SyncCtrlVO syncCtrlVO = this.getSyncCtrl(apiConfigVO, groupValue);
        SyncCtrlDTO syncCtrlDTO = null;
        if (syncCtrlVO != null) {
            // 更新记录
            syncCtrlDTO = SyncCtrlConvertor.INSTANCE.vo2Dto(syncCtrlVO);
            syncCtrlDTO.setSyncTime(new Date());
            syncCtrlDTO.setReferValue(referValue);
            this.doUpdate(syncCtrlDTO);
        } else {
            syncCtrlDTO = new SyncCtrlDTO();
            syncCtrlDTO.setApiConfigId(apiConfigVO.getId());
            syncCtrlDTO.setGroupValue(groupValue);
            syncCtrlDTO.setSyncTime(new Date());
            syncCtrlDTO.setReferValue(referValue);
            this.doCreate(syncCtrlDTO);
        }
    }
    /**
     * 保存同步时间
     *
     * @param apiConfigVO
     * @param groupValue
     */
    @Override
    public void saveSyncCtrlSyncTime(ApiConfigVO apiConfigVO, String groupValue) {
        SyncCtrlVO syncCtrlVO = this.getSyncCtrl(apiConfigVO, groupValue);
        SyncCtrlDTO syncCtrlDTO = null;
        if (syncCtrlVO != null) {
            // 更新记录
            syncCtrlDTO = SyncCtrlConvertor.INSTANCE.vo2Dto(syncCtrlVO);
            syncCtrlDTO.setSyncTime(new Date());
            this.doUpdate(syncCtrlDTO);
        } else {
            syncCtrlDTO = new SyncCtrlDTO();
            syncCtrlDTO.setApiConfigId(apiConfigVO.getId());
            syncCtrlDTO.setGroupValue(groupValue);
            syncCtrlDTO.setSyncTime(new Date());
            this.doCreate(syncCtrlDTO);
        }
    }
}
