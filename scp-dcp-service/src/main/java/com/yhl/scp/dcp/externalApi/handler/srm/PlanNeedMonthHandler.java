package com.yhl.scp.dcp.externalApi.handler.srm;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.externalApi.handler.erp.AuthHandler;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.material.plan.result.InsertRollPredictionMonthResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <code>PlanNeedMonthHandler</code>
 * <p>
 * 要货计划-下发要货计划（月）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-05 15:42:40
 */

@Component
@Slf4j
public class PlanNeedMonthHandler extends SyncDataHandler<List<InsertRollPredictionMonthResult>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @SneakyThrows
    @Override
    protected List<InsertRollPredictionMonthResult> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("下发要货计划（月）返回body为空");
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode
        JsonNode rootNode = objectMapper.readTree(body);
        // 获取 "Service" -> "Data" -> "Response" 节点
        JsonNode responseNode = rootNode.path("Service").path("Data").path("Response");
        // 确保 "InsertRollPredictionResult" 字段存在且是一个有效的字符串
        String insertResult = responseNode.path("InsertRollPredictionResult").asText(null);

        if (com.yhl.platform.common.utils.StringUtils.isEmpty(insertResult)) {
            // 处理字段不存在的情况
            throw new BusinessException("下发滚动预测要货计划接口响应对象（InsertRollPredictionResult）返回异常");
        }
        // 解析嵌套的 JSON 数组
        return objectMapper.readValue(insertResult,
                objectMapper.getTypeFactory().constructCollectionType(List.class, InsertRollPredictionMonthResult.class));
    }

    @SneakyThrows
    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode
        JsonNode rootNode = objectMapper.readTree("{\n" +
                "    \"Service\": {\n" +
                "        \"Route\": {\n" +
                "            \"SerialNO\": \"2024121203018000001\",\n" +
                "            \"ServiceID\": \"02003000000009\",\n" +
                "            \"SourceSysID\": \"03018\",\n" +
                "            \"ServiceTime\": \"20241212152805\",\n" +
                "            \"Soapenv\": \"http://schemas.xmlsoap.org/soap/envelope/\",\n" +
                "            \"ServiceResponse\": {\n" +
                "                \"Status\": \"COMPLETE\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"Data\": {\n" +
                "            \"Request\": {\n" +
                "                \"rollPredictionList\": {\n" +
                "                    \"RollPrediction\": {\n" +
                "                        \"PredictionNo\": \"WCYQC02_20250721null012\",\n" +
                "                        \"version\": \"1\",\n" +
                "                        \"orgCode\": \"323\",\n" +
                "                        \"orgName\": \"上海福耀\",\n" +
                "                        \"publisher\": \"默认\",\n" +
                "                        \"publishDate\": \"2025-07-21\",\n" +
                "                        \"supplierCode\": \"WCYQC02\",\n" +
                "                        \"supplierName\": \"东莞市辰研汽车配件有限公司\",\n" +
                "                        \"itemCode\": \"BFA2686G\",\n" +
                "                        \"itemName\": \"辅助固定支架 FJB-1844-G\",\n" +
                "                        \"requireDate\": \"2025-07\",\n" +
                "                        \"requireNum\": \"120.0000\"\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"Control\": null,\n" +
                "            \"Response\": {\n" +
                "                \"InsertRollPredictionResult\": \"[{\\\"status\\\":0,\\\"planCode\\\":\\\"WCYQC02_20250721null012\\\",\\\"itemCode\\\":\\\"BFA2686G\\\",\\\"versionNo\\\":1,\\\"requireDate\\\":\\\"2025-07\\\",\\\"msg\\\":\\\"插入成功。\\\"}]\"\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}");
        // 获取 "Service" -> "Data" -> "Response" 节点
        JsonNode responseNode = rootNode.path("Service").path("Data").path("Response");
        // 确保 "InsertRollPredictionResult" 字段存在且是一个有效的字符串
        String insertResult = responseNode.path("InsertRollPredictionResult").asText(null);
        Object o = objectMapper.readValue(insertResult,
                objectMapper.getTypeFactory().constructCollectionType(List.class, InsertRollPredictionMonthResult.class));
        System.out.println(o);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<InsertRollPredictionMonthResult> insertRollPredictionMonthResultList) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (Objects.isNull(insertRollPredictionMonthResultList)) {
            log.error("下发要货计划（月）为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        this.saveSyncCtrl(apiConfigVO, params, insertRollPredictionMonthResultList);
        return JSONObject.toJSONString(insertRollPredictionMonthResultList);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("下发要货计划（月）给SRM:{},{}", apiConfigVO, params);
        try {
            String srmToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("srmToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", srmToken, apiUri
                        , systemNumber, lastUpdateDate, url, bodyStr);
            }
            log.info("下发要货计划（月）给SRM请求参数为{}", bodyStr);
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("下发要货计划（月）给SRM请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("下发要货计划（月）给SRM处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_MONTH.getCode());
    }
    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        SyncCtrlVO syncCtrlVO = syncCtrlService.getSyncCtrl(apiConfigVO, this.getSyncGroupValue(apiConfigVO, params));
        return ObjUtil.isNotNull(syncCtrlVO) ? syncCtrlVO.getReferValue() : DateUtil.offsetDay(DateTime.now(), -7).toString(DatePattern.PURE_DATE_PATTERN);
    }

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("materialPlanNeed");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<InsertRollPredictionMonthResult> insertRollPredictionMonthResults) {
        Date lastUpdateDate = new Date();
        return DateUtils.dateToString(lastUpdateDate, DatePattern.PURE_DATE_PATTERN);
    }
}
