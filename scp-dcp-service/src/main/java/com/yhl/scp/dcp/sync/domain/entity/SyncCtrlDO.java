package com.yhl.scp.dcp.sync.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SyncCtrlDO</code>
 * <p>
 * 外部api同步控制表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:17:38
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SyncCtrlDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 522548820199607902L;

    /**
     * 主键
     */
    private String id;
    /**
     * 接口配置ID
     */
    private String apiConfigId;
    /**
     * 同步引用字段值
     */
    private String referValue;
    /**
     * 分组字段值(多字段用#隔开)
     */
    private String groupValue;
    /**
     * 最后同步时间
     */
    private Date syncTime;
    /**
     * 数据表版本
     */
    private Integer versionValue;

}
