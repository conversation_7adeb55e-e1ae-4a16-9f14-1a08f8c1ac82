package com.yhl.scp.dcp.externalApi.handler.fone;

import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.warning.dto.WarningSqlSettingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * description:
 * author：李杰 预警配置-发送邮件
 * email: <EMAIL>
 * date: 2025/3/1
 */
@Component
@Slf4j
public class AlertHandler extends SyncDataHandler<List> {

    @Resource
    private MessageHandler messageHandler;

    public String TYPE_EMAIL = "EMAIL";
    public String TYPE_SMS = "SMS";
    public String TYPE_WX = "WX";

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("发送预警信息:{},{}", apiConfigVO, params);
        }
        //获取传递前的数据
        Object mainObject = params.get("mainData");
        List<Map<String, Object>> mapList = null;
        WarningSqlSettingDTO warningSqlSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(mainObject), WarningSqlSettingDTO.class);
        if ("1".equals(warningSqlSettingDTO.getSendWay())){
            Object object = params.get("data");
            if (object instanceof List) {
                mapList = (List<Map<String, Object>>) object;
            }
        }
        Date date = new Date();
        // 生成邮件消息
        String messageInfo = createMessageBody(warningSqlSettingDTO, date);
        Map<String, Object> messageParamMap = new HashMap<>();
        messageParamMap.put("messageInfo", messageInfo);
        // 生成邮件附件
        if ("1".equals(warningSqlSettingDTO.getSendWay())) {
            File file = createAttachment(warningSqlSettingDTO, mapList, date);
            //有数据前提才发送文件
            if ("1".equals(warningSqlSettingDTO.getWarnType())){
                messageParamMap.put("file", file);
            }
            try {
                return messageHandler.handle(messageParamMap);
            } catch (Exception e) {
                log.error("发送预警信息报错:{},原因：{}", e.getMessage(), e.getCause());
                throw new BusinessException("发送预警信息报错:{},原因：{}", e.getMessage(), e.getCause());
            } finally {
                if (file != null && file.exists()) {
                    boolean delete = file.delete();
                    if (!delete) {
                        log.error("删除附件失败：" + file.getAbsolutePath());
                    }
                }
            }
        } else {
            return messageHandler.handle(messageParamMap);
        }
    }

    @Override
    protected List convertData(String body) {
        return Collections.emptyList();
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List list) {
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.EMAIL_MESSAGE.getCode());
    }

    /**
     * 获取邮箱中的excel
     *
     * @param warningSqlSettingDTO
     * @param mapList
     * @param date
     * @return
     */
    private File createAttachment(WarningSqlSettingDTO warningSqlSettingDTO, List<Map<String, Object>> mapList, Date date) {
        File file = null;
        ExcelWriter excelWriter = null;
        try {
            // 设置 Excel 文件名
            String nameOrTitle = warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14);
            String outFileName = nameOrTitle + ".xlsx";
            file = new File(outFileName);
            excelWriter = new ExcelWriter(true);

            // 获取表头
            Set<String> columns = new LinkedHashSet<>();
            for (Map<String, Object> map : mapList) {
                columns.addAll(map.keySet());
            }
            List<String> headerList = new ArrayList<>(columns);

            // 统计每列最大显示宽度（考虑中文字符）
            int[] maxWidths = new int[headerList.size()];
            for (int i = 0; i < headerList.size(); i++) {
                String header = headerList.get(i);
                maxWidths[i] = calculateDisplayWidth(header); // 表头也算进去
            }

            for (Map<String, Object> map : mapList) {
                for (int i = 0; i < headerList.size(); i++) {
                    String key = headerList.get(i);
                    Object value = map.get(key);
                    if (value != null) {
                        String cellValue = value.toString();
                        int width = calculateDisplayWidth(cellValue);
                        if (width > maxWidths[i]) {
                            maxWidths[i] = width;
                        }
                    }
                }
            }

            // 写入数据
            excelWriter.write(mapList, true);

            // 获取 sheet（假设只有一个 sheet）
            Sheet sheet = excelWriter.getSheet();

            // 设置每列宽度（单位：1/256 字符宽度）
            for (int i = 0; i < maxWidths.length; i++) {
                // 加一点 padding，比如 +2，防止内容贴边
                int width = (maxWidths[i] + 2) * 256;
                // 最大列宽限制为 255 * 256
                sheet.setColumnWidth(i, Math.min(width, 255 * 256));
            }

            excelWriter.flush(file);
            return file;
        } catch (Exception e) {
            log.error("生成excel文件报错：{}", e.getMessage());
            throw new BusinessException("生成excel文件报错：" + e.getMessage());
        } finally {
            if (excelWriter != null) {
                excelWriter.close();
            }
        }
    }

    /**
     * 计算字符串的显示宽度，假定中文字符占2个单位宽度，英文字符占1个单位宽度。
     */
    private int calculateDisplayWidth(String str) {
        int length = 0;
        for (char c : str.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                    || Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                    || Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                    || Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                    || Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                    || Character.UnicodeBlock.of(c) == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
                length += 2; // 中文字符或其他全角字符
            } else {
                length += 1; // 英文字符或半角字符
            }
        }
        return length;
    }

    /**
     * 获取邮件
     *
     * @param warningSqlSettingDTO
     * @param date
     * @return
     */
    private String createMessageBody(WarningSqlSettingDTO warningSqlSettingDTO, Date date) {
        if (Objects.isNull(warningSqlSettingDTO)) {
            log.error("主体对象数据为空");
            return "";
        }
        //塞入邮箱
        HashMap<String, Object> map = MapUtils.newHashMap();
        if ("1".equals(warningSqlSettingDTO.getSendWay())) {
            map.put("title", "预警配置:" + warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14));
            map.put("content", StringUtils.isEmpty(warningSqlSettingDTO.getTextContent())
                    ? "请及时处理。" : warningSqlSettingDTO.getTextContent().replaceAll("\n", "<br>").replaceAll(" ", "&nbsp;"));
            map.put("type", TYPE_EMAIL);
            map.put("useTemplate", false);
            map.put("receiver", warningSqlSettingDTO.getReceiver().replace(",", ";"));
            map.put("cc", warningSqlSettingDTO.getCarbonCopy().replace(",", ";"));
            map.put("bcc", warningSqlSettingDTO.getBlindCarbonCopy().replace(",", ";"));
            log.info("当前邮箱数据map={}", map);
        } else if ("2".equals(warningSqlSettingDTO.getSendWay())) {
            map.put("type", TYPE_WX);
            map.put("title", "预警配置:" + warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14));
            map.put("content", StringUtils.isEmpty(warningSqlSettingDTO.getTextContent())
                    ? "请及时处理。" : warningSqlSettingDTO.getTextContent().replaceAll("\n", "<br>").replaceAll(" ", "&nbsp;"));
            map.put("receiver", warningSqlSettingDTO.getStaffCode().replace(",", ";"));
            map.put("agentId", "1000206");
            log.info("当前企微数据map={}", map);
        }
        return JSON.toJSONString(map);
    }
}
