package com.yhl.scp.mds.mold.domain.factory;

import com.yhl.scp.mds.mold.domain.entity.MoldToolingGroupDO;
import com.yhl.scp.mds.mold.dto.MoldToolingGroupDTO;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingGroupDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MoldToolingGroupFactory</code>
 * <p>
 * 模具工装族领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:26:14
 */
@Component
public class MoldToolingGroupFactory {

    @Resource
    private MoldToolingGroupDao moldToolingGroupDao;

    MoldToolingGroupDO create(MoldToolingGroupDTO dto) {
        // TODO
        return null;
    }

}
