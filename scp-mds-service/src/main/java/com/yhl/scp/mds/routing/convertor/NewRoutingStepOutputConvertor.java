package com.yhl.scp.mds.routing.convertor;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.yhl.scp.mds.routing.domain.entity.NewRoutingStepOutputDO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepOutputDTO;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepOutputVO;

/**
 * <code>RoutingStepOutputConvertor</code>
 * <p>
 * 生产路径步骤输出物品转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:58:33
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NewRoutingStepOutputConvertor {

	NewRoutingStepOutputConvertor INSTANCE = Mappers.getMapper(NewRoutingStepOutputConvertor.class);

	NewRoutingStepOutputDO dto2Do(NewRoutingStepOutputDTO obj);

    List<NewRoutingStepOutputDO> dto2Dos(List<NewRoutingStepOutputDTO> list);

    NewRoutingStepOutputDTO do2Dto(NewRoutingStepOutputDO obj);

    List<NewRoutingStepOutputDTO> do2Dtos(List<NewRoutingStepOutputDO> list);

    NewRoutingStepOutputDTO vo2Dto(NewRoutingStepOutputVO obj);

    List<NewRoutingStepOutputDTO> vo2Dtos(List<NewRoutingStepOutputVO> list);

    NewRoutingStepOutputVO po2Vo(NewRoutingStepOutputPO obj);

    List<NewRoutingStepOutputVO> po2Vos(List<NewRoutingStepOutputPO> list);

    NewRoutingStepOutputPO dto2Po(NewRoutingStepOutputDTO obj);

    List<NewRoutingStepOutputPO> dto2Pos(List<NewRoutingStepOutputDTO> obj);

    NewRoutingStepOutputVO do2Vo(NewRoutingStepOutputDO obj);

    NewRoutingStepOutputPO do2Po(NewRoutingStepOutputDO obj);

    NewRoutingStepOutputDO po2Do(NewRoutingStepOutputPO obj);

}
