package com.yhl.scp.mds.routing.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>NewRoutingStepPO</code>
 * <p>
 * 生产路径步骤PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 11:27:00
 */
public class NewRoutingStepPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 232891200688711474L;

        /**
     * 路径ID
     */
        private String routingId;
        /**
     * 顺序号
     */
        private Integer sequenceNo;
        /**
     * 前工序顺序号
     */
        private String preRoutingStepSequenceNo;
        /**
     * 后工序顺序号
     */
        private String nextRoutingStepSequenceNo;
        /**
     * 成品率
     */
        private BigDecimal yield;
        /**
     * 损耗策略
     */
        private String scrapStrategy;
        /**
     * 百分比损耗率
     */
        private BigDecimal percentageScrapRate;
        /**
     * 固定损耗数
     */
        private BigDecimal scrap;
        /**
     * 与前工序数量比
     */
        private BigDecimal preProcessRatio;
        /**
     * 加工方式
     */
        private String processingType;
        /**
     * 接续任务
     */
        private String connectionTask;
        /**
     * 接续方式
     */
        private String connectionType;
        /**
     * 最大接续时间
     */
        private Integer maxConnectionDuration;
        /**
     * 最小接续时间
     */
        private Integer minConnectionDuration;
        /**
     * 标准工艺ID
     */
        private String standardStepId;
        /**
     * 计数单位ID
     */
        private String countingUnitId;
        /**
     * 失效原因
     */
        private String expireReason;
        /**
     * 版本
     */
        private Integer versionValue;
        /**
     * 工序行ID
     */
        private String routingRowId;
        /**
     * 部门编码
     */
        private String deptCode;
        /**
     * 部门名称
     */
        private String deptName;
        /**
     * 上次更新时间
     */
        private Date lastUpdateTime;
        /**
     * 有效期起
     */
        private Date effectiveBeginTime;
        /**
     * 有效期止
     */
        private Date effectiveEndTime;
        /**
     * 是否有效
     */
        private String effective;

    public String getRoutingId() {
        return routingId;
    }

    public void setRoutingId(String routingId) {
        this.routingId = routingId;
    }

    public Integer getSequenceNo() {
        return sequenceNo;
    }

    public void setSequenceNo(Integer sequenceNo) {
        this.sequenceNo = sequenceNo;
    }

    public String getPreRoutingStepSequenceNo() {
        return preRoutingStepSequenceNo;
    }

    public void setPreRoutingStepSequenceNo(String preRoutingStepSequenceNo) {
        this.preRoutingStepSequenceNo = preRoutingStepSequenceNo;
    }

    public String getNextRoutingStepSequenceNo() {
        return nextRoutingStepSequenceNo;
    }

    public void setNextRoutingStepSequenceNo(String nextRoutingStepSequenceNo) {
        this.nextRoutingStepSequenceNo = nextRoutingStepSequenceNo;
    }

    public BigDecimal getYield() {
        return yield;
    }

    public void setYield(BigDecimal yield) {
        this.yield = yield;
    }

    public String getScrapStrategy() {
        return scrapStrategy;
    }

    public void setScrapStrategy(String scrapStrategy) {
        this.scrapStrategy = scrapStrategy;
    }

    public BigDecimal getPercentageScrapRate() {
        return percentageScrapRate;
    }

    public void setPercentageScrapRate(BigDecimal percentageScrapRate) {
        this.percentageScrapRate = percentageScrapRate;
    }

    public BigDecimal getScrap() {
        return scrap;
    }

    public void setScrap(BigDecimal scrap) {
        this.scrap = scrap;
    }

    public BigDecimal getPreProcessRatio() {
        return preProcessRatio;
    }

    public void setPreProcessRatio(BigDecimal preProcessRatio) {
        this.preProcessRatio = preProcessRatio;
    }

    public String getProcessingType() {
        return processingType;
    }

    public void setProcessingType(String processingType) {
        this.processingType = processingType;
    }

    public String getConnectionTask() {
        return connectionTask;
    }

    public void setConnectionTask(String connectionTask) {
        this.connectionTask = connectionTask;
    }

    public String getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(String connectionType) {
        this.connectionType = connectionType;
    }

    public Integer getMaxConnectionDuration() {
        return maxConnectionDuration;
    }

    public void setMaxConnectionDuration(Integer maxConnectionDuration) {
        this.maxConnectionDuration = maxConnectionDuration;
    }

    public Integer getMinConnectionDuration() {
        return minConnectionDuration;
    }

    public void setMinConnectionDuration(Integer minConnectionDuration) {
        this.minConnectionDuration = minConnectionDuration;
    }

    public String getStandardStepId() {
        return standardStepId;
    }

    public void setStandardStepId(String standardStepId) {
        this.standardStepId = standardStepId;
    }

    public String getCountingUnitId() {
        return countingUnitId;
    }

    public void setCountingUnitId(String countingUnitId) {
        this.countingUnitId = countingUnitId;
    }

    public String getExpireReason() {
        return expireReason;
    }

    public void setExpireReason(String expireReason) {
        this.expireReason = expireReason;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getRoutingRowId() {
        return routingRowId;
    }

    public void setRoutingRowId(String routingRowId) {
        this.routingRowId = routingRowId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getEffectiveBeginTime() {
        return effectiveBeginTime;
    }

    public void setEffectiveBeginTime(Date effectiveBeginTime) {
        this.effectiveBeginTime = effectiveBeginTime;
    }

    public Date getEffectiveEndTime() {
        return effectiveEndTime;
    }

    public void setEffectiveEndTime(Date effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }

    public String getEffective() {
        return effective;
    }

    public void setEffective(String effective) {
        this.effective = effective;
    }

}
