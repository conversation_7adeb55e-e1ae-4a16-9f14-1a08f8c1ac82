package com.yhl.scp.mds.routing.service.impl;

import cn.hutool.core.date.StopWatch;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductRouting;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesOpYield;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.convertor.ProductRoutingConvertor;
import com.yhl.scp.mds.routing.convertor.ProductRoutingStepConvertor;
import com.yhl.scp.mds.routing.domain.entity.ProductRoutingDO;
import com.yhl.scp.mds.routing.domain.service.ProductRoutingDomainService;
import com.yhl.scp.mds.routing.dto.ProductRoutingDTO;
import com.yhl.scp.mds.routing.dto.ProductRoutingStepDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.ProductRoutingDao;
import com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO;
import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.routing.vo.ProductRoutingStepVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductRoutingServiceImpl</code>
 * <p>
 * 物品工艺路径应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:51:45
 */
@Slf4j
@Service
public class ProductRoutingServiceImpl extends AbstractService implements ProductRoutingService {

    @Resource
    private ProductRoutingDao productRoutingDao;

    @Resource
    private ProductRoutingDomainService productRoutingDomainService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private ProductRoutingStepService productRoutingStepService;

    @Resource
    private StandardStepService standardStepService;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    public static final String DIRECT_CALLER_MSG = "Direct caller: {}.{} ({}:{})";

    public static final String POSITION_MSG = "{}at {}.{} ({}:{})";

    public static final String STACK_TRACE_MSG = "Complete call stack trace:";

    @Override
    public BaseResponse<Void> doCreate(ProductRoutingDTO productRoutingDTO) {
        // 0.数据转换
        ProductRoutingDO productRoutingDO = ProductRoutingConvertor.INSTANCE.dto2Do(productRoutingDTO);
        ProductRoutingPO productRoutingPO = ProductRoutingConvertor.INSTANCE.dto2Po(productRoutingDTO);
        // 1.数据校验
        productRoutingDomainService.validation(productRoutingDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productRoutingPO);
        productRoutingDao.insert(productRoutingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductRoutingDTO productRoutingDTO) {
        // 0.数据转换
        ProductRoutingDO productRoutingDO = ProductRoutingConvertor.INSTANCE.dto2Do(productRoutingDTO);
        ProductRoutingPO productRoutingPO = ProductRoutingConvertor.INSTANCE.dto2Po(productRoutingDTO);
        // 1.数据校验
        productRoutingDomainService.validation(productRoutingDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productRoutingPO);
        productRoutingDao.update(productRoutingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductRoutingDTO> list) {
        List<ProductRoutingPO> newList = ProductRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productRoutingDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductRoutingDTO> list) {
        // 获取当前线程的堆栈跟踪
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        log.info("productRoutingService doUpdateBatch method called with {} records", list != null ? list.size() : 0);

        if (stackTrace.length > 2) {
            // 索引0是getStackTrace，索引1是当前方法
            StackTraceElement caller = stackTrace[2];
            log.info(DIRECT_CALLER_MSG, caller.getClassName(), caller.getMethodName(), caller.getFileName(),
                    caller.getLineNumber());
        }

        // 打印完整调用链
        log.info(STACK_TRACE_MSG);
        for (int i = 1; i < stackTrace.length; i++) {
            String indentation = StringUtils.repeat("  ", i - 1);
            log.info(POSITION_MSG, indentation, stackTrace[i].getClassName(), stackTrace[i].getMethodName(),
                    stackTrace[i].getFileName(), stackTrace[i].getLineNumber());
        }
        List<ProductRoutingPO> newList = ProductRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        log.info("Executing batch update product_routing in database...");
        productRoutingDao.updateBatch(newList);
        log.info("Batch update completed product_routing successfully");
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productRoutingDao.deleteBatch(idList);
        }
        return productRoutingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductRoutingVO selectByPrimaryKey(String id) {
        ProductRoutingPO po = productRoutingDao.selectByPrimaryKey(id);
        return ProductRoutingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_ROUTING")
    public List<ProductRoutingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_ROUTING")
    public List<ProductRoutingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductRoutingVO> dataList = productRoutingDao.selectByCondition(sortParam, queryCriteriaParam);
        ProductRoutingServiceImpl target = SpringBeanUtils.getBean(ProductRoutingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductRoutingVO> selectByParams(Map<String, Object> params) {
        List<ProductRoutingPO> list = productRoutingDao.selectByParams(params);
        return ProductRoutingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductRoutingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<ProductRoutingVO> selectByRoutingSequenceIds(List<String> routingSequenceIds) {
        return productRoutingDao.selectByRoutingSequenceIds(routingSequenceIds);
    }

    @Override
    public BaseResponse<Void> syncProductRoutings(String tenantId) {
        Map<String, Object> params = ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        List<String> orgCodes = newStockPointVOS.stream().filter(t ->
                        StringUtils.isNotEmpty(t.getInterfaceFlag())).filter(t ->
                        t.getInterfaceFlag().contains(ApiCategoryEnum.PRODUCT_ROUTING.getCode()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        for (String stockPoint : orgCodes) {
            // 调用远程的工艺路径信息
            Map<String, Object> newStockPointMap = new HashMap<>(2);
            newStockPointMap.put("stockPointCode", stockPoint);
            newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PRODUCT_ROUTING.getCode(), newStockPointMap);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public List<ProductRoutingVO> selectByProductIds(List<String> productIds) {
        if (CollectionUtils.isNotEmpty(productIds)) {
            return ProductRoutingConvertor.INSTANCE.po2Vos(productRoutingDao.selectByProductIds(productIds));
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<ProductRoutingDTO> list) {
        // 获取当前线程的堆栈跟踪
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        log.info("productRoutingService doCreateBatchWithPrimaryKey method called with {} records",
                list != null ? list.size() : 0);

        if (stackTrace.length > 2) {
            // 索引0是getStackTrace，索引1是当前方法
            StackTraceElement caller = stackTrace[2];
            log.info(DIRECT_CALLER_MSG, caller.getClassName(), caller.getMethodName(), caller.getFileName(),
                    caller.getLineNumber());
        }

        // 打印完整调用链
        log.info(STACK_TRACE_MSG);
        for (int i = 1; i < stackTrace.length; i++) {
            String indentation = StringUtils.repeat("  ", i-1);
            log.info(POSITION_MSG, indentation, stackTrace[i].getClassName(), stackTrace[i].getMethodName(),
                    stackTrace[i].getFileName(), stackTrace[i].getLineNumber());
        }
        List<ProductRoutingPO> newList = ProductRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        log.info("Executing bulkUpdateOrCreate product_routing in database...");
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                productRoutingDao.insertBatchWithPrimaryKey(poList), 2500);
        log.info("Batch bulkUpdateOrCreate product_routing completed successfully");
    }

    @Override
    public void doCreateBatchWithPartition(List<ProductRoutingDTO> list) {
        List<ProductRoutingPO> newList = ProductRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                productRoutingDao.insertBatch(poList), 2500);
    }

    @Override
    public void doUpdateBatchWithPartition(List<ProductRoutingDTO> list) {
        // 获取当前线程的堆栈跟踪
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        log.info("productRoutingService doUpdateBatchWithPartition method called with {} records",
                list != null ? list.size() : 0);

        if (stackTrace.length > 2) {
            // 索引0是getStackTrace，索引1是当前方法
            StackTraceElement caller = stackTrace[2];
            log.info(DIRECT_CALLER_MSG, caller.getClassName(), caller.getMethodName(), caller.getFileName(),
                    caller.getLineNumber());
        }

        // 打印完整调用链
        log.info(STACK_TRACE_MSG);
        for (int i = 1; i < stackTrace.length; i++) {
            String indentation = StringUtils.repeat("  ", i-1);
            log.info(POSITION_MSG, indentation, stackTrace[i].getClassName(), stackTrace[i].getMethodName(),
                    stackTrace[i].getFileName(), stackTrace[i].getLineNumber());
        }
        List<ProductRoutingPO> newList = ProductRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        log.info("Executing doUpdateBatchWithPartition product_routing in database...");
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                productRoutingDao.updateBatch(poList), 2500);
        log.info("Executing doUpdateBatchWithPartition product_routing in database...");

    }

    @Override
    public BaseResponse<Void> doSync(List<ErpProductRouting> list, String scenario) {
        StopWatch stopWatch = new StopWatch("ProductRouting Sync");
        stopWatch.start("Data Processing");

        log.info("开始处理物料工艺路径数据，大小：{}", list != null ? list.size() : 0);
        if (CollectionUtils.isEmpty(list)) {
            log.error("接口返回物料工艺路径数据为空");
            return BaseResponse.error("接口返回物料工艺路径数据为空");
        }

        List<String> routingSequenceIds = list.stream().map(ErpProductRouting::getRoutingSequenceId)
                .distinct().collect(Collectors.toList());
        log.info("接口routingSequenceIds:{}", routingSequenceIds);
        List<String> operationSequenceIds = list.stream().map(ErpProductRouting::getOperationSequenceId)
                .distinct().collect(Collectors.toList());
        log.info("接口operationSequenceIds:{}", operationSequenceIds);
        List<ProductRoutingVO> oldRoutingVOList = this.selectByRoutingSequenceIds(routingSequenceIds);
        List<ProductRoutingStepVO> oldRoutingStepList = productRoutingStepService.selectByParams(ImmutableMap
                .of("operationSequenceIds", operationSequenceIds));
        Map<String, ProductRoutingVO> oldRoutingVOMap = CollectionUtils.isEmpty(oldRoutingVOList)
                ? new HashMap<>() : oldRoutingVOList.stream().collect(Collectors
                .toMap(ProductRoutingVO::getRoutingSequenceId, Function.identity(), (v1, v2) -> v1));
        Map<String, ProductRoutingStepVO> oldRoutingStepVOMap = CollectionUtils.isEmpty(oldRoutingStepList)
                ? new HashMap<>() : oldRoutingStepList.stream().collect(Collectors
                .toMap(ProductRoutingStepVO::getOperationSequenceId, Function.identity(), (v1, v2) -> v1));
        List<ProductRoutingDTO> insertProductRoutingDTOs = Lists.newArrayList();
        List<ProductRoutingDTO> updateProductRoutingDTOs = Lists.newArrayList();

        List<ProductRoutingStepDTO> allStepList = new ArrayList<>();
        List<ProductRoutingStepDTO> insertRoutingStepDTOs;
        List<ProductRoutingStepDTO> updateRoutingStepDTOs;

        List<String> productCodes = list.stream().map(ErpProductRouting::getItemCode)
                .distinct().collect(Collectors.toList());
        Map<String, Object> params = ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> stockPoints = newStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(stockPoints)) {
            log.error("系统找不到库存点信息");
            return BaseResponse.error("系统找不到库存点信息");
        }
        Map<String, NewStockPointVO> stockMap = stockPoints.stream().collect(Collectors
                .toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByProductCode(productCodes);
        Map<String, NewProductStockPointPO> stockPointPOMap = newProductStockPointPOS.stream()
                .collect(Collectors.toMap(t -> t.getProductCode() + "|" + t.getStockPointCode(),
                        Function.identity(), (v1, v2) -> v1));

        if (CollectionUtils.isEmpty(newProductStockPointPOS)) {
            log.error("系统找不到物料信息");
            return BaseResponse.error("系统找不到物料信息");
        }
        Map<String, Object> standardStepParam = new HashMap<>();
        standardStepParam.put("enable", YesOrNoEnum.YES.getCode());
        List<StandardStepVO> standardStepVOS = standardStepService.selectByParams(standardStepParam);
        Map<String, StandardStepVO> standardRoutingStepVOMap = CollectionUtils.isEmpty(standardStepVOS)
                ? new HashMap<>() : standardStepVOS.stream().collect(Collectors.toMap(t ->
                t.getStockPointCode() + "|" + t.getStandardStepCode(), Function.identity(), (v1, v2) -> v1));
        ProductRoutingStepConvertor instance = ProductRoutingStepConvertor.INSTANCE;
        for (ErpProductRouting erpProductRouting : list) {
            String productCode = erpProductRouting.getItemCode();
            String stockPointCode = erpProductRouting.getOrganizationCode();
            String stockPointId = stockMap.get(stockPointCode).getId();
            String routingDataKey = erpProductRouting.getRoutingSequenceId();
            String routingStepDataKey = erpProductRouting.getOperationSequenceId();
            String productKey = productCode + "|" + stockPointCode;
            log.info("工艺路径编码:{},组织:{},工序:{}",productCode,stockPointCode,erpProductRouting.getOperationSeqNum());
            if (!stockPointPOMap.containsKey(productKey)) {
                log.error("没有找到对应的库存点息，接口物料编码：{}，接口库存点：{}", productCode, stockPointCode);
                continue;
            }
            NewProductStockPointPO newProductStockPointPO = stockPointPOMap.get(productKey);
            ProductRoutingDTO productRoutingDTO = new ProductRoutingDTO();
            if (oldRoutingVOMap.containsKey(routingDataKey)) {
                log.info("已有数据，正在处理数据，routingDataKey:{}", routingDataKey);
                ProductRoutingVO oldProductRoutingVO = oldRoutingVOMap.get(routingDataKey);
                productRoutingDTO.setId(oldProductRoutingVO.getId());
                productRoutingDTO.setStockPointId(stockPointId);
                productRoutingDTO.setProductId(newProductStockPointPO.getId());
                productRoutingDTO.setLastUpdateDate(erpProductRouting.getLastUpdateDate());
                productRoutingDTO.setRoutingSequenceId(erpProductRouting.getRoutingSequenceId());
                if (Objects.equals("有效", erpProductRouting.getItemStatus())
                        || Objects.equals("PR-STOP", erpProductRouting.getItemStatus())) {
                    productRoutingDTO.setEnabled(YesOrNoEnum.YES.getCode());
                    productRoutingDTO.setEffective(YesOrNoEnum.YES.getCode());
                } else {
                    productRoutingDTO.setEnabled(YesOrNoEnum.NO.getCode());
                    productRoutingDTO.setEffective(YesOrNoEnum.NO.getCode());
                }

                productRoutingDTO.setRoutingCode(erpProductRouting.getItemCode());
                productRoutingDTO.setRoutingName(erpProductRouting.getItemName());
                productRoutingDTO.setPriority(1);
                productRoutingDTO.setProductionVersion(erpProductRouting.getProcessRevision());
                productRoutingDTO.setLotSize(BigDecimal.valueOf(1));
                updateProductRoutingDTOs.add(productRoutingDTO);

                // 格式化打印工艺路径头更新信息
                logRoutingHeaderInfo("UPDATE", productRoutingDTO, erpProductRouting, stockPointCode);
            } else {
                log.info("没有找到对应的数据，正在处理新增数据，routingDataKey:{}", routingDataKey);
                productRoutingDTO.setId(UUIDUtil.getUUID());
                productRoutingDTO.setStockPointId(stockPointId);
                productRoutingDTO.setProductId(newProductStockPointPO.getId());
                productRoutingDTO.setLastUpdateDate(erpProductRouting.getLastUpdateDate());
                productRoutingDTO.setRoutingSequenceId(erpProductRouting.getRoutingSequenceId());
                if (Objects.equals("有效", erpProductRouting.getItemStatus())
                        || Objects.equals("PR-STOP", erpProductRouting.getItemStatus())) {
                    productRoutingDTO.setEnabled(YesOrNoEnum.YES.getCode());
                    productRoutingDTO.setEffective(YesOrNoEnum.YES.getCode());
                } else {
                    productRoutingDTO.setEnabled(YesOrNoEnum.NO.getCode());
                    productRoutingDTO.setEffective(YesOrNoEnum.NO.getCode());
                }
                productRoutingDTO.setRoutingCode(erpProductRouting.getItemCode());
                productRoutingDTO.setRoutingName(erpProductRouting.getItemName());
                productRoutingDTO.setPriority(1);
                productRoutingDTO.setProductionVersion(erpProductRouting.getProcessRevision());
                productRoutingDTO.setLotSize(BigDecimal.valueOf(1));
                insertProductRoutingDTOs.add(productRoutingDTO);

                // 格式化打印工艺路径头新增信息
                logRoutingHeaderInfo("INSERT", productRoutingDTO, erpProductRouting, stockPointCode);
            }
            // 处理产品工艺路径步骤数据
            ProductRoutingStepDTO productRoutingStepDTO = new ProductRoutingStepDTO();
            String standardStepDataKey = stockPointCode + "|" + erpProductRouting.getOperationSeqNum();
            if (!standardRoutingStepVOMap.containsKey(standardStepDataKey)) {
                log.error("没有找到对应的标准产品工艺路径步骤，接口物料工艺路径步骤序号：{}，库存点：{}",
                        erpProductRouting.getOperationSeqNum(), stockPointCode);
                continue;
            }
            log.info("工艺路径步骤key:{}", standardStepDataKey);
            String standardStepId = standardRoutingStepVOMap.get(standardStepDataKey).getId();

            if (oldRoutingStepVOMap.containsKey(routingStepDataKey)) {
                log.info("已有数据，正在处理行数据，routingStepDataKey:{}", routingStepDataKey);
                ProductRoutingStepVO oldRoutingStepVO = oldRoutingStepVOMap.get(routingStepDataKey);
                productRoutingStepDTO = instance.vo2Dto(oldRoutingStepVO);
                productRoutingStepDTO.setSequenceNo(Integer.valueOf(erpProductRouting.getOperationSeqNum()));
                productRoutingStepDTO.setOperationSequenceId(erpProductRouting.getOperationSequenceId());
                productRoutingStepDTO.setRoutingId(erpProductRouting.getRoutingSequenceId());
                productRoutingStepDTO.setYield(BigDecimal.valueOf(1));
                Date now = new Date();
                Date startDate = erpProductRouting.getStartDate();
                Date disableDate = erpProductRouting.getDisableDate();
                productRoutingStepDTO.setStartDate(startDate);
                productRoutingStepDTO.setDisableDate(disableDate);
                // 如果开始日期为空,则默认无效
                if (Objects.isNull(startDate)) {
                    log.info("更新时，开始日期为空");
                    productRoutingStepDTO.setEffective(YesOrNoEnum.NO.getCode());
                }
                // 如果失效日期为空,只需要判断是否在开始日期之后
                if (Objects.isNull(disableDate)) {
                    log.info("更新时，失效日期为空");
                    productRoutingStepDTO.setEffective(now.after(startDate)
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                }
                if (Objects.nonNull(startDate) && Objects.nonNull(disableDate)) {
                    log.info("更新时，开始日期，失效日期不为空");
                    productRoutingStepDTO.setEffective(now.after(startDate) && now.before(disableDate)
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                }
                productRoutingStepDTO.setLastUpdateDate(erpProductRouting.getLastUpdateDate());

                // 格式化打印工艺路径步骤更新信息
                logRoutingStepInfo("UPDATE", productRoutingStepDTO, erpProductRouting, stockPointCode);
            } else {
                log.info("没有找到对应的数据，正在处理新增数据，routingStepDataKey:{}", routingStepDataKey);
                productRoutingStepDTO.setSequenceNo(Integer.valueOf(erpProductRouting.getOperationSeqNum()));
                productRoutingStepDTO.setOperationSequenceId(erpProductRouting.getOperationSequenceId());
                productRoutingStepDTO.setRoutingId(erpProductRouting.getRoutingSequenceId());
                productRoutingStepDTO.setYield(BigDecimal.valueOf(1));
                productRoutingStepDTO.setLastUpdateDate(erpProductRouting.getLastUpdateDate());
                productRoutingStepDTO.setSequenceNo(Integer.valueOf(erpProductRouting.getOperationSeqNum()));
                productRoutingStepDTO.setLastUpdateDate(erpProductRouting.getLastUpdateDate());
                Date now = new Date();
                Date startDate = erpProductRouting.getStartDate();
                Date disableDate = erpProductRouting.getDisableDate();
                productRoutingStepDTO.setStartDate(startDate);
                productRoutingStepDTO.setDisableDate(disableDate);
                // 如果开始日期为空,则默认无效
                if (Objects.isNull(startDate)) {
                    log.info("新增时，开始日期为空");
                    productRoutingStepDTO.setEffective(YesOrNoEnum.NO.getCode());
                }
                // 如果失效日期为空,只需要判断是否在开始日期之后
                if (Objects.isNull(disableDate)) {
                    log.info("新增时，失效日期为空");
                    productRoutingStepDTO.setEffective(now.after(startDate)
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                }
                if (Objects.nonNull(startDate) && Objects.nonNull(disableDate)) {
                    log.info("新增时，开始日期，失效日期不为空");
                    productRoutingStepDTO.setEffective(now.after(startDate) && now.before(disableDate)
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                }

                // 格式化打印工艺路径步骤新增信息
                logRoutingStepInfo("INSERT", productRoutingStepDTO, erpProductRouting, stockPointCode);
            }
            productRoutingStepDTO.setEnabled(productRoutingStepDTO.getEffective().equals(YesOrNoEnum.YES.getCode())
                    ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            productRoutingStepDTO.setStandardStepId(standardStepId);
            allStepList.add(productRoutingStepDTO);
        }

        if (CollectionUtils.isNotEmpty(insertProductRoutingDTOs)) {
            // 根据routingSequenceId去重
            insertProductRoutingDTOs = insertProductRoutingDTOs.stream().collect(Collectors
                    .collectingAndThen(Collectors.toMap(ProductRoutingDTO::getRoutingSequenceId,
                                    Function.identity(), (v1, v2) -> v1),
                            map -> new ArrayList<>(map.values())));
            this.doCreateBatchWithPartition(insertProductRoutingDTOs);
            log.info("新增产品工艺路径头数据条数:{}", insertProductRoutingDTOs.size());
        }

        if (CollectionUtils.isNotEmpty(updateProductRoutingDTOs)) {
            // 根据routingSequenceId去重
            updateProductRoutingDTOs = updateProductRoutingDTOs.stream().collect(Collectors
                    .collectingAndThen(Collectors.toMap(ProductRoutingDTO::getRoutingSequenceId,
                                    Function.identity(), (v1, v2) -> v1),
                            map -> new ArrayList<>(map.values())));
            this.doUpdateBatchWithPartition(updateProductRoutingDTOs);
            log.info("更新产品工艺路径头数据条数:{}", updateProductRoutingDTOs.size());
        }

        List<ProductRoutingDTO> allList = new ArrayList<>();
        allList.addAll(insertProductRoutingDTOs);
        allList.addAll(updateProductRoutingDTOs);

        Map<String, List<ProductRoutingStepDTO>> stepMap = allStepList.stream().collect(Collectors
                .groupingBy(ProductRoutingStepDTO::getRoutingId));
        for (ProductRoutingDTO productRoutingDTO : allList) {
            if (stepMap.containsKey(productRoutingDTO.getRoutingSequenceId())) {
                List<ProductRoutingStepDTO> productRoutingStepDTOS = stepMap.get(productRoutingDTO.getRoutingSequenceId());
                productRoutingStepDTOS.forEach(t -> t.setRoutingId(productRoutingDTO.getId()));
            }
        }
        insertRoutingStepDTOs = allStepList.stream().filter(t ->
                Objects.isNull(t.getId())).collect(Collectors.toList());
        updateRoutingStepDTOs = allStepList.stream().filter(t ->
                Objects.nonNull(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertRoutingStepDTOs)) {
            productRoutingStepService.doCreateBatchWithPartition(insertRoutingStepDTOs);
            log.info("新增产品工艺路径行数据条数:{}", insertRoutingStepDTOs.size());
        }

        if (CollectionUtils.isNotEmpty(updateRoutingStepDTOs)) {
            productRoutingStepService.doUpdateBatchWithPartition(updateRoutingStepDTOs);
            log.info("更新产品工艺路径行数据条数:{}", updateRoutingStepDTOs.size());
        }

        List<ProductRoutingVO> newRoutingVOList = this.selectByRoutingSequenceIds(routingSequenceIds);
        List<String> routingIds = newRoutingVOList.stream().map(ProductRoutingVO::getId)
                .distinct().collect(Collectors.toList());
        List<ProductRoutingStepVO> newRoutingStepVOList = productRoutingStepService.selectByRoutingIds(routingIds);

        Map<String, List<ProductRoutingStepVO>> newRoutingStepVOMap = CollectionUtils.isEmpty(newRoutingStepVOList)
                ? new HashMap<>() :
                newRoutingStepVOList.stream().collect(Collectors.groupingBy(ProductRoutingStepVO::getRoutingId));

        List<ProductRoutingStepDTO> updateNewRoutingStepDTOs = Lists.newArrayList();
        for (ProductRoutingVO productRoutingVO : newRoutingVOList) {
            String dataKey = productRoutingVO.getId();
            if (newRoutingStepVOMap.containsKey(dataKey)) {
                log.info("dataKey:{}, 匹配到有效的产品工艺路径行数据", dataKey);
                List<ProductRoutingStepVO> oldRoutingStepVOList = newRoutingStepVOMap.get(dataKey);
                List<Integer> operations = oldRoutingStepVOList.stream().filter(t ->
                                Objects.equals(t.getEnabled(), YesOrNoEnum.YES.getCode()))
                        .map(ProductRoutingStepVO::getSequenceNo).sorted(Comparator
                                .comparing(Integer::valueOf)).collect(Collectors.toList());
                oldRoutingStepVOList.forEach(t -> {
                    Integer previousOperation = findPreviousOperation(operations, t.getSequenceNo());
                    Integer nextOperation = findNextOperation(operations, t.getSequenceNo());
                    log.info("前工序：{}，后工序：{}", previousOperation, nextOperation);
                    ProductRoutingStepDTO newRoutingStepDTO = instance.vo2Dto(t);
                    newRoutingStepDTO.setPreRoutingStepSequenceNo(Objects.isNull(previousOperation)
                            ? null : String.valueOf(previousOperation));
                    newRoutingStepDTO.setNextRoutingStepSequenceNo(Objects.isNull(nextOperation)
                            ? null : String.valueOf(nextOperation));
                    updateNewRoutingStepDTOs.add(newRoutingStepDTO);
                });
            }
        }

        if (CollectionUtils.isNotEmpty(updateNewRoutingStepDTOs)) {
            productRoutingStepService.doUpdateBatchWithPartition(updateNewRoutingStepDTOs);
            log.info("第二次更新产品工艺路径步骤数据条数:{}", updateNewRoutingStepDTOs.size());
        }

        stopWatch.stop();

        // 统计处理结果
        int totalInsertRouting = CollectionUtils.isEmpty(insertProductRoutingDTOs) ? 0 : insertProductRoutingDTOs.size();
        int totalUpdateRouting = CollectionUtils.isEmpty(updateProductRoutingDTOs) ? 0 : updateProductRoutingDTOs.size();
        int totalInsertStep = CollectionUtils.isEmpty(insertRoutingStepDTOs) ? 0 : insertRoutingStepDTOs.size();
        int totalUpdateStep = CollectionUtils.isEmpty(updateRoutingStepDTOs) ? 0 : updateRoutingStepDTOs.size();

        log.info("=== 工艺路径数据同步完成统计 ===");
        log.info("输入数据总数: {}", list.size());
        log.info("新增工艺路径: {}", totalInsertRouting);
        log.info("更新工艺路径: {}", totalUpdateRouting);
        log.info("新增工艺路径步骤: {}", totalInsertStep);
        log.info("更新工艺路径步骤: {}", totalUpdateStep);
        log.info("处理耗时: {}", stopWatch.getTotalTimeMillis() + "ms");
        log.info("=== 统计结束 ===");

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> syncOpYield(List<MesOpYield> yields) {
        if (CollectionUtils.isEmpty(yields)) {
            return BaseResponse.success();
        }

        List<String> productCodes = yields.stream().map(MesOpYield::getItemCode).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPoints = newProductStockPointService.selectByProductCode(productCodes);

        Map<String, NewProductStockPointVO> productStockPointVOMap = CollectionUtils.isEmpty(newProductStockPoints)
                ? new HashMap<>() : newProductStockPoints.stream().collect(Collectors
                .toMap(t -> t.getStockPointCode() + "|" + t.getProductCode(),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, Object> params = ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> stockPoints = newStockPointService.selectByParams(params);
        Map<String, NewStockPointVO> stockMap = stockPoints.stream().collect(Collectors
                .toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));

        List<String> productIds = newProductStockPoints.stream().map(NewProductStockPointVO::getId)
                .collect(Collectors.toList());
        List<ProductRoutingVO> productRoutingVOS = this.selectByProductIds(productIds);

        List<String> routingIds = productRoutingVOS.stream().map(ProductRoutingVO::getId).collect(Collectors.toList());
        List<ProductRoutingStepVO> existRoutingStepList = productRoutingStepService.selectByRoutingIds(routingIds);

        Map<String, ProductRoutingVO> existRoutingVOMap = CollectionUtils.isEmpty(productRoutingVOS)
                ? new HashMap<>() : productRoutingVOS.stream().collect(Collectors.toMap(t ->
                t.getProductId() + "|" + t.getStockPointId(), Function.identity(), (v1, v2) -> v1));

        Map<String, List<ProductRoutingStepVO>> existRoutingStepVOMap = CollectionUtils.isEmpty(existRoutingStepList)
                ? new HashMap<>() : existRoutingStepList.stream().collect(Collectors
                .groupingBy(ProductRoutingStepVO::getRoutingId));

        List<ProductRoutingStepDTO> updateRoutingStepDTOs = Lists.newArrayList();

        for (MesOpYield mesOpYield : yields) {
            String productCode = mesOpYield.getItemCode();
            String stockPointCode = mesOpYield.getPlantCode();
            String productDataKey = productCode + "|" + stockPointCode;
            if (Objects.isNull(productStockPointVOMap.get(productDataKey))) {
                log.error("没有找到对应的物料信息，接口物料编码：{}，接口库存点：{}", productCode, stockPointCode);
                continue;
            }
            if (Objects.isNull(stockMap.get(stockPointCode))) {
                log.info("{}没有找到对应的库存点，库存点：{}", productCode, stockPointCode);
                continue;
            }
            String stockPointId = stockMap.get(stockPointCode).getId();
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(productDataKey);
            String productId = newProductStockPointVO.getId();
            String operationSequenceNum = mesOpYield.getOpProcess();
            String opYield = mesOpYield.getOpYield();
            String routingDataKey = productId + "|" + stockPointId;
            if (Objects.isNull(existRoutingVOMap.get(routingDataKey))) {
                log.error("routingDataKey:{}没有找到对应的产品工艺路径：", routingDataKey);
                continue;
            }
            ProductRoutingVO productRoutingVO = existRoutingVOMap.get(routingDataKey);
            if (Objects.isNull(existRoutingStepVOMap.get(productRoutingVO.getId()))) {
                log.error("RoutingSequenceId:{}没有找到对应的产品工艺路径步骤", productRoutingVO.getRoutingSequenceId());
                continue;
            }
            List<ProductRoutingStepVO> routingStepVO = existRoutingStepVOMap.get(productRoutingVO.getId());
            routingStepVO.stream().filter(t ->
                            Objects.equals(t.getSequenceNo() + "", operationSequenceNum)).findFirst()
                    .ifPresent(t -> {
                        ProductRoutingStepDTO productRoutingStepDTO=ProductRoutingStepConvertor.INSTANCE.vo2Dto(t);
                        productRoutingStepDTO.setYield(new BigDecimal(opYield));
                        updateRoutingStepDTOs.add(productRoutingStepDTO);
                    });
        }
        if (CollectionUtils.isNotEmpty(updateRoutingStepDTOs)) {
            productRoutingStepService.doUpdateBatchWithPartition(updateRoutingStepDTOs);
        }
        log.info("更新产品工艺路径步骤数据条数:{}", updateRoutingStepDTOs.size());
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleSyncOpYield(String tenantId) {
        Map<String, Object> newStockPoingMap = new HashMap<>(2);
        newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.OP_YIELD.getCode(), newStockPoingMap);
        return BaseResponse.success("同步成功");
    }

    @Override
    public void doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductRoutingVersionList) {
        productRoutingDao.doLogicDeleteBatch(deleteProductRoutingVersionList);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<ProductRoutingVO> invocation(List<ProductRoutingVO> dataList, Map<String, Object> params,
                                             String invocation) {
        return dataList;
    }

    private Integer findPreviousOperation(List<Integer> operations, Integer currentOperation) {
        // 找到当前工序的索引
        int currentIndex = operations.indexOf(currentOperation);
        // 如果是第一个工序或者没找到当前工序，返回null
        if (currentIndex <= 0) {
            log.info("当前工序为空或者没找到当前工序，返回null，当前工序：{}", currentOperation);
            return null;
        }
        // 返回前一个工序
        return operations.get(currentIndex - 1);
    }

    private Integer findNextOperation(List<Integer> operations, Integer currentOperation) {
        // 找到当前工序的索引
        int currentIndex = operations.indexOf(currentOperation);
        // 如果是最后一个工序或者没找到当前工序，返回null
        if (currentIndex == -1 || currentIndex >= operations.size() - 1) {
            log.info("当前工序为空或者没找到当前工序，返回null，当前工序：{}", currentOperation);
            return null;
        }
        // 返回后一个工序
        return operations.get(currentIndex + 1);
    }

    @Override
    public List<ProductRoutingVO> selectVOByParams(Map<String, Object> params) {
        return productRoutingDao.selectVOByParams(params);
    }

    @Override
    public void syncAllRouting() {
        String scenario = SystemHolder.getScenario();
        // 1.工艺路径，工艺路径步骤转化处理
        StopWatch stopWatch = new StopWatch("工艺路径同步");
        stopWatch.start("路径、路径步骤转化处理");
        newRoutingService.doTransitionRouting(null, scenario);
        stopWatch.stop();

        // 2.输入物品
        stopWatch.start("输入物品处理");
        List<NewStockPointVO> stockPoints = newStockPointService.selectAll();
        newRoutingStepInputService.doTransitionRoutingStepInput(null, null,
                null, stockPoints, scenario);
        stopWatch.stop();

        // 3.候选资源
        stopWatch.start("候选资源处理");
        newRoutingStepResourceService.doTransitionRoutingStepResource(null, null,
                null, stockPoints, scenario);
        stopWatch.stop();

        // 4.维护第一道工序的输入物品，输出物品
        stopWatch.start("第一道工序的输入物品，输出物品处理");
        newRoutingStepService.doCreatFirstStepInputAndOut();
        stopWatch.stop();

        // 5.处理已经过期失效的工艺路径步骤输入物品
        stopWatch.start("处理已经过期失效的工艺路径步骤输入物品");
        int updateEnableForExpiryTime = newRoutingStepInputService.updateEnableForExpiryTime();
        if (updateEnableForExpiryTime > 0) {
            // 6.维护第一道工序的输入物品，输出物品
            newRoutingStepService.doCreatFirstStepInputAndOut();
        }
        stopWatch.stop();

        // 工艺路径，工艺路径步骤处理过期数据
        stopWatch.start("工艺路径，工艺路径步骤处理过期数据");
        newRoutingService.updateEnableForExpiryTime();
        newRoutingStepService.updateEnableForExpiryTime();
        stopWatch.stop();

        // 7.若存在S2的10工序没有候选资源则默认维护S2XL01资源
        // TODO 默认逻辑待完善
        stopWatch.start("若存在S2的10工序没有候选资源则默认维护S2XL01资源");
        newRoutingStepService.doAddDefaultResource();
        stopWatch.stop();

        // 8.根据采购类别判断工艺路径是否有效
        //
        stopWatch.start("根据采购类别判断工艺路径是否有效");
        newRoutingService.checkEnableFlagByPoCategory(scenario);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    @Override
    public List<ProductRoutingVO> selectByRoutingSequenceIdNotNull() {
        return productRoutingDao.selectByRoutingSequenceIdNotNull();
    }
    /**
     * 格式化打印工艺路径头信息
     *
     * @param operation 操作类型 (INSERT/UPDATE)
     * @param routingDTO 工艺路径DTO
     * @param erpRouting ERP工艺路径数据
     * @param stockPointCode 库存点编码
     */
    private void logRoutingHeaderInfo(String operation, ProductRoutingDTO routingDTO,
                                      ErpProductRouting erpRouting, String stockPointCode) {
        log.info("=== 工艺路径头 {} 信息 ===", operation);
        log.info("  ID: {}", routingDTO.getId());
        log.info("  工艺路径编码(routingCode): {}", routingDTO.getRoutingCode());
        log.info("  工艺路径名称(routingName): {}", routingDTO.getRoutingName());
        log.info("  工艺路径序列ID(routingSequenceId): {}", routingDTO.getRoutingSequenceId());
        log.info("  库存点编码(stockPointCode): {}", stockPointCode);
        log.info("  产品ID(productId): {}", routingDTO.getProductId());
        log.info("  库存点ID(stockPointId): {}", routingDTO.getStockPointId());
        log.info("  启用状态(enabled): {}", routingDTO.getEnabled());
        log.info("  有效状态(effective): {}", routingDTO.getEffective());
        log.info("  优先级(priority): {}", routingDTO.getPriority());
        log.info("  生产版本(productionVersion): {}", routingDTO.getProductionVersion());
        log.info("  批量大小(lotSize): {}", routingDTO.getLotSize());
        log.info("  最后更新日期(lastUpdateDate): {}", routingDTO.getLastUpdateDate());
        log.info("  ERP物料状态(itemStatus): {}", erpRouting.getItemStatus());
        log.info("=== 工艺路径头信息结束 ===");
    }

    /**
     * 格式化打印工艺路径步骤信息
     *
     * @param operation 操作类型 (INSERT/UPDATE)
     * @param stepDTO 工艺路径步骤DTO
     * @param erpRouting ERP工艺路径数据
     * @param stockPointCode 库存点编码
     */
    private void logRoutingStepInfo(String operation, ProductRoutingStepDTO stepDTO,
                                    ErpProductRouting erpRouting, String stockPointCode) {
        log.info("=== 工艺路径步骤 {} 信息 ===", operation);
        log.info("  步骤ID: {}", stepDTO.getId());
        log.info("  工艺路径ID(routingId): {}", stepDTO.getRoutingId());
        log.info("  操作序列ID(operationSequenceId): {}", stepDTO.getOperationSequenceId());
        log.info("  工序序号(sequenceNo): {}", stepDTO.getSequenceNo());
        log.info("  库存点编码(stockPointCode): {}", stockPointCode);
        log.info("  标准步骤ID(standardStepId): {}", stepDTO.getStandardStepId());
        log.info("  启用状态(enabled): {}", stepDTO.getEnabled());
        log.info("  有效状态(effective): {}", stepDTO.getEffective());
        log.info("  产出率(yield): {}", stepDTO.getYield());
        log.info("  开始日期(startDate): {}", stepDTO.getStartDate());
        log.info("  失效日期(disableDate): {}", stepDTO.getDisableDate());
        log.info("  最后更新日期(lastUpdateDate): {}", stepDTO.getLastUpdateDate());
        log.info("  ERP工序序号(operationSeqNum): {}", erpRouting.getOperationSeqNum());
        log.info("=== 工艺路径步骤信息结束 ===");
    }

}
