package com.yhl.scp.mds.bom.infrastructure.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BasePO;

public class ProductBomPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 270589799959040601L;

    /**
     * BOM版本ID
     */
    private String bomVersionId;
    /**
     * 行号
     */
    private Integer rowNum;
    /**
     * 输入输出产品系列ID
     */
    private Integer ioProductSeriesId;
    /**
     * 标准工艺ID
     */
    private String standardStepId;
    /**
     * 输入输出库存点ID
     */
    private String ioStockPointId;
    /**
     * 输入输出物品ID
     */
    private String ioProductId;
    /**
     * 输入输出类型
     */
    private String ioType;
    /**
     * 是否主要物料
     */
    private String mainMaterial;
    /**
     * 成品率
     */
    private BigDecimal yield;
    /**
     * 损耗策略
     */
    private String scrapStrategy;
    /**
     * 百分比损耗率
     */
    private BigDecimal percentageScrapRate;
    /**
     * 固定损耗数
     */
    private BigDecimal scrap;
    /**
     * 单位输入输出量
     */
    private BigDecimal ioFactor;
    /**
     * 单位生产成本
     */
    private BigDecimal unitProductionCost;
    /**
     * 单位直接人工成本
     */
    private BigDecimal unitLaborCost;
    /**
     * 单位直接能耗成本
     */
    private BigDecimal unitEnergyCost;
    /**
     * 消耗明细分配方式id
     */
    private String productBomAllocationId;
    /**
     * 替代方式
     */
    private String altMode;
    /**
     * 替代比例
     */
    private BigDecimal altRatio;
    /**
     * 替代料组号
     */
    private String altMaterialGroup;
    /**
     * 成套使用号
     */
    private String matchCode;
    /**
     * 接续任务
     */
    private String connectionTask;
    /**
     * 接续方式
     */
    private String connectionType;
    /**
     * 最大接续时间
     */
    private Integer maxConnectionDuration;
    /**
     * 最小接续时间
     */
    private Integer minConnectionDuration;
    /**
     * 计量单位ID
     */
    private String countingUnitId;
    /**
     * 行id
     */
    private String componentSequenceId;
    /**
     * 供应方式
     */
    private String wipSupplyType;
    
    /**
     * Bom版本物品id
     */
    private String versionProductId;
    /**
     * 生效时间
     */
    private Date startTime;
    /**
     * 失效时间
     */
    private Date endTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 输入输出物品编码
     */
    private String ioProductSyncCode;

    public String getBomVersionId() {
        return bomVersionId;
    }

    public void setBomVersionId(String bomVersionId) {
        this.bomVersionId = bomVersionId;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public Integer getIoProductSeriesId() {
        return ioProductSeriesId;
    }

    public void setIoProductSeriesId(Integer ioProductSeriesId) {
        this.ioProductSeriesId = ioProductSeriesId;
    }

    public String getStandardStepId() {
        return standardStepId;
    }

    public void setStandardStepId(String standardStepId) {
        this.standardStepId = standardStepId;
    }

    public String getIoStockPointId() {
        return ioStockPointId;
    }

    public void setIoStockPointId(String ioStockPointId) {
        this.ioStockPointId = ioStockPointId;
    }

    public String getIoProductId() {
        return ioProductId;
    }

    public void setIoProductId(String ioProductId) {
        this.ioProductId = ioProductId;
    }

    public String getIoType() {
        return ioType;
    }

    public void setIoType(String ioType) {
        this.ioType = ioType;
    }

    public String getMainMaterial() {
        return mainMaterial;
    }

    public void setMainMaterial(String mainMaterial) {
        this.mainMaterial = mainMaterial;
    }

    public BigDecimal getYield() {
        return yield;
    }

    public void setYield(BigDecimal yield) {
        this.yield = yield;
    }

    public String getScrapStrategy() {
        return scrapStrategy;
    }

    public void setScrapStrategy(String scrapStrategy) {
        this.scrapStrategy = scrapStrategy;
    }

    public BigDecimal getPercentageScrapRate() {
        return percentageScrapRate;
    }

    public void setPercentageScrapRate(BigDecimal percentageScrapRate) {
        this.percentageScrapRate = percentageScrapRate;
    }

    public BigDecimal getScrap() {
        return scrap;
    }

    public void setScrap(BigDecimal scrap) {
        this.scrap = scrap;
    }

    public BigDecimal getIoFactor() {
        return ioFactor;
    }

    public void setIoFactor(BigDecimal ioFactor) {
        this.ioFactor = ioFactor;
    }

    public BigDecimal getUnitProductionCost() {
        return unitProductionCost;
    }

    public void setUnitProductionCost(BigDecimal unitProductionCost) {
        this.unitProductionCost = unitProductionCost;
    }

    public BigDecimal getUnitLaborCost() {
        return unitLaborCost;
    }

    public void setUnitLaborCost(BigDecimal unitLaborCost) {
        this.unitLaborCost = unitLaborCost;
    }

    public BigDecimal getUnitEnergyCost() {
        return unitEnergyCost;
    }

    public void setUnitEnergyCost(BigDecimal unitEnergyCost) {
        this.unitEnergyCost = unitEnergyCost;
    }

    public String getProductBomAllocationId() {
        return productBomAllocationId;
    }

    public void setProductBomAllocationId(String productBomAllocationId) {
        this.productBomAllocationId = productBomAllocationId;
    }

    public String getAltMode() {
        return altMode;
    }

    public void setAltMode(String altMode) {
        this.altMode = altMode;
    }

    public BigDecimal getAltRatio() {
        return altRatio;
    }

    public void setAltRatio(BigDecimal altRatio) {
        this.altRatio = altRatio;
    }

    public String getAltMaterialGroup() {
        return altMaterialGroup;
    }

    public void setAltMaterialGroup(String altMaterialGroup) {
        this.altMaterialGroup = altMaterialGroup;
    }

    public String getMatchCode() {
        return matchCode;
    }

    public void setMatchCode(String matchCode) {
        this.matchCode = matchCode;
    }

    public String getConnectionTask() {
        return connectionTask;
    }

    public void setConnectionTask(String connectionTask) {
        this.connectionTask = connectionTask;
    }

    public String getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(String connectionType) {
        this.connectionType = connectionType;
    }

    public Integer getMaxConnectionDuration() {
        return maxConnectionDuration;
    }

    public void setMaxConnectionDuration(Integer maxConnectionDuration) {
        this.maxConnectionDuration = maxConnectionDuration;
    }

    public Integer getMinConnectionDuration() {
        return minConnectionDuration;
    }

    public void setMinConnectionDuration(Integer minConnectionDuration) {
        this.minConnectionDuration = minConnectionDuration;
    }

    public String getCountingUnitId() {
        return countingUnitId;
    }

    public void setCountingUnitId(String countingUnitId) {
        this.countingUnitId = countingUnitId;
    }

    public String getComponentSequenceId() {
        return componentSequenceId;
    }

    public void setComponentSequenceId(String componentSequenceId) {
        this.componentSequenceId = componentSequenceId;
    }

    public String getWipSupplyType() {
        return wipSupplyType;
    }

    public void setWipSupplyType(String wipSupplyType) {
        this.wipSupplyType = wipSupplyType;
    }

	public String getVersionProductId() {
		return versionProductId;
	}

	public void setVersionProductId(String versionProductId) {
		this.versionProductId = versionProductId;
	}
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getIoProductSyncCode() {
        return ioProductSyncCode;
    }

    public void setIoProductSyncCode(String ioProductSyncCode) {
        this.ioProductSyncCode = ioProductSyncCode;
    }
}
