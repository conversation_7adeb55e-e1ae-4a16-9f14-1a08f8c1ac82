package com.yhl.scp.mds.curingTime.convertor;

import com.yhl.scp.mds.curingTime.domain.entity.MdsFinishedProductDeliveryDO;
import com.yhl.scp.mds.curingTime.dto.MdsFinishedProductDeliveryDTO;
import com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MdsFinishedProductDeliveryConvertor</code>
 * <p>
 * mes系统成品发送属性接口同步中间表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 10:46:25
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MdsFinishedProductDeliveryConvertor {

    MdsFinishedProductDeliveryConvertor INSTANCE = Mappers.getMapper(MdsFinishedProductDeliveryConvertor.class);

    MdsFinishedProductDeliveryDO dto2Do(MdsFinishedProductDeliveryDTO obj);

    MdsFinishedProductDeliveryDTO do2Dto(MdsFinishedProductDeliveryDO obj);

    List<MdsFinishedProductDeliveryDO> dto2Dos(List<MdsFinishedProductDeliveryDTO> list);

    List<MdsFinishedProductDeliveryDTO> do2Dtos(List<MdsFinishedProductDeliveryDO> list);

    MdsFinishedProductDeliveryVO do2Vo(MdsFinishedProductDeliveryDO obj);

    MdsFinishedProductDeliveryVO po2Vo(MdsFinishedProductDeliveryPO obj);

    List<MdsFinishedProductDeliveryVO> po2Vos(List<MdsFinishedProductDeliveryPO> list);

    MdsFinishedProductDeliveryPO do2Po(MdsFinishedProductDeliveryDO obj);

    MdsFinishedProductDeliveryDO po2Do(MdsFinishedProductDeliveryPO obj);

    MdsFinishedProductDeliveryPO dto2Po(MdsFinishedProductDeliveryDTO obj);

    List<MdsFinishedProductDeliveryPO> dto2Pos(List<MdsFinishedProductDeliveryDTO> obj);
    MdsFinishedProductDeliveryDTO po2Dto(MdsFinishedProductDeliveryPO obj);

}
