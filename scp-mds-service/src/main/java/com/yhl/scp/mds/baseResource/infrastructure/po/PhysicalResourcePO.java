package com.yhl.scp.mds.baseResource.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PhysicalResourcePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -15559482366496393L;

    /**
     * 标准资源ID
     */
    private String standardResourceId;
    /**
     * 物理资源代码
     */
    private String physicalResourceCode;
    /**
     * 物理资源名称
     */
    private String physicalResourceName;
    /**
     * 资源类别
     */
    private String resourceCategory;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 资源门类
     */
    private String resourceClassification;
    /**
     * 子任务类型
     */
    private String subtaskType;
    /**
     * 堆叠限制类型
     */
    private String assignQuantityType;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 是否瓶颈资源
     */
    private String bottleneck;
    /**
     * 是否无限能力
     */
    private String infiniteCapacity;
    /**
     * 工序代码
     */
    private String sequenceCode;
    /**
     * 是否可变工时
     */
    private String variableWorkHours;
    /**
     * 资源量系数
     */
    private String resourceQuantityCoefficient;
    /**
     * 计量单位ID
     */
    private String countingUnitId;
    /**
     * 制造效率
     */
    private BigDecimal productionEfficiency;
    /**
     * 设置效率
     */
    private BigDecimal setupEfficiency;
    /**
     * 清洗效率
     */
    private BigDecimal cleanupEfficiency;
    /**
     * 设置时间
     */
    private Integer setupDuration;
    /**
     * 清洗时间
     */
    private Integer cleanupDuration;
    /**
     * 前缓冲时间
     */
    private Integer bufferTimeBefore;
    /**
     * 后缓冲时间
     */
    private Integer bufferTimeAfter;
    /**
     * 制造最大中断时间
     */
    private Integer maxProductionSuspendDuration;
    /**
     * 设置最大中断时间
     */
    private Integer maxSetupSuspendDuration;
    /**
     * 清理最大中断时间
     */
    private Integer maxCleanupSuspendDuration;
    /**
     * 生产线
     */
    private String productionLine;
    /**
     * 是否严格遵守生产线约束
     */
    private String strictProductionLineConstraints;
    private String noBufferActionType;
    /**
     * 资源锁定时间
     */
    private Integer noBufferActionDuration;
    /**
     * 单位制造批量
     */
    private Integer lotSize;
    /**
     * 最大制造批量
     */
    private Integer maxLotSize;
    /**
     * 制造时刻尾数调整单位
     */
    private String productionDateLastNumChangeUnit;
    /**
     * 制造时间尾数调整单位
     */
    private String productionTimeLastNumChangeUnit;
    /**
     * 制造时间取值方式
     */
    private String productionDurationLogic;
    /**
     * 设置和清洗时间取值方式
     */
    private String setupAndCleanupDurationLogic;
    /**
     * 动态设置和清洗时间取值方式
     */
    private String dynamicSetupAndCleanupDurationLogic;
    /**
     * 切换间取值切换
     */
    private String changeoverDurationLogic;
    /**
     * 动态切换时间取值方式
     */
    private String dynamicChangeoverDurationLogic;
    /**
     * 生效时间
     */
    private Date effectiveTime;
    /**
     * 失效时间
     */
    private Date expiryTime;

    public String getStandardResourceId() {
        return standardResourceId;
    }

    public void setStandardResourceId(String standardResourceId) {
        this.standardResourceId = standardResourceId;
    }

    public String getPhysicalResourceCode() {
        return physicalResourceCode;
    }

    public void setPhysicalResourceCode(String physicalResourceCode) {
        this.physicalResourceCode = physicalResourceCode;
    }

    public String getPhysicalResourceName() {
        return physicalResourceName;
    }

    public void setPhysicalResourceName(String physicalResourceName) {
        this.physicalResourceName = physicalResourceName;
    }

    public String getResourceCategory() {
        return resourceCategory;
    }

    public void setResourceCategory(String resourceCategory) {
        this.resourceCategory = resourceCategory;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceClassification() {
        return resourceClassification;
    }

    public void setResourceClassification(String resourceClassification) {
        this.resourceClassification = resourceClassification;
    }

    public String getSubtaskType() {
        return subtaskType;
    }

    public void setSubtaskType(String subtaskType) {
        this.subtaskType = subtaskType;
    }

    public String getAssignQuantityType() {
        return assignQuantityType;
    }

    public void setAssignQuantityType(String assignQuantityType) {
        this.assignQuantityType = assignQuantityType;
    }

    public Integer getDisplayIndex() {
        return displayIndex;
    }

    public void setDisplayIndex(Integer displayIndex) {
        this.displayIndex = displayIndex;
    }

    public String getBottleneck() {
        return bottleneck;
    }

    public void setBottleneck(String bottleneck) {
        this.bottleneck = bottleneck;
    }

    public String getInfiniteCapacity() {
        return infiniteCapacity;
    }

    public void setInfiniteCapacity(String infiniteCapacity) {
        this.infiniteCapacity = infiniteCapacity;
    }

    public String getSequenceCode() {
        return sequenceCode;
    }

    public void setSequenceCode(String sequenceCode) {
        this.sequenceCode = sequenceCode;
    }

    public String getVariableWorkHours() {
        return variableWorkHours;
    }

    public void setVariableWorkHours(String variableWorkHours) {
        this.variableWorkHours = variableWorkHours;
    }

    public String getResourceQuantityCoefficient() {
        return resourceQuantityCoefficient;
    }

    public void setResourceQuantityCoefficient(String resourceQuantityCoefficient) {
        this.resourceQuantityCoefficient = resourceQuantityCoefficient;
    }

    public String getCountingUnitId() {
        return countingUnitId;
    }

    public void setCountingUnitId(String countingUnitId) {
        this.countingUnitId = countingUnitId;
    }

    public BigDecimal getProductionEfficiency() {
        return productionEfficiency;
    }

    public void setProductionEfficiency(BigDecimal productionEfficiency) {
        this.productionEfficiency = productionEfficiency;
    }

    public BigDecimal getSetupEfficiency() {
        return setupEfficiency;
    }

    public void setSetupEfficiency(BigDecimal setupEfficiency) {
        this.setupEfficiency = setupEfficiency;
    }

    public BigDecimal getCleanupEfficiency() {
        return cleanupEfficiency;
    }

    public void setCleanupEfficiency(BigDecimal cleanupEfficiency) {
        this.cleanupEfficiency = cleanupEfficiency;
    }

    public Integer getSetupDuration() {
        return setupDuration;
    }

    public void setSetupDuration(Integer setupDuration) {
        this.setupDuration = setupDuration;
    }

    public Integer getCleanupDuration() {
        return cleanupDuration;
    }

    public void setCleanupDuration(Integer cleanupDuration) {
        this.cleanupDuration = cleanupDuration;
    }

    public Integer getBufferTimeBefore() {
        return bufferTimeBefore;
    }

    public void setBufferTimeBefore(Integer bufferTimeBefore) {
        this.bufferTimeBefore = bufferTimeBefore;
    }

    public Integer getBufferTimeAfter() {
        return bufferTimeAfter;
    }

    public void setBufferTimeAfter(Integer bufferTimeAfter) {
        this.bufferTimeAfter = bufferTimeAfter;
    }

    public Integer getMaxProductionSuspendDuration() {
        return maxProductionSuspendDuration;
    }

    public void setMaxProductionSuspendDuration(Integer maxProductionSuspendDuration) {
        this.maxProductionSuspendDuration = maxProductionSuspendDuration;
    }

    public Integer getMaxSetupSuspendDuration() {
        return maxSetupSuspendDuration;
    }

    public void setMaxSetupSuspendDuration(Integer maxSetupSuspendDuration) {
        this.maxSetupSuspendDuration = maxSetupSuspendDuration;
    }

    public Integer getMaxCleanupSuspendDuration() {
        return maxCleanupSuspendDuration;
    }

    public void setMaxCleanupSuspendDuration(Integer maxCleanupSuspendDuration) {
        this.maxCleanupSuspendDuration = maxCleanupSuspendDuration;
    }

    public String getProductionLine() {
        return productionLine;
    }

    public void setProductionLine(String productionLine) {
        this.productionLine = productionLine;
    }

    public String getStrictProductionLineConstraints() {
        return strictProductionLineConstraints;
    }

    public void setStrictProductionLineConstraints(String strictProductionLineConstraints) {
        this.strictProductionLineConstraints = strictProductionLineConstraints;
    }

    public String getNoBufferActionType() {
        return noBufferActionType;
    }

    public void setNoBufferActionType(String noBufferActionType) {
        this.noBufferActionType = noBufferActionType;
    }

    public Integer getNoBufferActionDuration() {
        return noBufferActionDuration;
    }

    public void setNoBufferActionDuration(Integer noBufferActionDuration) {
        this.noBufferActionDuration = noBufferActionDuration;
    }

    public Integer getLotSize() {
        return lotSize;
    }

    public void setLotSize(Integer lotSize) {
        this.lotSize = lotSize;
    }

    public Integer getMaxLotSize() {
        return maxLotSize;
    }

    public void setMaxLotSize(Integer maxLotSize) {
        this.maxLotSize = maxLotSize;
    }

    public String getProductionDateLastNumChangeUnit() {
        return productionDateLastNumChangeUnit;
    }

    public void setProductionDateLastNumChangeUnit(String productionDateLastNumChangeUnit) {
        this.productionDateLastNumChangeUnit = productionDateLastNumChangeUnit;
    }

    public String getProductionTimeLastNumChangeUnit() {
        return productionTimeLastNumChangeUnit;
    }

    public void setProductionTimeLastNumChangeUnit(String productionTimeLastNumChangeUnit) {
        this.productionTimeLastNumChangeUnit = productionTimeLastNumChangeUnit;
    }

    public String getProductionDurationLogic() {
        return productionDurationLogic;
    }

    public void setProductionDurationLogic(String productionDurationLogic) {
        this.productionDurationLogic = productionDurationLogic;
    }

    public String getSetupAndCleanupDurationLogic() {
        return setupAndCleanupDurationLogic;
    }

    public void setSetupAndCleanupDurationLogic(String setupAndCleanupDurationLogic) {
        this.setupAndCleanupDurationLogic = setupAndCleanupDurationLogic;
    }

    public String getDynamicSetupAndCleanupDurationLogic() {
        return dynamicSetupAndCleanupDurationLogic;
    }

    public void setDynamicSetupAndCleanupDurationLogic(String dynamicSetupAndCleanupDurationLogic) {
        this.dynamicSetupAndCleanupDurationLogic = dynamicSetupAndCleanupDurationLogic;
    }

    public String getChangeoverDurationLogic() {
        return changeoverDurationLogic;
    }

    public void setChangeoverDurationLogic(String changeoverDurationLogic) {
        this.changeoverDurationLogic = changeoverDurationLogic;
    }

    public String getDynamicChangeoverDurationLogic() {
        return dynamicChangeoverDurationLogic;
    }

    public void setDynamicChangeoverDurationLogic(String dynamicChangeoverDurationLogic) {
        this.dynamicChangeoverDurationLogic = dynamicChangeoverDurationLogic;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

}
