package com.yhl.scp.mds.customer.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>CustomerDO</code>
 * <p>
 * erp客户DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-02 19:12:00
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 216145705094996544L;

        /**
     * 主键ID
     */
        private String id;
        /**
     * erp客户id
     */
        private String customerId;
        /**
     * 客户编码
     */
        private String customerCode;
        /**
     * 客户名称
     */
        private String customerName;
        /**
     * 版本
     */
        private Integer versionValue;

}
