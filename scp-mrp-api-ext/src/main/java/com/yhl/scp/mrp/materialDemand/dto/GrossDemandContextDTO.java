package com.yhl.scp.mrp.materialDemand.dto;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;

/**
 * @ClassName GrossDemandContextDTO
 * @Description TODO
 * @Date 2025-04-02 14:05:39
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Data
public class GrossDemandContextDTO {

    private String scenario;

    private Date mrpCalcDate;
    // 浮法已发运

    // 最新已发布的产能版本
    private CapacityBalanceVersionVO capacityBalanceVersionVO;

    private Map<String, NewProductStockPointVO> productStockPointVOMapOfId = new HashMap<>();

    private Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = new HashMap<>();

    private Map<String, RoutingVO> routingVOMapOfProductCode = new HashMap<>();

    private Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = new HashMap<>();

    private Map<String, RoutingStepVO> routingStepMapOfId = new HashMap<>();

    private Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = new HashMap<>();

    private List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = new ArrayList<>();

    private Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>();

    // 中转库剩余库存，如果有库存存的是负数(小于0则有库存)
    private Map<String, BigDecimal> gapDemandQuantityMap;
}
