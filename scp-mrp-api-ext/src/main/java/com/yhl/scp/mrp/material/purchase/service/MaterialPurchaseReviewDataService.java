package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDataDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewDataService</code>
 * <p>
 * 材料采购评审应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21 22:27:22
 */
public interface MaterialPurchaseReviewDataService extends BaseService<MaterialPurchaseReviewDataDTO, MaterialPurchaseReviewDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseReviewDataVO}
     */
    List<MaterialPurchaseReviewDataVO> selectAll();

    BaseResponse<Void> calc(MaterialPurchaseReviewDTO dto);

    BaseResponse<Void> doUpdateSelective(MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO);

    BaseResponse<Void> release();

    void exportData(HttpServletResponse response, String versionId);

}
