package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>AllocationStatusEnum</code>
 * <p>
 * 替代类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-06 15:41:42
 */
public enum AlternativeTypeEnum implements CommonEnum {

    BACKLOG("BACKLOG", "积压"),

    MATERIAL_SHORTAGE("MATERIAL_SHORTAGE", "缺料");

    private String code;

    private String desc;

    AlternativeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
