package com.yhl.scp.mrp.material.arrival.service;

import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialInventoryAndDemandVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelForecastVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelHistoryVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseReviewVO;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewService</code>
 * <p>
 * 材料采购评审应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:15:11
 */
public interface MaterialPurchaseReviewService {

    List<MaterialInventoryAndDemandVO> selectTableInventoryAndDemand(MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO);

    List<MaterialModelHistoryVO> selectTableModelHistory(MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO);

    List<MaterialModelForecastVO> selectTableModelForecast(MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO);
}
