package com.yhl.scp.mrp.material.arrival.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewVO</code>
 * <p>
 * 材料采购评审VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:17:20
 */
@ApiModel(value = "材料采购评审VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPurchaseReviewVO implements Serializable {

    private static final long serialVersionUID = -2747323237243922192L;

    /**
     * 材料库存与需求
     */
    @ApiModelProperty(value = "材料库存与需求")
    @FieldInterpretation(value = "材料库存与需求")
    private List<MaterialInventoryAndDemandVO> materialInventoryAndDemandVOList;

    /**
     * 车型历史
     */
    @ApiModelProperty(value = "车型历史")
    @FieldInterpretation(value = "车型历史")
    private List<MaterialModelHistoryVO> materialModelHistoryVOList;

    /**
     * 车型预测
     */
    @ApiModelProperty(value = "车型预测")
    @FieldInterpretation(value = "车型预测")
    private List<MaterialModelForecastVO> materialModelForecastVOList;
}
