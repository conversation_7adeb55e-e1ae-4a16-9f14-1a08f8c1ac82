package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>MaterialPurchaseReviewFixedEnum</code>
 * <p>
 * 材料采购评审固定类型枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-16 16:59:58
 */
public enum MaterialPurchaseReviewFixedEnum implements CommonEnum {

    ACTUAL_CONSUMPTION("ACTUAL_CONSUMPTION", "实际消耗量（个）"),

    BEGINNING_INVENTORY("BEGINNING_INVENTORY", "期初库存"),

    IN_TRANSIT_ORDERS("IN_TRANSIT_ORDERS", "在途订单"),

    ARRIVAL_TIME("ARRIVAL_TIME", "到货时间"),

    INVENTORY_GAP("INVENTORY_GAP", "库存缺口"),

    PLANNED_PURCHASE_QUANTITY("PLANNED_PURCHASE_QUANTITY", "计划采购量"),

    PLANNED_ARRIVAL_TIME("PLANNED_ARRIVAL_TIME", "计划到货时间"),

    MATERIAL_FORECAST("MATERIAL_FORECAST", "材料预测量"),

    MATERIAL_CONSUME("MATERIAL_CONSUME", "材料消耗量"),

    VEHICLE_MODEL_CONSUME("VEHICLE_MODEL_CONSUME", "车型耗量"),

    CONSUME_DELIVERY_PROPORTION("CONSUME_DELIVERY_PROPORTION", "销量与发货比"),

    DELIVERY_FORECAST_PROPORTION("DELIVERY_FORECAST_PROPORTION", "发货与预测比");

    private String code;

    private String desc;

    MaterialPurchaseReviewFixedEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
