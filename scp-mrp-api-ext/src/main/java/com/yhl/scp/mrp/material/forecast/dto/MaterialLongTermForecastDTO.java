package com.yhl.scp.mrp.material.forecast.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialLongTermForecastDTO</code>
 * <p>
 * 材料长期预测DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:46:18
 */
@ApiModel(value = "材料长期预测DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialLongTermForecastDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 758368048062198017L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private Date demandDate;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal demandQuantity;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private String supplierId;
    /**
     * 要货模式
     */
    @ApiModelProperty(value = "要货模式")
    private String demandPattern;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 推移版本号
     */
    @ApiModelProperty(value = "推移版本号")
    private String inventoryShiftVersionCode;

}
