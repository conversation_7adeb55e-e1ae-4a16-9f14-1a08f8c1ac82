package com.yhl.scp.mrp.material.plan.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>MrpStockPointTypeEnum</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 16:18:53
 */
public enum MrpStockPointTypeEnum implements CommonEnum {

    BC("BC", "本厂仓库"),
    BCMT("BCMT", "本厂+码头"),
    MT("MT", "码头仓库"),
    FF("FF", "浮法仓库");

    private String code;

    private String desc;

    MrpStockPointTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
