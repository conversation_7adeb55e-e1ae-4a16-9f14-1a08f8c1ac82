package com.yhl.scp.mrp.material.arrival.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.scss.ScssInventoryOceanFreight;
import com.yhl.scp.mrp.material.arrival.dto.MaterialDeliveryNoteDTO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialDeliveryNoteService</code>
 * <p>
 * 材料送货单数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:08:46
 */
public interface MaterialDeliveryNoteService extends BaseService<MaterialDeliveryNoteDTO, MaterialDeliveryNoteVO> {

    void doUpdateBatchSelective(List<MaterialDeliveryNoteDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link MaterialDeliveryNoteVO}
     */
    List<MaterialDeliveryNoteVO> selectAll();

    /**
     * 定时任务处理送货单数据
     * @param moveMinute 
     */
	void doDisposePredictArrivalForJob(Integer moveMinute);
    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleMaterialDeliveryNote(List<MesMaterialDeliveryNote> mesMaterialDeliveryNote);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncMaterialDeliveryNote(String tenantId);

}
