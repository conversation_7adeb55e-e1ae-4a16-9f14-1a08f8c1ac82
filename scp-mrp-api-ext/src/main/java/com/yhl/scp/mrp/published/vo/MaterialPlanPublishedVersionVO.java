package com.yhl.scp.mrp.published.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPlanPublishedVersionVO</code>
 * <p>
 * 物料计划发布版本VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-19 17:03:18
 */
@ApiModel(value = "物料计划发布版本VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPlanPublishedVersionVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 621002314052169872L;

    /**
     * 发布版本号
     */
    @ApiModelProperty(value = "发布版本号")
    @FieldInterpretation(value = "发布版本号")
    private String versionCode;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    @FieldInterpretation(value = "材料类型")
    private String materialType;

    /**
     * 毛需求版本号
     */
    @ApiModelProperty(value = "毛需求版本号")
    @FieldInterpretation(value = "毛需求版本号")
    private String grossDemandVersionCode;


    @Override
    public void clean() {

    }

}
