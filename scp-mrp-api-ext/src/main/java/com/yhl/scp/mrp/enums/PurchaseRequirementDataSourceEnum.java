package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * @ClassName PurchaseRequirementDataSourceEnum
 * @Description TODO
 * @Date 2025-04-18 13:57:12
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
public enum PurchaseRequirementDataSourceEnum implements CommonEnum {

    PLAN_PURCHASE_ADDITION("PLAN_PURCHASE_ADDITION", "采购需求生成"),

    MANUAL_ADDITION("MANUAL_ADDITION", "手工新增");

    private String code;

    private String desc;

    PurchaseRequirementDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
