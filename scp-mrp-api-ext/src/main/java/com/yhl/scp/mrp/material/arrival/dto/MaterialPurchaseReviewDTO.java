package com.yhl.scp.mrp.material.arrival.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.enums.YesOrNoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewDTO</code>
 * <p>
 * 材料采购评审报表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:17:20
 */
@ApiModel(value = "材料采购评审DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPurchaseReviewDTO implements Serializable {

    private static final long serialVersionUID = -2747323237243922192L;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    @FieldInterpretation(value = "版本id")
    private String materialPurchaseReviewVersionId;

    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private List<String> productCodeList;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;

    /**
     * 指定年月
     */
    @ApiModelProperty(value = "指定年月")
    private String appointYearMonth;

    /**
     * 开始年月维度
     */
    @ApiModelProperty(value = "开始年月维度")
    private String startYearMonthDimension;

    /**
     * 结束年月维度
     */
    @ApiModelProperty(value = "结束年月维度")
    private String endYearMonthDimension;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandCategory;

    /**
     * 是/否为评审版本
     */
    @ApiModelProperty(value = "是/否为评审版本")
    private String reviewVersionFlag;
}
