package com.yhl.scp.mrp.material.plan.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <code>MaterialPlanNeedArrivedDTO</code>
 * <p>
 * 要货计划-下发到货DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-13 15:12:21
 */
@ApiModel(value = "要货计划-下发到货DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPlanNeedArrivedDTO implements Serializable {
    @JsonProperty("Service")
    private Service service;

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Service implements Serializable {
        @JsonProperty("Route")
        private Route route;

        @JsonProperty("Data")
        private Data data;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Route implements Serializable {
        @JsonProperty("SerialNO")
        private String serialNo;

        @JsonProperty("ServiceID")
        private String serviceId;

        @JsonProperty("SourceSysID")
        private String sourceSysId;

        @JsonProperty("ServiceTime")
        private String serviceTime;

        @JsonProperty("Soapenv")
        private String soapenv;

        @JsonProperty("ServiceResponse")
        private ServiceResponse serviceResponse;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceResponse implements Serializable {
        @JsonProperty("Status")
        private String status;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Data implements Serializable {
        @JsonProperty("Control")
        private Control control;

        @JsonProperty("Request")
        private Request request;

        @JsonProperty("Response")
        private Response response;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Control implements Serializable {
        @JsonProperty("PathVariable")
        private String pathVariable;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Request implements Serializable {
        @JsonProperty("list")
        private ListWrapper list;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListWrapper implements Serializable {
        @JsonProperty("PurchasingPlanLine")
        private List<PurchasingPlanLine> purchasingPlanLine;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchasingPlanLine implements Serializable {
        @JsonProperty("onroadQty")
        private String onroadQty;

        @JsonProperty("receivedQty")
        private String receivedQty;

        @JsonProperty("waitingQty")
        private String waitingQty;

        @JsonProperty("materialPlanNeedId")
        private String materialPlanNeedId;
    }

    @lombok.Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Response implements Serializable {
        @JsonProperty("UpdateLineQtyResult")
        private String updateLineQtyResult;
    }
}
