package com.yhl.scp.mrp.material.arrival.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <code>MaterialInventoryAndDemandVO</code>
 * <p>
 * 材料库存与需求VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:19:15
 */
@ApiModel(value = "材料库存与需求VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialInventoryAndDemandVO implements Serializable {

    private static final long serialVersionUID = 731393624673893750L;

    /**
     * 年月列
     */
    @ApiModelProperty(value = "年月列")
    @FieldInterpretation(value = "年月列")
    private List<String> yearMonthArrange;

    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;

    /**
     * 本厂名称
     */
    @ApiModelProperty(value = "本厂名称")
    @FieldInterpretation(value = "本厂名称")
    private String productName;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    @FieldInterpretation(value = "车型")
    private String vehicleModel;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;

    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;

    /**
     * 采购周期
     */
    @ApiModelProperty("采购周期")
    @FieldInterpretation(value = "采购周期")
    private BigDecimal purchaseLot;

    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
    @FieldInterpretation(value = "最小起订量")
    private BigDecimal minOrderQuantity;

    /**
     * 统计
     */
    @ApiModelProperty(value = "统计")
    @FieldInterpretation(value = "统计")
    private List<MaterialStatisticsVO> statisticsList;

    /**
     * 评审意见
     */
    @ApiModelProperty(value = "评审意见")
    @FieldInterpretation(value = "评审意见")
    private String reviewComments;

    /**
     * 批准
     */
    @ApiModelProperty(value = "批准")
    @FieldInterpretation(value = "批准")
    private String approve;

    /**
     * 审核
     */
    @ApiModelProperty(value = "审核")
    @FieldInterpretation(value = "审核")
    private String toExamine;

    /**
     * 制表
     */
    @ApiModelProperty(value = "制表")
    @FieldInterpretation(value = "制表")
    private String tabulation;
}
