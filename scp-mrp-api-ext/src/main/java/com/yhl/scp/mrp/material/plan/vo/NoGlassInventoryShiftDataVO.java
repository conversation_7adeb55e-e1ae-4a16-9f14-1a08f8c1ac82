package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>NoGlassInventoryShiftDataVO</code>
 * <p>
 * 非原片库存推移主表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:33
 */
@ApiModel(value = "非原片库存推移主表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NoGlassInventoryShiftDataVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -26562938095349623L;

    /**
     * 物料计划版本id
     */
    @ApiModelProperty(value = "物料计划版本id")
    @FieldInterpretation(value = "物料计划版本id")
    private String materialPlanVersionId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    @FieldInterpretation(value = "物料分类")
    private String productClassify;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 最小安全库存天数
     */
    @ApiModelProperty(value = "最小安全库存天数")
    @FieldInterpretation(value = "最小安全库存天数")
    private BigDecimal safetyStockDaysMin;
    /**
     * 目标安全库存天数
     */
    @ApiModelProperty(value = "目标安全库存天数")
    @FieldInterpretation(value = "目标安全库存天数")
    private BigDecimal safetyStockDaysStandard;
    /**
     * 最大安全库存天数
     */
    @ApiModelProperty(value = "最大安全库存天数")
    @FieldInterpretation(value = "最大安全库存天数")
    private BigDecimal safetyStockDaysMax;
    /**
     * 物料大类，PVB,B
     */
    @ApiModelProperty(value = "物料大类，PVB,B")
    @FieldInterpretation(value = "物料大类，PVB,B")
    private String productCategory;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productFactoryCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModeCode;
    /**
     * 材料风险等级
     */
    @ApiModelProperty(value = "材料风险等级")
    @FieldInterpretation(value = "材料风险等级")
    private String materialRiskLevel;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;

    @Override
    public void clean() {

    }

}
