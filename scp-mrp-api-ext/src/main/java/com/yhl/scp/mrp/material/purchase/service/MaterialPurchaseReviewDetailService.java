package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDetailVO;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewDetailService</code>
 * <p>
 * 材料采购评审明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:05:17
 */
public interface MaterialPurchaseReviewDetailService extends BaseService<MaterialPurchaseReviewDetailDTO, MaterialPurchaseReviewDetailVO> {

    BaseResponse<Void> doUpdateSelective(MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO);

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseReviewDetailVO}
     */
    List<MaterialPurchaseReviewDetailVO> selectAll();

}
