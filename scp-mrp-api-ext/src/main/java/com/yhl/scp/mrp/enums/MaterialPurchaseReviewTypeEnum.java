package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>MaterialPurchaseReviewTypeEnum</code>
 * <p>
 * 材料采购评审类型枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 16:23:59
 */
public enum MaterialPurchaseReviewTypeEnum implements CommonEnum {

    INVENTORY_AND_DEMAND("INVENTORY_AND_DEMAND", "库存与需求"),
    MODEL_HISTORY("MODEL_HISTORY", "车型历史"),
    MODEL_FORECAST("MODEL_FORECAST", "车型预测");

    private String code;

    private String desc;

    MaterialPurchaseReviewTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static String getCodeByDesc(String desc) {
        for (MaterialPurchaseReviewTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
