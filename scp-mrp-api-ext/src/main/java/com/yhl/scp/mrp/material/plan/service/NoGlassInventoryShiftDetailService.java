package com.yhl.scp.mrp.material.plan.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDetailDTO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;

import java.util.List;
import java.util.Map;

/**
 * <code>NoGlassInventoryShiftDetailService</code>
 * <p>
 * 非原片库存推移详情表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:26:20
 */
public interface NoGlassInventoryShiftDetailService extends BaseService<NoGlassInventoryShiftDetailDTO, NoGlassInventoryShiftDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link NoGlassInventoryShiftDetailVO}
     */
    List<NoGlassInventoryShiftDetailVO> selectAll();

    BaseResponse<String> checkAdjust(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO);

    BaseResponse<String> doAdjustData(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO);

    void doDeleteDataIds(List<String> dataIdList);

    List<NoGlassInventoryShiftDetailVO> selectVOByParams(Map<String, Object> params);

}
