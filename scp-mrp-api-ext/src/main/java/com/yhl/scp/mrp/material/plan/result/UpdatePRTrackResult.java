package com.yhl.scp.mrp.material.plan.result;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>UpdatePRTrackResult</code>
 * <p>
 * SRM到货更新单（PR模式）返回值结果响应对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 14:06:47
 */
@ApiModel(value = "SRM到货更新单（PR模式）返回值结果响应对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePRTrackResult implements Serializable {

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    @FieldInterpretation(value = "状态码")
    private int status;
    /**
     * PR单号
     */
    @ApiModelProperty(value = "PR单号")
    @FieldInterpretation(value = "PR单号")
    private String prNo;
    /**
     * PR行号
     */
    @ApiModelProperty(value = "PR行号")
    @FieldInterpretation(value = "PR行号")
    private String lineNo;
    /**
     * 消息
     */
    @ApiModelProperty(value = "消息")
    @FieldInterpretation(value = "消息")
    private String msg;

}
