package com.yhl.scp.mrp.material.arrival.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <code>MaterialModelHistoryVO</code>
 * <p>
 * 材料车型预测VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:19:15
 */
@ApiModel(value = "材料车型预测VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialModelForecastVO implements Serializable {

    private static final long serialVersionUID = 8146747248326694780L;

    /**
     * 年月列
     */
    @ApiModelProperty(value = "年月列")
    @FieldInterpretation(value = "年月列")
    private List<String> yearMonthArrange;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;

    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productFactoryCode;

    /**
     * 单片面积
     */
    @ApiModelProperty(value = "单片面积")
    @FieldInterpretation(value = "单片面积")
    private BigDecimal singleAcreage;

    /**
     * 统计
     */
    @ApiModelProperty(value = "统计")
    @FieldInterpretation(value = "统计")
    private List<MaterialStatisticsVO> statisticsList;
}
