package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseOrderDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseOrderVO;

import java.util.List;

/**
 * <code>MaterialPurchaseOrderService</code>
 * <p>
 * 物料采购订单信息表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-30 10:14:35
 */
public interface MaterialPurchaseOrderService extends BaseService<MaterialPurchaseOrderDTO, MaterialPurchaseOrderVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseOrderVO}
     */
    List<MaterialPurchaseOrderVO> selectAll();

	void doDisposeWaitDeliveryQuantity(Integer moveMinute, String scenario);

}
