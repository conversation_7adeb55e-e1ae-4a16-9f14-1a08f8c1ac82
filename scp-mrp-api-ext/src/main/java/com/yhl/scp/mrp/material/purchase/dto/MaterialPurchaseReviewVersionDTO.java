package com.yhl.scp.mrp.material.purchase.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewVersionDTO</code>
 * <p>
 * 材料采购评审版本DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:26
 */
@ApiModel(value = "材料采购评审版本DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPurchaseReviewVersionDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 791508565317988941L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 版本编码
     */
    @ApiModelProperty(value = "版本编码")
    private String versionCode;
    /**
     * 版本名称
     */
    @ApiModelProperty(value = "版本名称")
    private String versionName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
