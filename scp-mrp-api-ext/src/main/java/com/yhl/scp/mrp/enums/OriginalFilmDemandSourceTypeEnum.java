package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <p>
 * 原片需求征询数据来源枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 14:00:17
 */
public enum OriginalFilmDemandSourceTypeEnum implements CommonEnum {

	INVENTORY_SHIFT_DEMAND("INVENTORY_SHIFT_DEMAND", "原片库存推移需求"),
	MANUAL_ADDITION_DEMAND("MANUAL_ADDITION_DEMAND", "手工新增需求"),
    ;


    private String code;
    private String desc;

    OriginalFilmDemandSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

