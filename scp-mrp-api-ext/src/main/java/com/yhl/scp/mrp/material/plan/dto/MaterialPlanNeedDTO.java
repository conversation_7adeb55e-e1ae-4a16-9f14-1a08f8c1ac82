package com.yhl.scp.mrp.material.plan.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPlanNeedDTO</code>
 * <p>
 * 要货计划DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:11:00
 */
@ApiModel(value = "要货计划DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanNeedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 622581920536159166L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 库存推移id
     */
    @ApiModelProperty(value = "库存推移id")
    private String materialPlanInventoryShiftId;
    /**
     * 要货计划单号
     */
    @ApiModelProperty(value = "要货计划单号")
    private String materialPlanNeedNo;
    /**
     * 上版数量
     */
    @ApiModelProperty(value = "上版数量")
    private BigDecimal preVersionNeedQuantity;
    /**
     * 要货数量
     */
    @ApiModelProperty(value = "要货数量")
    private BigDecimal needQuantity;
    /**
     * 要货日期
     */
    @ApiModelProperty(value = "要货日期")
    private Date needDate;
    /**
     * 材料需求发布日期
     */
    @ApiModelProperty(value = "需求发布日期")
    private Date requirementReleaseDate;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderCode;
    /**
     * 采购单行号
     */
    @ApiModelProperty(value = "采购单行号")
    private String purchaseOrderLineCode;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 承诺到货时间
     */
    @ApiModelProperty(value = "承诺到货时间")
    private Date expectedArrivalTime;
    /**
     * 承诺到货数量
     */
    @ApiModelProperty(value = "承诺到货数量")
    private BigDecimal expectedArrivalQuantity;
    /**
     * 剩余未发货数量
     */
    @ApiModelProperty(value = "剩余未发货数量")
    private BigDecimal unshippedQuantity;
    /**
     * 供应数量
     */
    @ApiModelProperty(value = "供应数量")
    private BigDecimal supplyQuantity;
    /**
     * 订单数量
     */
    @ApiModelProperty(value = "订单数量")
    private BigDecimal orderQuantity;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;
    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    private String publishStatus;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    /**
     * 父级id（拆单）
     */
    @ApiModelProperty(value = "父级id（拆单）")
    private String parentId;

    /**
     *  星期几
     */
    @ApiModelProperty(value = "星期几")
    private Integer dayOfWeek;

    /**
     * 几号
     */
    @ApiModelProperty(value = "几号")
    private Integer dayOfMonth;
    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    private String publishUser;

    private BigDecimal packageLot;

    private BigDecimal minOrderQty;

    /**
     * 要货计划锁定期
     */
    @ApiModelProperty(value = "要货计划锁定期")
    @FieldInterpretation(value = "要货计划锁定期")
    private BigDecimal requestCargoPlanLockDay;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    /**
     * 推移版本代码
     */
    @ApiModelProperty(value = "推移版本代码")
    private String inventoryShiftVersionCode;

    /**
     *  是否在锁定期
     */
    @ApiModelProperty(value = "是否在锁定期")
    private String whetherLockPeriod;

}
