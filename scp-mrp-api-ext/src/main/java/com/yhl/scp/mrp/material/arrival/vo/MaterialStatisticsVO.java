package com.yhl.scp.mrp.material.arrival.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <code>MaterialStatisticsVO</code>
 * <p>
 * 材料统计VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:19:15
 */
@ApiModel(value = "材料统计VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialStatisticsVO implements Serializable {

    private static final long serialVersionUID = 731393624673893750L;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    @FieldInterpretation(value = "版本id")
    private String materialPurchaseReviewVersionId;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @FieldInterpretation(value = "id")
    private String id;

    /**
     * 行描述
     */
    @ApiModelProperty(value = "行描述")
    @FieldInterpretation(value = "行描述")
    private String lineDescription;

    /**
     * 月度
     */
    @ApiModelProperty(value = "月度")
    @FieldInterpretation(value = "月度")
    private Map<String, ?> monthlyMap = new HashMap<>();

    /**
     * 合计
     */
    @ApiModelProperty(value = "合计")
    @FieldInterpretation(value = "合计")
    private BigDecimal amount;

    /**
     * 一致性需求预测版本ID
     */
    @ApiModelProperty(value = "一致性需求预测版本ID")
    @FieldInterpretation(value = "一致性需求预测版本ID")
    private String consistenceDemandForecastVersionId;

    /**
     * 预测提供者
     */
    @ApiModelProperty(value = "预测提供者")
    @FieldInterpretation(value = "预测提供者")
    private String forecastProvider;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;
}
