package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewVersionVO;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewVersionService</code>
 * <p>
 * 材料采购评审版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:26
 */
public interface MaterialPurchaseReviewVersionService extends BaseService<MaterialPurchaseReviewVersionDTO, MaterialPurchaseReviewVersionVO> {

    @SuppressWarnings({"unchecked"})
    BaseResponse<Void> doCreateWithPrimaryKey(MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO);

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseReviewVersionVO}
     */
    List<MaterialPurchaseReviewVersionVO> selectAll();


    String getVersionCode();
}
