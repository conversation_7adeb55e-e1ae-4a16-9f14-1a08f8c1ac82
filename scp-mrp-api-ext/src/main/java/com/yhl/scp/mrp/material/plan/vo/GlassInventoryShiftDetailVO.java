package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>GlassInventoryShiftDetailVO</code>
 * <p>
 * 物料库存推移VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:08:22
 */
@ApiModel(value = "物料库存推移VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GlassInventoryShiftDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -63079242782968267L;

    /**
     * 物料计划版本id
     */
    @ApiModelProperty(value = "物料计划版本id")
    @FieldInterpretation(value = "物料计划版本id")
    private String inventoryShiftDataId;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 库存点类型
     */
    @ApiModelProperty(value = "库存点类型")
    @FieldInterpretation(value = "库存点类型")
    private String stockPointType;
    /**
     * 库存推移日期
     */
    @ApiModelProperty(value = "库存推移日期")
    @FieldInterpretation(value = "库存推移日期")
    private Date inventoryDate;
    /**
     * 所有浮法厂库存
     */
    @ApiModelProperty(value = "所有浮法厂库存")
    @FieldInterpretation(value = "所有浮法厂库存")
    private BigDecimal allFfInventory;
    /**
     * 最小安全库存水位
     */
    @ApiModelProperty(value = "最小安全库存水位")
    @FieldInterpretation(value = "最小安全库存水位")
    private BigDecimal safetyStockLevelMin;
    /**
     * 标准安全库存水位
     */
    @ApiModelProperty(value = "标准安全库存水位")
    @FieldInterpretation(value = "标准安全库存水位")
    private BigDecimal safetyStockLevelStandard;
    /**
     * 最大安全库存水位
     */
    @ApiModelProperty(value = "最大安全库存水位")
    @FieldInterpretation(value = "最大安全库存水位")
    private BigDecimal safetyStockLevelMax;
    /**
     * 期初库存
     */
    @ApiModelProperty(value = "期初库存")
    @FieldInterpretation(value = "期初库存")
    private BigDecimal openingInventory;
    /**
     * 本厂期初库存
     */
    @ApiModelProperty(value = "本厂期初库存")
    @FieldInterpretation(value = "本厂期初库存")
    private BigDecimal bcOpeningInventory;
    /**
     * 毛需求
     */
    @ApiModelProperty(value = "毛需求")
    @FieldInterpretation(value = "毛需求")
    private BigDecimal demandQuantity;
    /**
     * 计划调拨-浮法送柜
     */
    @ApiModelProperty(value = "计划调拨-浮法送柜")
    @FieldInterpretation(value = "计划调拨-浮法送柜")
    private BigDecimal adjustQuantityFromFloat;
    /**
     * 已发布调拨计划到货
     */
    @ApiModelProperty(value = "已发布调拨计划到货")
    @FieldInterpretation(value = "已发布调拨计划到货")
    private BigDecimal inputQuantity;
    /**
     * 计划调拨-码头送柜
     */
    @ApiModelProperty(value = "计划调拨-码头送柜")
    @FieldInterpretation(value = "计划调拨-码头送柜")
    private BigDecimal adjustQuantityFromPort;
    /**
     * 在途码头送柜
     */
    @ApiModelProperty(value = "在途码头送柜")
    @FieldInterpretation(value = "在途码头送柜")
    private BigDecimal transitQuantityFromPort;
    /**
     * 在途浮法送柜
     */
    @ApiModelProperty(value = "在途浮法送柜")
    @FieldInterpretation(value = "在途浮法送柜")
    private BigDecimal transitQuantityFromFloat;
    /**
     * 已发布调拨运出至本厂
     */
    @ApiModelProperty(value = "已发布调拨运出至本厂")
    @FieldInterpretation(value = "已发布调拨运出至本厂")
    private BigDecimal outputQuantityToBc;
    /**
     * 已发布调拨运出至码头
     */
    @ApiModelProperty(value = "已发布调拨运出至码头")
    @FieldInterpretation(value = "已发布调拨运出至码头")
    private BigDecimal outputQuantityToPort;
    /**
     * 计划调拨运出至本厂
     */
    @ApiModelProperty(value = "计划调拨运出至本厂")
    @FieldInterpretation(value = "计划调拨运出至本厂")
    private BigDecimal decisionOutputQuantityToBc;
    /**
     * 计划调拨运出至码头
     */
    @ApiModelProperty(value = "计划调拨运出至码头")
    @FieldInterpretation(value = "计划调拨运出至码头")
    private BigDecimal decisionOutputQuantityToPort;
    /**
     * 供应替代
     */
    @ApiModelProperty(value = "供应替代")
    @FieldInterpretation(value = "供应替代")
    private BigDecimal useReplaceQuantity;
    /**
     * 替代其他需求
     */
    @ApiModelProperty(value = "替代其他需求")
    @FieldInterpretation(value = "替代其他需求")
    private BigDecimal usedAsReplaceQuantity;
    /**
     * 补库缺口
     */
    @ApiModelProperty(value = "补库缺口")
    @FieldInterpretation(value = "补库缺口")
    private BigDecimal safetyStockGap;
    /**
     * 期末库存
     */
    @ApiModelProperty(value = "期末库存")
    @FieldInterpretation(value = "期末库存")
    private BigDecimal endingInventory;

    /**
     * 警告提醒（期初）
     */
    @ApiModelProperty(value = "警告提醒（期初）")
    @FieldInterpretation(value = "警告提醒（期初）")
    private String warningRemindStart;
    /**
     * 警告提醒（期末）
     */
    @ApiModelProperty(value = "警告提醒（期末）")
    @FieldInterpretation(value = "警告提醒（期末）")
    private String warningRemindEnd;

    /**
     * 库存推移日期
     */
    @ApiModelProperty(value = "库存推移日期维度")
    @FieldInterpretation(value = "库存推移日期维度")
    private String inventoryDateDimension;

    /**
     * 镀膜预警（码头）
     */
    @ApiModelProperty(value = "镀膜预警（码头）")
    @FieldInterpretation(value = "镀膜预警（码头）")
    private Boolean coatingWarningMt = false;

    /**
     * 镀膜预警原因（码头）
     */
    @ApiModelProperty(value = "镀膜预警原因（码头）")
    @FieldInterpretation(value = "镀膜预警原因（码头）")
    private String coatingWarningReasonMt;

    /**
     * 镀膜预警（浮法）
     */
    @ApiModelProperty(value = "镀膜预警（浮法）")
    @FieldInterpretation(value = "镀膜预警（浮法）")
    private Boolean coatingWarningFf = false;

    /**
     * 镀膜预警原因（浮法）
     */
    @ApiModelProperty(value = "镀膜预警原因（浮法）")
    @FieldInterpretation(value = "镀膜预警原因（浮法）")
    private String coatingWarningReasonFf;

    @Override
    public void clean() {

    }

}
