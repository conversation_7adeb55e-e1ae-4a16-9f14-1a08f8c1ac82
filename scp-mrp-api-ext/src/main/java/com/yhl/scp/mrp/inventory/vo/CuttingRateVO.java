package com.yhl.scp.mrp.inventory.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <code>CuttingRateVO</code>
 * <p>
 * 切裁率计算VO（仅用于收集参数计算切裁率）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-09 14:13:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CuttingRateVO {
    /**
     * 所属工艺路径id
     */
    @ApiModelProperty(value = "所属工艺路径id")
    @FieldInterpretation(value = "所属工艺路径id")
    private String routingId;
    /**
     * 所属工艺路径步骤id
     */
    @ApiModelProperty(value = "所属工艺路径步骤id")
    @FieldInterpretation(value = "所属工艺路径步骤id")
    private String routingStepId;
    /**
     * 所属工艺路径步骤输入物品id
     */
    @ApiModelProperty(value = "所属工艺路径步骤id")
    @FieldInterpretation(value = "所属工艺路径步骤id")
    private String routingStepInputId;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal length;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal width;
    /**
     * 单位输入量
     */
    @ApiModelProperty("单位输入量")
    @FieldInterpretation(value = "单位输入量")
    private BigDecimal inputFactor;
}
