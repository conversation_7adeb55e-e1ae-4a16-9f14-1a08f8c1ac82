package com.yhl.scp.mrp.originalFilm.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>OriginalFilmDemandConsultSummaryDTO</code>
 * <p>
 * 原片需求征询汇总DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 09:26:39
 */
@ApiModel(value = "原片需求征询汇总DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OriginalFilmDemandConsultSummaryDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 429739832278701909L;

        
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 原片需求征询版本ID
     */
    @ApiModelProperty(value = "原片需求征询版本ID")
    private String originalFilmDemandConsultVersionId;
        
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private BigDecimal productThickness;
        
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String productColor;
        
    /**
     * 特殊要求
     */
    @ApiModelProperty(value = "特殊要求")
    private String specialRemark;
        
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandedQuantity;
        
    /**
     * 需求月份
     */
    @ApiModelProperty(value = "需求月份")
    private String demandedMonth;
        
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
        
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
        
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;
    
    /**
     * 起始厚度
     */
    @ApiModelProperty(value = "起始厚度")
    private BigDecimal startProductThickness;
    
    /**
     * 结束厚度
     */
    @ApiModelProperty(value = "结束厚度")
    private BigDecimal endProductThickness;
    
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;


}
