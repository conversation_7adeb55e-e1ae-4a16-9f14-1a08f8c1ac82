package com.yhl.scp.mrp.material.purchase.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewVersionVO</code>
 * <p>
 * 材料采购评审版本VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:26
 */
@ApiModel(value = "材料采购评审版本VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPurchaseReviewVersionVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 143484331984498102L;

    /**
     * 版本编码
     */
    @ApiModelProperty(value = "版本编码")
    @FieldInterpretation(value = "版本编码")
    private String versionCode;
    /**
     * 版本名称
     */
    @ApiModelProperty(value = "版本名称")
    @FieldInterpretation(value = "版本名称")
    private String versionName;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
