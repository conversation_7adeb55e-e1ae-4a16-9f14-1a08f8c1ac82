package com.yhl.scp.mrp.material.purchase.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewDetailDTO</code>
 * <p>
 * 材料采购评审明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:05:17
 */
@ApiModel(value = "材料采购评审明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPurchaseReviewDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -67921523051485732L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    private String materialPurchaseReviewVersionId;
    /**
     * 评审类型
     */
    @ApiModelProperty(value = "评审类型")
    private String reviewType;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String productName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productFactoryCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemName;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    private BigDecimal purchaseLot;
    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
    private BigDecimal minOrderQuantity;
    /**
     * 评审意见
     */
    @ApiModelProperty(value = "评审意见")
    private String reviewComments;
    /**
     * 批准
     */
    @ApiModelProperty(value = "批准")
    private String approve;
    /**
     * 审核
     */
    @ApiModelProperty(value = "审核")
    private String toExamine;
    /**
     * 制表
     */
    @ApiModelProperty(value = "制表")
    private String tabulation;
    /**
     * 一致性需求预测版本ID
     */
    @ApiModelProperty(value = "一致性需求预测版本ID")
    private String consistenceDemandForecastVersionId;
    /**
     * 预测提供者
     */
    @ApiModelProperty(value = "预测提供者")
    private String forecastProvider;
    /**
     * 行描述
     */
    @ApiModelProperty(value = "行描述")
    private String lineDescription;
    /**
     * 车型EOP时间
     */
    @ApiModelProperty(value = "车型EOP时间")
    private String vehicleModelEopDate;
    /**
     * 主机厂车型
     */
    @ApiModelProperty(value = "主机厂车型")
    private String vehicleModel;
    /**
     * 年月值（一）
     */
    @ApiModelProperty(value = "年月值（一）")
    private String oneYearMonthValue;
    /**
     * 年月值（二）
     */
    @ApiModelProperty(value = "年月值（二）")
    private String twoYearMonthValue;
    /**
     * 年月值（三）
     */
    @ApiModelProperty(value = "年月值（三）")
    private String threeYearMonthValue;
    /**
     * 年月值（四）
     */
    @ApiModelProperty(value = "年月值（四）")
    private String fourYearMonthValue;
    /**
     * 年月值（五）
     */
    @ApiModelProperty(value = "年月值（五）")
    private String fiveYearMonthValue;
    /**
     * 年月值（六）
     */
    @ApiModelProperty(value = "年月值（六）")
    private String sixYearMonthValue;
    /**
     * 年月值（七）
     */
    @ApiModelProperty(value = "年月值（七）")
    private String sevenYearMonthValue;
    /**
     * 年月值（八）
     */
    @ApiModelProperty(value = "年月值（八）")
    private String eightYearMonthValue;
    /**
     * 年月值（九）
     */
    @ApiModelProperty(value = "年月值（九）")
    private String nineYearMonthValue;
    /**
     * 年月值（十）
     */
    @ApiModelProperty(value = "年月值（十）")
    private String tenYearMonthValue;
    /**
     * 年月值（十一）
     */
    @ApiModelProperty(value = "年月值（十一）")
    private String elevenYearMonthValue;
    /**
     * 年月值（十二）
     */
    @ApiModelProperty(value = "年月值（十二）")
    private String twelveYearMonthValue;
    /**
     * 指定年月
     */
    @ApiModelProperty(value = "指定年月")
    private String appointYearMonth;
    /**
     * 单片面积
     */
    @ApiModelProperty(value = "单片面积")
    private String singlePieceArea;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
