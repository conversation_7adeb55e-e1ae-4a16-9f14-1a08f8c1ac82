package com.yhl.scp.dcp.apiConfig.externalApi.resp.mes;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.Date;

/**
 * <code>MesFeedBack</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16 15:03:14
 */
@lombok.Data
public class MesFeedBack {

    private Integer kid;
    private String itemCode;
    private String propertyValue;
    private String transactionCode;
    private String userName;
    private Integer trxQty;
    private String businessUnitt;
    private String transactionDesc;
    private String transactionType;
    private String wipEntityName;
    private Integer operationNum;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date shiftdate;
    private String prodLineCode;
    private String eventTime;
    private String plantCode;
    private String cellOperationNum;

}
