package com.yhl.scp.dcp.apiConfig.feign;

import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <code>NewDcpFeign</code>
 * <p>
 * NewDcpFeign
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-09 09:10:18
 */
@FeignClient(value = ServletContextConstants.DCP, path = "/", configuration = FeignConfig.class, url = "${dcp.feign.url:}")
public interface NewDcpFeign {

    @ApiOperation("调用外部系统接口")
    @PostMapping("externalApi/call")
    BaseResponse<String> callExternalApi(@RequestParam("tenantCode") String tenantCode,
                                         @RequestParam("apiSource") String apiSource,
                                         @RequestParam("apiCategory") String apiCategory,
                                         @RequestBody Map<String, Object> params);

    @ApiOperation("调用外部系统带文件接口")
    @RequestMapping(method = RequestMethod.POST, value = "externalApi/callWithAttach", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    BaseResponse<String> callWithAttach(@RequestPart("data") String data, @RequestPart("files") MultipartFile[] files);

    @ApiOperation("获取接口日志")
    @PostMapping("externalApi/apiLog")
    ExtApiLogVO getExtApiLog(@RequestParam(value = "id") String id);

    @ApiOperation("获取所有外部接口标识")
    @PostMapping("externalApi/allApiLog")
    List<ApiConfigVO> getAllApiConfig();

    @ApiOperation("根据参数获取接口日志")
    @PostMapping("externalApi/apiMapLog")
    List<ExtApiLogVO> getByParamsExtApiLog(@RequestBody Map<String, Object> params);
}