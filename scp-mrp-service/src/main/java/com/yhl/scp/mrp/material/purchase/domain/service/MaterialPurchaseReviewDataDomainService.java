package com.yhl.scp.mrp.material.purchase.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDataDO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDataDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDataPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseReviewDataDomainService</code>
 * <p>
 * 材料采购评审领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:30
 */
@Service
public class MaterialPurchaseReviewDataDomainService {

    @Resource
    private MaterialPurchaseReviewDataDao materialPurchaseReviewDataDao;

    /**
     * 数据校验
     *
     * @param materialPurchaseReviewDataDO 领域对象
     */
    public void validation(MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO) {
        checkNotNull(materialPurchaseReviewDataDO);
        checkUniqueCode(materialPurchaseReviewDataDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialPurchaseReviewDataDO 领域对象
     */
    private void checkNotNull(MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO) {}

    /**
     * 唯一性校验
     *
     * @param materialPurchaseReviewDataDO 领域对象
     */
    private void checkUniqueCode(MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO) {}

}
