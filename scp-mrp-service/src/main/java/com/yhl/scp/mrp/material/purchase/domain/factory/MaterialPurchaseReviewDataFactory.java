package com.yhl.scp.mrp.material.purchase.domain.factory;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDataDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDataDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDataDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialPurchaseReviewDataFactory</code>
 * <p>
 * 材料采购评审领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:30
 */
@Component
public class MaterialPurchaseReviewDataFactory {

    @Resource
    private MaterialPurchaseReviewDataDao materialPurchaseReviewDataDao;

    MaterialPurchaseReviewDataDO create(MaterialPurchaseReviewDataDTO dto) {
        // TODO
        return null;
    }

}
