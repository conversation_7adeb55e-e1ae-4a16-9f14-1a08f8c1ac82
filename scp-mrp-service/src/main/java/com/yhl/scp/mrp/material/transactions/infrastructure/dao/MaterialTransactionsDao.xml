<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.transactions.infrastructure.dao.MaterialTransactionsDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO">
        <!--@Table mrp_material_transactions-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="transaction_type" jdbcType="VARCHAR" property="transactionType"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="sub_inventory" jdbcType="VARCHAR" property="subInventory"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="to_org" jdbcType="VARCHAR" property="toOrg"/>
        <result column="to_sub_inventory" jdbcType="VARCHAR" property="toSubInventory"/>
        <result column="to_location" jdbcType="VARCHAR" property="toLocation"/>
        <result column="transaction_qty" jdbcType="VARCHAR" property="transactionQty"/>
        <result column="transaction_uom" jdbcType="VARCHAR" property="transactionUom"/>
        <result column="primary_qty" jdbcType="VARCHAR" property="primaryQty"/>
        <result column="primary_unit" jdbcType="VARCHAR" property="primaryUnit"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="cost" jdbcType="VARCHAR" property="cost"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="transaction_date" jdbcType="TIMESTAMP" property="transactionDate"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="transaction_id" jdbcType="VARCHAR" property="transactionId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,company,stock_point_code,product_code,description,batch,category,transaction_type,source_type,source,sub_inventory,location,to_org,to_sub_inventory,to_location,transaction_qty,transaction_uom,primary_qty,primary_unit,area,cost,item_id,transaction_date,last_update_date,transaction_id,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.company != null and params.company != ''">
                and company = #{params.company,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size()>0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.description != null and params.description != ''">
                and description = #{params.description,jdbcType=VARCHAR}
            </if>
            <if test="params.batch != null and params.batch != ''">
                and batch = #{params.batch,jdbcType=VARCHAR}
            </if>
            <if test="params.category != null and params.category != ''">
                and category = #{params.category,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionType != null and params.transactionType != ''">
                and transaction_type = #{params.transactionType,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.source != null and params.source != ''">
                and source = #{params.source,jdbcType=VARCHAR}
            </if>
            <if test="params.subInventory != null and params.subInventory != ''">
                and sub_inventory = #{params.subInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.location != null and params.location != ''">
                and location = #{params.location,jdbcType=VARCHAR}
            </if>
            <if test="params.toOrg != null and params.toOrg != ''">
                and to_org = #{params.toOrg,jdbcType=VARCHAR}
            </if>
            <if test="params.toSubInventory != null and params.toSubInventory != ''">
                and to_sub_inventory = #{params.toSubInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.toLocation != null and params.toLocation != ''">
                and to_location = #{params.toLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionQty != null">
                and transaction_qty = #{params.transactionQty,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionUom != null and params.transactionUom != ''">
                and transaction_uom = #{params.transactionUom,jdbcType=VARCHAR}
            </if>
            <if test="params.primaryQty != null">
                and primary_qty = #{params.primaryQty,jdbcType=VARCHAR}
            </if>
            <if test="params.primaryUnit != null and params.primaryUnit != ''">
                and primary_unit = #{params.primaryUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null and params.area != ''">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.cost != null and params.cost != ''">
                and cost = #{params.cost,jdbcType=VARCHAR}
            </if>
            <if test="params.itemId != null and params.itemId != ''">
                and item_id = #{params.itemId,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionDate != null">
                and transaction_date = #{params.transactionDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transactionId != null and params.transactionId != ''">
                and transaction_id = #{params.transactionId,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionIdList != null and params.transactionIdList.size()>0">
                and transaction_id  in
                <foreach collection="params.transactionIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_transactions
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_transactions
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_transactions
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_transactions
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_transactions(
        id,
        company,
        stock_point_code,
        product_code,
        description,
        batch,
        category,
        transaction_type,
        source_type,
        source,
        sub_inventory,
        location,
        to_org,
        to_sub_inventory,
        to_location,
        transaction_qty,
        transaction_uom,
        primary_qty,
        primary_unit,
        area,
        cost,
        item_id,
        transaction_date,
        last_update_date,
        transaction_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{description,jdbcType=VARCHAR},
        #{batch,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR},
        #{transactionType,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{source,jdbcType=VARCHAR},
        #{subInventory,jdbcType=VARCHAR},
        #{location,jdbcType=VARCHAR},
        #{toOrg,jdbcType=VARCHAR},
        #{toSubInventory,jdbcType=VARCHAR},
        #{toLocation,jdbcType=VARCHAR},
        #{transactionQty,jdbcType=VARCHAR},
        #{transactionUom,jdbcType=VARCHAR},
        #{primaryQty,jdbcType=VARCHAR},
        #{primaryUnit,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{cost,jdbcType=VARCHAR},
        #{itemId,jdbcType=VARCHAR},
        #{transactionDate,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{transactionId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO">
        insert into mrp_material_transactions(id,
                                              company,
                                              stock_point_code,
                                              product_code,
                                              description,
                                              batch,
                                              category,
                                              transaction_type,
                                              source_type,
                                              source,
                                              sub_inventory,
                                              location,
                                              to_org,
                                              to_sub_inventory,
                                              to_location,
                                              transaction_qty,
                                              transaction_uom,
                                              primary_qty,
                                              primary_unit,
                                              area,
                                              cost,
                                              item_id,
                                              transaction_date,
                                              last_update_date,
                                              transaction_id,
                                              remark,
                                              enabled,
                                              creator,
                                              create_time,
                                              modifier,
                                              modify_time,
                                              version_value)
        values (#{id,jdbcType=VARCHAR},
                #{company,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR},
                #{batch,jdbcType=VARCHAR},
                #{category,jdbcType=VARCHAR},
                #{transactionType,jdbcType=VARCHAR},
                #{sourceType,jdbcType=VARCHAR},
                #{source,jdbcType=VARCHAR},
                #{subInventory,jdbcType=VARCHAR},
                #{location,jdbcType=VARCHAR},
                #{toOrg,jdbcType=VARCHAR},
                #{toSubInventory,jdbcType=VARCHAR},
                #{toLocation,jdbcType=VARCHAR},
                #{transactionQty,jdbcType=VARCHAR},
                #{transactionUom,jdbcType=VARCHAR},
                #{primaryQty,jdbcType=VARCHAR},
                #{primaryUnit,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR},
                #{cost,jdbcType=VARCHAR},
                #{itemId,jdbcType=VARCHAR},
                #{transactionDate,jdbcType=TIMESTAMP},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{transactionId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_transactions(
        id,
        company,
        stock_point_code,
        product_code,
        description,
        batch,
        category,
        transaction_type,
        source_type,
        source,
        sub_inventory,
        location,
        to_org,
        to_sub_inventory,
        to_location,
        transaction_qty,
        transaction_uom,
        primary_qty,
        primary_unit,
        area,
        cost,
        item_id,
        transaction_date,
        last_update_date,
        transaction_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.company,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.description,jdbcType=VARCHAR},
            #{entity.batch,jdbcType=VARCHAR},
            #{entity.category,jdbcType=VARCHAR},
            #{entity.transactionType,jdbcType=VARCHAR},
            #{entity.sourceType,jdbcType=VARCHAR},
            #{entity.source,jdbcType=VARCHAR},
            #{entity.subInventory,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.toOrg,jdbcType=VARCHAR},
            #{entity.toSubInventory,jdbcType=VARCHAR},
            #{entity.toLocation,jdbcType=VARCHAR},
            #{entity.transactionQty,jdbcType=VARCHAR},
            #{entity.transactionUom,jdbcType=VARCHAR},
            #{entity.primaryQty,jdbcType=VARCHAR},
            #{entity.primaryUnit,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.cost,jdbcType=VARCHAR},
            #{entity.itemId,jdbcType=VARCHAR},
            #{entity.transactionDate,jdbcType=TIMESTAMP},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.transactionId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_transactions(
        id,
        company,
        stock_point_code,
        product_code,
        description,
        batch,
        category,
        transaction_type,
        source_type,
        source,
        sub_inventory,
        location,
        to_org,
        to_sub_inventory,
        to_location,
        transaction_qty,
        transaction_uom,
        primary_qty,
        primary_unit,
        area,
        cost,
        item_id,
        transaction_date,
        last_update_date,
        transaction_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.company,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.description,jdbcType=VARCHAR},
            #{entity.batch,jdbcType=VARCHAR},
            #{entity.category,jdbcType=VARCHAR},
            #{entity.transactionType,jdbcType=VARCHAR},
            #{entity.sourceType,jdbcType=VARCHAR},
            #{entity.source,jdbcType=VARCHAR},
            #{entity.subInventory,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.toOrg,jdbcType=VARCHAR},
            #{entity.toSubInventory,jdbcType=VARCHAR},
            #{entity.toLocation,jdbcType=VARCHAR},
            #{entity.transactionQty,jdbcType=VARCHAR},
            #{entity.transactionUom,jdbcType=VARCHAR},
            #{entity.primaryQty,jdbcType=VARCHAR},
            #{entity.primaryUnit,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.cost,jdbcType=VARCHAR},
            #{entity.itemId,jdbcType=VARCHAR},
            #{entity.transactionDate,jdbcType=TIMESTAMP},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.transactionId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO">
        update mrp_material_transactions
        set company          = #{company,jdbcType=VARCHAR},
            stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
            product_code     = #{productCode,jdbcType=VARCHAR},
            description         = #{description,jdbcType=VARCHAR},
            batch            = #{batch,jdbcType=VARCHAR},
            category         = #{category,jdbcType=VARCHAR},
            transaction_type = #{transactionType,jdbcType=VARCHAR},
            source_type      = #{sourceType,jdbcType=VARCHAR},
            source           = #{source,jdbcType=VARCHAR},
            sub_inventory    = #{subInventory,jdbcType=VARCHAR},
            location         = #{location,jdbcType=VARCHAR},
            to_org           = #{toOrg,jdbcType=VARCHAR},
            to_sub_inventory = #{toSubInventory,jdbcType=VARCHAR},
            to_location      = #{toLocation,jdbcType=VARCHAR},
            transaction_qty  = #{transactionQty,jdbcType=VARCHAR},
            transaction_uom  = #{transactionUom,jdbcType=VARCHAR},
            primary_qty      = #{primaryQty,jdbcType=VARCHAR},
            primary_unit     = #{primaryUnit,jdbcType=VARCHAR},
            area             = #{area,jdbcType=VARCHAR},
            cost             = #{cost,jdbcType=VARCHAR},
            item_id          = #{itemId,jdbcType=VARCHAR},
            transaction_date = #{transactionDate,jdbcType=TIMESTAMP},
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
            transaction_id   = #{transactionId,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO">
        update mrp_material_transactions
        <set>
            <if test="item.company != null and item.company != ''">
                company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.description != null and item.description != ''">
                description = #{item.description,jdbcType=VARCHAR},
            </if>
            <if test="item.batch != null and item.batch != ''">
                batch = #{item.batch,jdbcType=VARCHAR},
            </if>
            <if test="item.category != null and item.category != ''">
                category = #{item.category,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionType != null and item.transactionType != ''">
                transaction_type = #{item.transactionType,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.source != null and item.source != ''">
                source = #{item.source,jdbcType=VARCHAR},
            </if>
            <if test="item.subInventory != null and item.subInventory != ''">
                sub_inventory = #{item.subInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.location != null and item.location != ''">
                location = #{item.location,jdbcType=VARCHAR},
            </if>
            <if test="item.toOrg != null and item.toOrg != ''">
                to_org = #{item.toOrg,jdbcType=VARCHAR},
            </if>
            <if test="item.toSubInventory != null and item.toSubInventory != ''">
                to_sub_inventory = #{item.toSubInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.toLocation != null and item.toLocation != ''">
                to_location = #{item.toLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionQty != null">
                transaction_qty = #{item.transactionQty,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionUom != null and item.transactionUom != ''">
                transaction_uom = #{item.transactionUom,jdbcType=VARCHAR},
            </if>
            <if test="item.primaryQty != null">
                primary_qty = #{item.primaryQty,jdbcType=VARCHAR},
            </if>
            <if test="item.primaryUnit != null and item.primaryUnit != ''">
                primary_unit = #{item.primaryUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null and item.area != ''">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.cost != null and item.cost != ''">
                cost = #{item.cost,jdbcType=VARCHAR},
            </if>
            <if test="item.itemId != null and item.itemId != ''">
                item_id = #{item.itemId,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionDate != null">
                transaction_date = #{item.transactionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.transactionId != null and item.transactionId != ''">
                transaction_id = #{item.transactionId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_transactions
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.company,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.description,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batch,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.category,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.source,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sub_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.location,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="to_org = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toOrg,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="to_sub_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toSubInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="to_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_uom = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionUom,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="primary_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.primaryQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="primary_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.primaryUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="transaction_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_transactions
            <set>
                <if test="item.company != null and item.company != ''">
                    company = #{item.company,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.description != null and item.description != ''">
                    description = #{item.description,jdbcType=VARCHAR},
                </if>
                <if test="item.batch != null and item.batch != ''">
                    batch = #{item.batch,jdbcType=VARCHAR},
                </if>
                <if test="item.category != null and item.category != ''">
                    category = #{item.category,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionType != null and item.transactionType != ''">
                    transaction_type = #{item.transactionType,jdbcType=VARCHAR},
                </if>
                <if test="item.sourceType != null and item.sourceType != ''">
                    source_type = #{item.sourceType,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null and item.source != ''">
                    source = #{item.source,jdbcType=VARCHAR},
                </if>
                <if test="item.subInventory != null and item.subInventory != ''">
                    sub_inventory = #{item.subInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.location != null and item.location != ''">
                    location = #{item.location,jdbcType=VARCHAR},
                </if>
                <if test="item.toOrg != null and item.toOrg != ''">
                    to_org = #{item.toOrg,jdbcType=VARCHAR},
                </if>
                <if test="item.toSubInventory != null and item.toSubInventory != ''">
                    to_sub_inventory = #{item.toSubInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.toLocation != null and item.toLocation != ''">
                    to_location = #{item.toLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionQty != null">
                    transaction_qty = #{item.transactionQty,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionUom != null and item.transactionUom != ''">
                    transaction_uom = #{item.transactionUom,jdbcType=VARCHAR},
                </if>
                <if test="item.primaryQty != null">
                    primary_qty = #{item.primaryQty,jdbcType=VARCHAR},
                </if>
                <if test="item.primaryUnit != null and item.primaryUnit != ''">
                    primary_unit = #{item.primaryUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.area != null and item.area != ''">
                    area = #{item.area,jdbcType=VARCHAR},
                </if>
                <if test="item.cost != null and item.cost != ''">
                    cost = #{item.cost,jdbcType=VARCHAR},
                </if>
                <if test="item.itemId != null and item.itemId != ''">
                    item_id = #{item.itemId,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionDate != null">
                    transaction_date = #{item.transactionDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.transactionId != null and item.transactionId != ''">
                    transaction_id = #{item.transactionId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_transactions
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_transactions where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
