package com.yhl.scp.mrp.material.purchase.convertor;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewVersionDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewVersionPO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewVersionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewVersionConvertor</code>
 * <p>
 * 材料采购评审版本转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialPurchaseReviewVersionConvertor {

    MaterialPurchaseReviewVersionConvertor INSTANCE = Mappers.getMapper(MaterialPurchaseReviewVersionConvertor.class);

    MaterialPurchaseReviewVersionDO dto2Do(MaterialPurchaseReviewVersionDTO obj);

    MaterialPurchaseReviewVersionDTO do2Dto(MaterialPurchaseReviewVersionDO obj);

    List<MaterialPurchaseReviewVersionDO> dto2Dos(List<MaterialPurchaseReviewVersionDTO> list);

    List<MaterialPurchaseReviewVersionDTO> do2Dtos(List<MaterialPurchaseReviewVersionDO> list);

    MaterialPurchaseReviewVersionVO do2Vo(MaterialPurchaseReviewVersionDO obj);

    MaterialPurchaseReviewVersionVO po2Vo(MaterialPurchaseReviewVersionPO obj);

    List<MaterialPurchaseReviewVersionVO> po2Vos(List<MaterialPurchaseReviewVersionPO> list);

    MaterialPurchaseReviewVersionPO do2Po(MaterialPurchaseReviewVersionDO obj);

    MaterialPurchaseReviewVersionDO po2Do(MaterialPurchaseReviewVersionPO obj);

    MaterialPurchaseReviewVersionPO dto2Po(MaterialPurchaseReviewVersionDTO obj);

    List<MaterialPurchaseReviewVersionPO> dto2Pos(List<MaterialPurchaseReviewVersionDTO> obj);

}
