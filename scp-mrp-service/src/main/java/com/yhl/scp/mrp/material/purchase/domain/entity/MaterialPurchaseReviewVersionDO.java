package com.yhl.scp.mrp.material.purchase.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewVersionDO</code>
 * <p>
 * 材料采购评审版本DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPurchaseReviewVersionDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 191937812415516962L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 版本编码
     */
    private String versionCode;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本
     */
    private Integer versionValue;

}
