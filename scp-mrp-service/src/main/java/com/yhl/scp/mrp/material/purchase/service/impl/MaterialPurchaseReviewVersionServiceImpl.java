package com.yhl.scp.mrp.material.purchase.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseReviewVersionConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewVersionDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseReviewVersionDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewVersionDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewVersionPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewVersionService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseReviewVersionServiceImpl</code>
 * <p>
 * 材料采购评审版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
@Slf4j
@Service
public class MaterialPurchaseReviewVersionServiceImpl extends AbstractService implements MaterialPurchaseReviewVersionService {

    @Resource
    private MaterialPurchaseReviewVersionDao materialPurchaseReviewVersionDao;

    @Resource
    private MaterialPurchaseReviewVersionDomainService materialPurchaseReviewVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO) {
        // 0.数据转换
        MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Do(materialPurchaseReviewVersionDTO);
        MaterialPurchaseReviewVersionPO materialPurchaseReviewVersionPO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Po(materialPurchaseReviewVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewVersionDomainService.validation(materialPurchaseReviewVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseReviewVersionPO);
        materialPurchaseReviewVersionDao.insert(materialPurchaseReviewVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doCreateWithPrimaryKey(MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO) {
        // 0.数据转换
        MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Do(materialPurchaseReviewVersionDTO);
        MaterialPurchaseReviewVersionPO materialPurchaseReviewVersionPO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Po(materialPurchaseReviewVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewVersionDomainService.validation(materialPurchaseReviewVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseReviewVersionPO);
        materialPurchaseReviewVersionDao.insertWithPrimaryKey(materialPurchaseReviewVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO) {
        // 0.数据转换
        MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Do(materialPurchaseReviewVersionDTO);
        MaterialPurchaseReviewVersionPO materialPurchaseReviewVersionPO = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Po(materialPurchaseReviewVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewVersionDomainService.validation(materialPurchaseReviewVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseReviewVersionPO);
        materialPurchaseReviewVersionDao.update(materialPurchaseReviewVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseReviewVersionDTO> list) {
        List<MaterialPurchaseReviewVersionPO> newList = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseReviewVersionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseReviewVersionDTO> list) {
        List<MaterialPurchaseReviewVersionPO> newList = MaterialPurchaseReviewVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseReviewVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseReviewVersionDao.deleteBatch(idList);
        }
        return materialPurchaseReviewVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseReviewVersionVO selectByPrimaryKey(String id) {
        MaterialPurchaseReviewVersionPO po = materialPurchaseReviewVersionDao.selectByPrimaryKey(id);
        return MaterialPurchaseReviewVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_VERSION")
    public List<MaterialPurchaseReviewVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_VERSION")
    public List<MaterialPurchaseReviewVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseReviewVersionVO> dataList = materialPurchaseReviewVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseReviewVersionServiceImpl target = SpringBeanUtils.getBean(MaterialPurchaseReviewVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseReviewVersionVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseReviewVersionPO> list = materialPurchaseReviewVersionDao.selectByParams(params);
        return MaterialPurchaseReviewVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseReviewVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialPurchaseReviewVersionVO> invocation(List<MaterialPurchaseReviewVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public String getVersionCode() {
        String dateStr = DateUtils.dateToString(new Date(), "yyyyMMdd");
        String versionCode;
        MaterialPurchaseReviewVersionPO materialPurchaseReviewVersionPO = materialPurchaseReviewVersionDao.selectLastVersion();
        String userName = SystemHolder.getUserName();
        if (StringUtils.isEmpty(userName)) userName = "bpim";
        if (null == materialPurchaseReviewVersionPO) {
            versionCode = dateStr + userName + "001";
        } else {
            String lastVersionCode = materialPurchaseReviewVersionPO.getVersionCode().substring(materialPurchaseReviewVersionPO.getVersionCode().length() - 3);
            int newVersionNumber = Integer.parseInt(lastVersionCode) + 1;
            versionCode = dateStr + userName + String.format("%03d", newVersionNumber);
        }
        return versionCode;
    }

}
