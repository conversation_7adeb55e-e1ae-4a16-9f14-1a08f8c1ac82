package com.yhl.scp.mrp.freestorage.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.freestorage.convertor.ProFreeStorageConvertor;
import com.yhl.scp.mrp.freestorage.domain.entity.ProFreeStorageDO;
import com.yhl.scp.mrp.freestorage.domain.service.ProFreeStorageDomainService;
import com.yhl.scp.mrp.freestorage.dto.ProFreeStorageDTO;
import com.yhl.scp.mrp.freestorage.infrastructure.dao.ProFreeStorageDao;
import com.yhl.scp.mrp.freestorage.infrastructure.po.ProFreeStoragePO;
import com.yhl.scp.mrp.freestorage.service.ProFreeStorageService;
import com.yhl.scp.mrp.freestorage.vo.ProFreeStorageVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>ProFreeStorageServiceImpl</code>
 * <p>
 * 免堆期应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 10:59:45
 */
@Slf4j
@Service
public class ProFreeStorageServiceImpl extends AbstractService implements ProFreeStorageService {

    @Resource
    private ProFreeStorageDao proFreeStorageDao;

    @Resource
    private ProFreeStorageDomainService proFreeStorageDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ProFreeStorageDTO proFreeStorageDTO) {
        // 0.数据转换
        ProFreeStorageDO proFreeStorageDO = ProFreeStorageConvertor.INSTANCE.dto2Do(proFreeStorageDTO);
        ProFreeStoragePO proFreeStoragePO = ProFreeStorageConvertor.INSTANCE.dto2Po(proFreeStorageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        proFreeStorageDomainService.validation(proFreeStorageDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(proFreeStoragePO);
        proFreeStorageDao.insert(proFreeStoragePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ProFreeStorageDTO proFreeStorageDTO) {
        // 0.数据转换
        ProFreeStorageDO proFreeStorageDO = ProFreeStorageConvertor.INSTANCE.dto2Do(proFreeStorageDTO);
        ProFreeStoragePO proFreeStoragePO = ProFreeStorageConvertor.INSTANCE.dto2Po(proFreeStorageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        proFreeStorageDomainService.validation(proFreeStorageDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(proFreeStoragePO);
        proFreeStorageDao.update(proFreeStoragePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProFreeStorageDTO> list) {
        List<ProFreeStoragePO> newList = ProFreeStorageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        proFreeStorageDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProFreeStorageDTO> list) {
        List<ProFreeStoragePO> newList = ProFreeStorageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        proFreeStorageDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return proFreeStorageDao.deleteBatch(idList);
        }
        return proFreeStorageDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProFreeStorageVO selectByPrimaryKey(String id) {
        ProFreeStoragePO po = proFreeStorageDao.selectByPrimaryKey(id);
        return ProFreeStorageConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRO_FREE_STORAGE")
    public List<ProFreeStorageVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRO_FREE_STORAGE")
    public List<ProFreeStorageVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProFreeStorageVO> dataList = proFreeStorageDao.selectByCondition(sortParam, queryCriteriaParam);
        ProFreeStorageServiceImpl target = springBeanUtils.getBean(ProFreeStorageServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProFreeStorageVO> selectByParams(Map<String, Object> params) {
        List<ProFreeStoragePO> list = proFreeStorageDao.selectByParams(params);
        return ProFreeStorageConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProFreeStorageVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRO_FREE_STORAGE.getCode();
    }

    @Override
    public List<ProFreeStorageVO> invocation(List<ProFreeStorageVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
