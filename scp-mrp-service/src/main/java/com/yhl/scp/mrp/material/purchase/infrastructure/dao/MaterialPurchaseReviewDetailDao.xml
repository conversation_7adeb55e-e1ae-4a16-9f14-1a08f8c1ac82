<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDetailDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO">
        <!--@Table mrp_material_purchase_review_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_purchase_review_version_id" jdbcType="VARCHAR"
                property="materialPurchaseReviewVersionId"/>
        <result column="review_type" jdbcType="VARCHAR" property="reviewType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_factory_code" jdbcType="VARCHAR" property="productFactoryCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="purchase_lot" jdbcType="VARCHAR" property="purchaseLot"/>
        <result column="min_order_quantity" jdbcType="VARCHAR" property="minOrderQuantity"/>
        <result column="review_comments" jdbcType="VARCHAR" property="reviewComments"/>
        <result column="approve" jdbcType="VARCHAR" property="approve"/>
        <result column="to_examine" jdbcType="VARCHAR" property="toExamine"/>
        <result column="tabulation" jdbcType="VARCHAR" property="tabulation"/>
        <result column="consistence_demand_forecast_version_id" jdbcType="VARCHAR"
                property="consistenceDemandForecastVersionId"/>
        <result column="forecast_provider" jdbcType="VARCHAR" property="forecastProvider"/>
        <result column="line_description" jdbcType="VARCHAR" property="lineDescription"/>
        <result column="vehicle_model_eop_date" jdbcType="VARCHAR" property="vehicleModelEopDate"/>
        <result column="vehicle_model" jdbcType="VARCHAR" property="vehicleModel"/>
        <result column="one_year_month_value" jdbcType="VARCHAR" property="oneYearMonthValue"/>
        <result column="two_year_month_value" jdbcType="VARCHAR" property="twoYearMonthValue"/>
        <result column="three_year_month_value" jdbcType="VARCHAR" property="threeYearMonthValue"/>
        <result column="four_year_month_value" jdbcType="VARCHAR" property="fourYearMonthValue"/>
        <result column="five_year_month_value" jdbcType="VARCHAR" property="fiveYearMonthValue"/>
        <result column="six_year_month_value" jdbcType="VARCHAR" property="sixYearMonthValue"/>
        <result column="seven_year_month_value" jdbcType="VARCHAR" property="sevenYearMonthValue"/>
        <result column="eight_year_month_value" jdbcType="VARCHAR" property="eightYearMonthValue"/>
        <result column="nine_year_month_value" jdbcType="VARCHAR" property="nineYearMonthValue"/>
        <result column="ten_year_month_value" jdbcType="VARCHAR" property="tenYearMonthValue"/>
        <result column="eleven_year_month_value" jdbcType="VARCHAR" property="elevenYearMonthValue"/>
        <result column="twelve_year_month_value" jdbcType="VARCHAR" property="twelveYearMonthValue"/>
        <result column="appoint_year_month" jdbcType="VARCHAR" property="appointYearMonth"/>
        <result column="single_piece_area" jdbcType="VARCHAR" property="singlePieceArea"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_purchase_review_version_id,review_type,product_code,product_name,product_factory_code,vehicle_model_code,oem_code,oem_name,supplier_code,purchase_lot,min_order_quantity,review_comments,approve,to_examine,tabulation,consistence_demand_forecast_version_id,forecast_provider,line_description,vehicle_model_eop_date,vehicle_model,one_year_month_value,two_year_month_value,three_year_month_value,four_year_month_value,five_year_month_value,six_year_month_value,seven_year_month_value,eight_year_month_value,nine_year_month_value,ten_year_month_value,eleven_year_month_value,twelve_year_month_value,appoint_year_month,single_piece_area,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPurchaseReviewVersionId != null and params.materialPurchaseReviewVersionId != ''">
                and material_purchase_review_version_id = #{params.materialPurchaseReviewVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.reviewType != null and params.reviewType != ''">
                and review_type = #{params.reviewType,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
                and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemName != null and params.oemName != ''">
                and oem_name = #{params.oemName,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLot != null">
                and purchase_lot = #{params.purchaseLot,jdbcType=VARCHAR}
            </if>
            <if test="params.minOrderQuantity != null">
                and min_order_quantity = #{params.minOrderQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.reviewComments != null and params.reviewComments != ''">
                and review_comments = #{params.reviewComments,jdbcType=VARCHAR}
            </if>
            <if test="params.approve != null and params.approve != ''">
                and approve = #{params.approve,jdbcType=VARCHAR}
            </if>
            <if test="params.toExamine != null and params.toExamine != ''">
                and to_examine = #{params.toExamine,jdbcType=VARCHAR}
            </if>
            <if test="params.tabulation != null and params.tabulation != ''">
                and tabulation = #{params.tabulation,jdbcType=VARCHAR}
            </if>
            <if test="params.consistenceDemandForecastVersionId != null and params.consistenceDemandForecastVersionId != ''">
                and consistence_demand_forecast_version_id =
                #{params.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastProvider != null and params.forecastProvider != ''">
                and forecast_provider = #{params.forecastProvider,jdbcType=VARCHAR}
            </if>
            <if test="params.lineDescription != null and params.lineDescription != ''">
                and line_description = #{params.lineDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelEopDate != null and params.vehicleModelEopDate != ''">
                and vehicle_model_eop_date = #{params.vehicleModelEopDate,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModel != null and params.vehicleModel != ''">
                and vehicle_model = #{params.vehicleModel,jdbcType=VARCHAR}
            </if>
            <if test="params.oneYearMonthValue != null and params.oneYearMonthValue != ''">
                and one_year_month_value = #{params.oneYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.twoYearMonthValue != null and params.twoYearMonthValue != ''">
                and two_year_month_value = #{params.twoYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.threeYearMonthValue != null and params.threeYearMonthValue != ''">
                and three_year_month_value = #{params.threeYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.fourYearMonthValue != null and params.fourYearMonthValue != ''">
                and four_year_month_value = #{params.fourYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.fiveYearMonthValue != null and params.fiveYearMonthValue != ''">
                and five_year_month_value = #{params.fiveYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.sixYearMonthValue != null and params.sixYearMonthValue != ''">
                and six_year_month_value = #{params.sixYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.sevenYearMonthValue != null and params.sevenYearMonthValue != ''">
                and seven_year_month_value = #{params.sevenYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.eightYearMonthValue != null and params.eightYearMonthValue != ''">
                and eight_year_month_value = #{params.eightYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.nineYearMonthValue != null and params.nineYearMonthValue != ''">
                and nine_year_month_value = #{params.nineYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.tenYearMonthValue != null and params.tenYearMonthValue != ''">
                and ten_year_month_value = #{params.tenYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.elevenYearMonthValue != null and params.elevenYearMonthValue != ''">
                and eleven_year_month_value = #{params.elevenYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.twelveYearMonthValue != null and params.twelveYearMonthValue != ''">
                and twelve_year_month_value = #{params.twelveYearMonthValue,jdbcType=VARCHAR}
            </if>
            <if test="params.appointYearMonth != null and params.appointYearMonth != ''">
                and appoint_year_month = #{params.appointYearMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.singlePieceArea != null and params.singlePieceArea != ''">
                and single_piece_area = #{params.singlePieceArea,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_purchase_review_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_purchase_review_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_purchase_review_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_purchase_review_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_purchase_review_detail(
        id,
        material_purchase_review_version_id,
        review_type,
        product_code,
        product_name,
        product_factory_code,
        vehicle_model_code,
        oem_code,
        oem_name,
        supplier_code,
        purchase_lot,
        min_order_quantity,
        review_comments,
        approve,
        to_examine,
        tabulation,
        consistence_demand_forecast_version_id,
        forecast_provider,
        line_description,
        vehicle_model_eop_date,
        vehicle_model,
        one_year_month_value,
        two_year_month_value,
        three_year_month_value,
        four_year_month_value,
        five_year_month_value,
        six_year_month_value,
        seven_year_month_value,
        eight_year_month_value,
        nine_year_month_value,
        ten_year_month_value,
        eleven_year_month_value,
        twelve_year_month_value,
        appoint_year_month,
        single_piece_area,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPurchaseReviewVersionId,jdbcType=VARCHAR},
        #{reviewType,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{productFactoryCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{purchaseLot,jdbcType=VARCHAR},
        #{minOrderQuantity,jdbcType=VARCHAR},
        #{reviewComments,jdbcType=VARCHAR},
        #{approve,jdbcType=VARCHAR},
        #{toExamine,jdbcType=VARCHAR},
        #{tabulation,jdbcType=VARCHAR},
        #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        #{forecastProvider,jdbcType=VARCHAR},
        #{lineDescription,jdbcType=VARCHAR},
        #{vehicleModelEopDate,jdbcType=VARCHAR},
        #{vehicleModel,jdbcType=VARCHAR},
        #{oneYearMonthValue,jdbcType=VARCHAR},
        #{twoYearMonthValue,jdbcType=VARCHAR},
        #{threeYearMonthValue,jdbcType=VARCHAR},
        #{fourYearMonthValue,jdbcType=VARCHAR},
        #{fiveYearMonthValue,jdbcType=VARCHAR},
        #{sixYearMonthValue,jdbcType=VARCHAR},
        #{sevenYearMonthValue,jdbcType=VARCHAR},
        #{eightYearMonthValue,jdbcType=VARCHAR},
        #{nineYearMonthValue,jdbcType=VARCHAR},
        #{tenYearMonthValue,jdbcType=VARCHAR},
        #{elevenYearMonthValue,jdbcType=VARCHAR},
        #{twelveYearMonthValue,jdbcType=VARCHAR},
        #{appointYearMonth,jdbcType=VARCHAR},
        #{singlePieceArea,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO">
        insert into mrp_material_purchase_review_detail(id,
                                                        material_purchase_review_version_id,
                                                        review_type,
                                                        product_code,
                                                        product_name,
                                                        product_factory_code,
                                                        vehicle_model_code,
                                                        oem_code,
                                                        oem_name,
                                                        supplier_code,
                                                        purchase_lot,
                                                        min_order_quantity,
                                                        review_comments,
                                                        approve,
                                                        to_examine,
                                                        tabulation,
                                                        consistence_demand_forecast_version_id,
                                                        forecast_provider,
                                                        line_description,
                                                        vehicle_model_eop_date,
                                                        vehicle_model,
                                                        one_year_month_value,
                                                        two_year_month_value,
                                                        three_year_month_value,
                                                        four_year_month_value,
                                                        five_year_month_value,
                                                        six_year_month_value,
                                                        seven_year_month_value,
                                                        eight_year_month_value,
                                                        nine_year_month_value,
                                                        ten_year_month_value,
                                                        eleven_year_month_value,
                                                        twelve_year_month_value,
                                                        appoint_year_month,
                                                        single_piece_area,
                                                        remark,
                                                        enabled,
                                                        creator,
                                                        create_time,
                                                        modifier,
                                                        modify_time,
                                                        version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialPurchaseReviewVersionId,jdbcType=VARCHAR},
                #{reviewType,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productName,jdbcType=VARCHAR},
                #{productFactoryCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{oemName,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{purchaseLot,jdbcType=VARCHAR},
                #{minOrderQuantity,jdbcType=VARCHAR},
                #{reviewComments,jdbcType=VARCHAR},
                #{approve,jdbcType=VARCHAR},
                #{toExamine,jdbcType=VARCHAR},
                #{tabulation,jdbcType=VARCHAR},
                #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
                #{forecastProvider,jdbcType=VARCHAR},
                #{lineDescription,jdbcType=VARCHAR},
                #{vehicleModelEopDate,jdbcType=VARCHAR},
                #{vehicleModel,jdbcType=VARCHAR},
                #{oneYearMonthValue,jdbcType=VARCHAR},
                #{twoYearMonthValue,jdbcType=VARCHAR},
                #{threeYearMonthValue,jdbcType=VARCHAR},
                #{fourYearMonthValue,jdbcType=VARCHAR},
                #{fiveYearMonthValue,jdbcType=VARCHAR},
                #{sixYearMonthValue,jdbcType=VARCHAR},
                #{sevenYearMonthValue,jdbcType=VARCHAR},
                #{eightYearMonthValue,jdbcType=VARCHAR},
                #{nineYearMonthValue,jdbcType=VARCHAR},
                #{tenYearMonthValue,jdbcType=VARCHAR},
                #{elevenYearMonthValue,jdbcType=VARCHAR},
                #{twelveYearMonthValue,jdbcType=VARCHAR},
                #{appointYearMonth,jdbcType=VARCHAR},
                #{singlePieceArea,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_purchase_review_detail(
        id,
        material_purchase_review_version_id,
        review_type,
        product_code,
        product_name,
        product_factory_code,
        vehicle_model_code,
        oem_code,
        oem_name,
        supplier_code,
        purchase_lot,
        min_order_quantity,
        review_comments,
        approve,
        to_examine,
        tabulation,
        consistence_demand_forecast_version_id,
        forecast_provider,
        line_description,
        vehicle_model_eop_date,
        vehicle_model,
        one_year_month_value,
        two_year_month_value,
        three_year_month_value,
        four_year_month_value,
        five_year_month_value,
        six_year_month_value,
        seven_year_month_value,
        eight_year_month_value,
        nine_year_month_value,
        ten_year_month_value,
        eleven_year_month_value,
        twelve_year_month_value,
        appoint_year_month,
        single_piece_area,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPurchaseReviewVersionId,jdbcType=VARCHAR},
            #{entity.reviewType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.oemName,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.minOrderQuantity,jdbcType=VARCHAR},
            #{entity.reviewComments,jdbcType=VARCHAR},
            #{entity.approve,jdbcType=VARCHAR},
            #{entity.toExamine,jdbcType=VARCHAR},
            #{entity.tabulation,jdbcType=VARCHAR},
            #{entity.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
            #{entity.forecastProvider,jdbcType=VARCHAR},
            #{entity.lineDescription,jdbcType=VARCHAR},
            #{entity.vehicleModelEopDate,jdbcType=VARCHAR},
            #{entity.vehicleModel,jdbcType=VARCHAR},
            #{entity.oneYearMonthValue,jdbcType=VARCHAR},
            #{entity.twoYearMonthValue,jdbcType=VARCHAR},
            #{entity.threeYearMonthValue,jdbcType=VARCHAR},
            #{entity.fourYearMonthValue,jdbcType=VARCHAR},
            #{entity.fiveYearMonthValue,jdbcType=VARCHAR},
            #{entity.sixYearMonthValue,jdbcType=VARCHAR},
            #{entity.sevenYearMonthValue,jdbcType=VARCHAR},
            #{entity.eightYearMonthValue,jdbcType=VARCHAR},
            #{entity.nineYearMonthValue,jdbcType=VARCHAR},
            #{entity.tenYearMonthValue,jdbcType=VARCHAR},
            #{entity.elevenYearMonthValue,jdbcType=VARCHAR},
            #{entity.twelveYearMonthValue,jdbcType=VARCHAR},
            #{entity.appointYearMonth,jdbcType=VARCHAR},
            #{entity.singlePieceArea,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_purchase_review_detail(
        id,
        material_purchase_review_version_id,
        review_type,
        product_code,
        product_name,
        product_factory_code,
        vehicle_model_code,
        oem_code,
        oem_name,
        supplier_code,
        purchase_lot,
        min_order_quantity,
        review_comments,
        approve,
        to_examine,
        tabulation,
        consistence_demand_forecast_version_id,
        forecast_provider,
        line_description,
        vehicle_model_eop_date,
        vehicle_model,
        one_year_month_value,
        two_year_month_value,
        three_year_month_value,
        four_year_month_value,
        five_year_month_value,
        six_year_month_value,
        seven_year_month_value,
        eight_year_month_value,
        nine_year_month_value,
        ten_year_month_value,
        eleven_year_month_value,
        twelve_year_month_value,
        appoint_year_month,
        single_piece_area,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPurchaseReviewVersionId,jdbcType=VARCHAR},
            #{entity.reviewType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.oemName,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.purchaseLot,jdbcType=VARCHAR},
            #{entity.minOrderQuantity,jdbcType=VARCHAR},
            #{entity.reviewComments,jdbcType=VARCHAR},
            #{entity.approve,jdbcType=VARCHAR},
            #{entity.toExamine,jdbcType=VARCHAR},
            #{entity.tabulation,jdbcType=VARCHAR},
            #{entity.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
            #{entity.forecastProvider,jdbcType=VARCHAR},
            #{entity.lineDescription,jdbcType=VARCHAR},
            #{entity.vehicleModelEopDate,jdbcType=VARCHAR},
            #{entity.vehicleModel,jdbcType=VARCHAR},
            #{entity.oneYearMonthValue,jdbcType=VARCHAR},
            #{entity.twoYearMonthValue,jdbcType=VARCHAR},
            #{entity.threeYearMonthValue,jdbcType=VARCHAR},
            #{entity.fourYearMonthValue,jdbcType=VARCHAR},
            #{entity.fiveYearMonthValue,jdbcType=VARCHAR},
            #{entity.sixYearMonthValue,jdbcType=VARCHAR},
            #{entity.sevenYearMonthValue,jdbcType=VARCHAR},
            #{entity.eightYearMonthValue,jdbcType=VARCHAR},
            #{entity.nineYearMonthValue,jdbcType=VARCHAR},
            #{entity.tenYearMonthValue,jdbcType=VARCHAR},
            #{entity.elevenYearMonthValue,jdbcType=VARCHAR},
            #{entity.twelveYearMonthValue,jdbcType=VARCHAR},
            #{entity.appointYearMonth,jdbcType=VARCHAR},
            #{entity.singlePieceArea,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO">
        update mrp_material_purchase_review_detail
        set material_purchase_review_version_id    = #{materialPurchaseReviewVersionId,jdbcType=VARCHAR},
            review_type                            = #{reviewType,jdbcType=VARCHAR},
            product_code                           = #{productCode,jdbcType=VARCHAR},
            product_name                           = #{productName,jdbcType=VARCHAR},
            product_factory_code                   = #{productFactoryCode,jdbcType=VARCHAR},
            vehicle_model_code                     = #{vehicleModelCode,jdbcType=VARCHAR},
            oem_code                               = #{oemCode,jdbcType=VARCHAR},
            oem_name                               = #{oemName,jdbcType=VARCHAR},
            supplier_code                          = #{supplierCode,jdbcType=VARCHAR},
            purchase_lot                           = #{purchaseLot,jdbcType=VARCHAR},
            min_order_quantity                     = #{minOrderQuantity,jdbcType=VARCHAR},
            review_comments                        = #{reviewComments,jdbcType=VARCHAR},
            approve                                = #{approve,jdbcType=VARCHAR},
            to_examine                             = #{toExamine,jdbcType=VARCHAR},
            tabulation                             = #{tabulation,jdbcType=VARCHAR},
            consistence_demand_forecast_version_id = #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
            forecast_provider                      = #{forecastProvider,jdbcType=VARCHAR},
            line_description                       = #{lineDescription,jdbcType=VARCHAR},
            vehicle_model_eop_date                 = #{vehicleModelEopDate,jdbcType=VARCHAR},
            vehicle_model                          = #{vehicleModel,jdbcType=VARCHAR},
            one_year_month_value                   = #{oneYearMonthValue,jdbcType=VARCHAR},
            two_year_month_value                   = #{twoYearMonthValue,jdbcType=VARCHAR},
            three_year_month_value                 = #{threeYearMonthValue,jdbcType=VARCHAR},
            four_year_month_value                  = #{fourYearMonthValue,jdbcType=VARCHAR},
            five_year_month_value                  = #{fiveYearMonthValue,jdbcType=VARCHAR},
            six_year_month_value                   = #{sixYearMonthValue,jdbcType=VARCHAR},
            seven_year_month_value                 = #{sevenYearMonthValue,jdbcType=VARCHAR},
            eight_year_month_value                 = #{eightYearMonthValue,jdbcType=VARCHAR},
            nine_year_month_value                  = #{nineYearMonthValue,jdbcType=VARCHAR},
            ten_year_month_value                   = #{tenYearMonthValue,jdbcType=VARCHAR},
            eleven_year_month_value                = #{elevenYearMonthValue,jdbcType=VARCHAR},
            twelve_year_month_value                = #{twelveYearMonthValue,jdbcType=VARCHAR},
            appoint_year_month                     = #{appointYearMonth,jdbcType=VARCHAR},
            single_piece_area                      = #{singlePieceArea,jdbcType=VARCHAR},
            remark                                 = #{remark,jdbcType=VARCHAR},
            enabled                                = #{enabled,jdbcType=VARCHAR},
            modifier                               = #{modifier,jdbcType=VARCHAR},
            modify_time                            = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                          = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO">
        update mrp_material_purchase_review_detail
        <set>
            <if test="item.materialPurchaseReviewVersionId != null and item.materialPurchaseReviewVersionId != ''">
                material_purchase_review_version_id = #{item.materialPurchaseReviewVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.reviewType != null and item.reviewType != ''">
                review_type = #{item.reviewType,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseLot != null">
                purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
            </if>
            <if test="item.minOrderQuantity != null">
                min_order_quantity = #{item.minOrderQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.reviewComments != null and item.reviewComments != ''">
                review_comments = #{item.reviewComments,jdbcType=VARCHAR},
            </if>
            <if test="item.approve != null and item.approve != ''">
                approve = #{item.approve,jdbcType=VARCHAR},
            </if>
            <if test="item.toExamine != null and item.toExamine != ''">
                to_examine = #{item.toExamine,jdbcType=VARCHAR},
            </if>
            <if test="item.tabulation != null and item.tabulation != ''">
                tabulation = #{item.tabulation,jdbcType=VARCHAR},
            </if>
            <if test="item.consistenceDemandForecastVersionId != null and item.consistenceDemandForecastVersionId != ''">
                consistence_demand_forecast_version_id = #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastProvider != null and item.forecastProvider != ''">
                forecast_provider = #{item.forecastProvider,jdbcType=VARCHAR},
            </if>
            <if test="item.lineDescription != null and item.lineDescription != ''">
                line_description = #{item.lineDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelEopDate != null and item.vehicleModelEopDate != ''">
                vehicle_model_eop_date = #{item.vehicleModelEopDate,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModel != null and item.vehicleModel != ''">
                vehicle_model = #{item.vehicleModel,jdbcType=VARCHAR},
            </if>
            <if test="item.oneYearMonthValue != null and item.oneYearMonthValue != ''">
                one_year_month_value = #{item.oneYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.twoYearMonthValue != null and item.twoYearMonthValue != ''">
                two_year_month_value = #{item.twoYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.threeYearMonthValue != null and item.threeYearMonthValue != ''">
                three_year_month_value = #{item.threeYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.fourYearMonthValue != null and item.fourYearMonthValue != ''">
                four_year_month_value = #{item.fourYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.fiveYearMonthValue != null and item.fiveYearMonthValue != ''">
                five_year_month_value = #{item.fiveYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.sixYearMonthValue != null and item.sixYearMonthValue != ''">
                six_year_month_value = #{item.sixYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.sevenYearMonthValue != null and item.sevenYearMonthValue != ''">
                seven_year_month_value = #{item.sevenYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.eightYearMonthValue != null and item.eightYearMonthValue != ''">
                eight_year_month_value = #{item.eightYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.nineYearMonthValue != null and item.nineYearMonthValue != ''">
                nine_year_month_value = #{item.nineYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.tenYearMonthValue != null and item.tenYearMonthValue != ''">
                ten_year_month_value = #{item.tenYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.elevenYearMonthValue != null and item.elevenYearMonthValue != ''">
                eleven_year_month_value = #{item.elevenYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.twelveYearMonthValue != null and item.twelveYearMonthValue != ''">
                twelve_year_month_value = #{item.twelveYearMonthValue,jdbcType=VARCHAR},
            </if>
            <if test="item.appointYearMonth != null and item.appointYearMonth != ''">
                appoint_year_month = #{item.appointYearMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.singlePieceArea != null and item.singlePieceArea != ''">
                single_piece_area = #{item.singlePieceArea,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_purchase_review_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_purchase_review_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPurchaseReviewVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="review_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.reviewType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productFactoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_lot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseLot,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_order_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minOrderQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="review_comments = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.reviewComments,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approve = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.approve,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="to_examine = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toExamine,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tabulation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tabulation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="consistence_demand_forecast_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_provider = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastProvider,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_eop_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelEopDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="one_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oneYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="two_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.twoYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="three_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.threeYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="four_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fourYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="five_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fiveYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="six_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sixYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="seven_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sevenYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="eight_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eightYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="nine_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nineYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ten_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tenYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="eleven_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.elevenYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="twelve_year_month_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.twelveYearMonthValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_year_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.appointYearMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="single_piece_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.singlePieceArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_purchase_review_detail
            <set>
                <if test="item.materialPurchaseReviewVersionId != null and item.materialPurchaseReviewVersionId != ''">
                    material_purchase_review_version_id = #{item.materialPurchaseReviewVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.reviewType != null and item.reviewType != ''">
                    review_type = #{item.reviewType,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                    product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.oemName != null and item.oemName != ''">
                    oem_name = #{item.oemName,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseLot != null">
                    purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
                </if>
                <if test="item.minOrderQuantity != null">
                    min_order_quantity = #{item.minOrderQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.reviewComments != null and item.reviewComments != ''">
                    review_comments = #{item.reviewComments,jdbcType=VARCHAR},
                </if>
                <if test="item.approve != null and item.approve != ''">
                    approve = #{item.approve,jdbcType=VARCHAR},
                </if>
                <if test="item.toExamine != null and item.toExamine != ''">
                    to_examine = #{item.toExamine,jdbcType=VARCHAR},
                </if>
                <if test="item.tabulation != null and item.tabulation != ''">
                    tabulation = #{item.tabulation,jdbcType=VARCHAR},
                </if>
                <if test="item.consistenceDemandForecastVersionId != null and item.consistenceDemandForecastVersionId != ''">
                    consistence_demand_forecast_version_id =
                    #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.forecastProvider != null and item.forecastProvider != ''">
                    forecast_provider = #{item.forecastProvider,jdbcType=VARCHAR},
                </if>
                <if test="item.lineDescription != null and item.lineDescription != ''">
                    line_description = #{item.lineDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelEopDate != null and item.vehicleModelEopDate != ''">
                    vehicle_model_eop_date = #{item.vehicleModelEopDate,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModel != null and item.vehicleModel != ''">
                    vehicle_model = #{item.vehicleModel,jdbcType=VARCHAR},
                </if>
                <if test="item.oneYearMonthValue != null and item.oneYearMonthValue != ''">
                    one_year_month_value = #{item.oneYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.twoYearMonthValue != null and item.twoYearMonthValue != ''">
                    two_year_month_value = #{item.twoYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.threeYearMonthValue != null and item.threeYearMonthValue != ''">
                    three_year_month_value = #{item.threeYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.fourYearMonthValue != null and item.fourYearMonthValue != ''">
                    four_year_month_value = #{item.fourYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.fiveYearMonthValue != null and item.fiveYearMonthValue != ''">
                    five_year_month_value = #{item.fiveYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.sixYearMonthValue != null and item.sixYearMonthValue != ''">
                    six_year_month_value = #{item.sixYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.sevenYearMonthValue != null and item.sevenYearMonthValue != ''">
                    seven_year_month_value = #{item.sevenYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.eightYearMonthValue != null and item.eightYearMonthValue != ''">
                    eight_year_month_value = #{item.eightYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.nineYearMonthValue != null and item.nineYearMonthValue != ''">
                    nine_year_month_value = #{item.nineYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.tenYearMonthValue != null and item.tenYearMonthValue != ''">
                    ten_year_month_value = #{item.tenYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.elevenYearMonthValue != null and item.elevenYearMonthValue != ''">
                    eleven_year_month_value = #{item.elevenYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.twelveYearMonthValue != null and item.twelveYearMonthValue != ''">
                    twelve_year_month_value = #{item.twelveYearMonthValue,jdbcType=VARCHAR},
                </if>
                <if test="item.appointYearMonth != null and item.appointYearMonth != ''">
                    appoint_year_month = #{item.appointYearMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.singlePieceArea != null and item.singlePieceArea != ''">
                    single_piece_area = #{item.singlePieceArea,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_purchase_review_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_purchase_review_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
