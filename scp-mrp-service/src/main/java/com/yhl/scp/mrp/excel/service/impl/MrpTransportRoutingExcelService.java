package com.yhl.scp.mrp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.YesOrNoUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDetailDTO;
import com.yhl.scp.dfp.transport.vo.TransportResourceVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.freestorage.dto.ProFreeStorageDTO;
import com.yhl.scp.mrp.freestorage.infrastructure.po.ProFreeStoragePO;
import com.yhl.scp.mrp.supplier.dto.MaterialSupplierPurchaseDTO;
import com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO;
import com.yhl.scp.mrp.transport.convertor.TransportRoutingConvertor;
import com.yhl.scp.mrp.transport.dto.TransportRoutingDTO;
import com.yhl.scp.mrp.transport.infrastructure.dao.TransportRoutingDao;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportRoutingBasicPO;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportRoutingPO;
import com.yhl.scp.mrp.transport.service.TransportRoutingService;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>TransportRoutingExcelService</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 15:53:54
 */
@Service
public class MrpTransportRoutingExcelService extends AbstractExcelService<TransportRoutingDTO, TransportRoutingPO, TransportRoutingVO> {

    @Resource
    private TransportRoutingService transportRoutingService;

    @Resource
    private TransportRoutingDao transportRoutingDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private IpsFeign ipsFeign;

    @Override
    public BaseDao<TransportRoutingPO, TransportRoutingVO> getBaseDao() {
        return transportRoutingDao;
    }

    @Override
    public Function<TransportRoutingDTO, TransportRoutingPO> getDTO2POConvertor() {
        return TransportRoutingConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<TransportRoutingDTO> getDTOClass() {
        return TransportRoutingDTO.class;
    }

    @Override
    public BaseService<TransportRoutingDTO, TransportRoutingVO> getBaseService() {
        return transportRoutingService;
    }

    @Override
    protected void fillIdForUpdateData(List<TransportRoutingDTO> list, Map<String, TransportRoutingPO> map) {
        for (TransportRoutingDTO dto : list) {
            TransportRoutingPO transportRoutingPO = map.get(dto.getRoutingCode());
            if (transportRoutingPO != null){
                dto.setId(transportRoutingPO.getId());
            }
        }
    }
    @Override
    protected void specialVerification(ImportAnalysisResultHolder<TransportRoutingDTO, TransportRoutingPO> resultHolder, ImportContext importContext) {
        List<TransportRoutingDTO> insertList = resultHolder.getInsertList();
        List<TransportRoutingDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    @Override
    protected ImportRelatedDataHolder<TransportRoutingPO> prepareData(List<TransportRoutingDTO> list) {
        List<TransportRoutingPO> existingData = transportRoutingDao.selectByParams(new HashMap<>(2));
        Map<String, TransportRoutingPO> codeToPOMap = existingData.stream().collect(Collectors
                .toMap(TransportRoutingBasicPO::getRoutingCode, Function.identity(), (v1, v2) -> v1));
        String scenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId()).getData();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        List<NewStockPointVO> stockPointVOS = newMdsFeign.selectAllStockPoint(scenario);
        List<SimpleVO> stockVOS = CollectionUtils.isEmpty(stockPointVOS) ?
                Lists.newArrayList() :
                stockPointVOS.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getId());
                    simpleVO.setCode(x.getStockPointName());
                    return simpleVO;
                }).collect(Collectors.toList());
        List<CollectionValueVO> transportTypeList = ipsFeign.getByCollectionCode("TRANSPORT_TYPE");
        List<SimpleVO> transportTypeListVOS = CollectionUtils.isEmpty(transportTypeList) ?
                Lists.newArrayList() :
                transportTypeList.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getCollectionValue());
                    simpleVO.setCode(x.getValueMeaning());
                    return simpleVO;
                }).collect(Collectors.toList());
        List<SimpleVO> enabledListVOS = ListUtil.of(new SimpleVO(YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getDesc(),
                YesOrNoEnum.YES.getDesc()), new SimpleVO(YesOrNoEnum.NO.getCode(), YesOrNoEnum.NO.getDesc(),YesOrNoEnum.NO.getDesc()));
        List<String> uniqueKeys = ListUtil.of("routingCode");
        List<String> foreignKeys = ListUtil.of("originStockPointId", "destinStockPointId", "transportType","enabled");

        foreignDataMap.put("originStockPointId", stockVOS);
        foreignDataMap.put("destinStockPointId", stockVOS);
        foreignDataMap.put("transportType", transportTypeListVOS);
        foreignDataMap.put("enabled", enabledListVOS);
        return ImportRelatedDataHolder.<TransportRoutingPO>builder()
                .existingData(existingData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    private void verifyPaternity(List<TransportRoutingDTO> checkList, List<DataImportInfo> importLogList) {

        String scenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId()).getData();
        List<NewStockPointVO> stockPointVOS = newMdsFeign.selectAllStockPoint(scenario);
        Map<String, NewStockPointVO> newStockPointVOMap = stockPointVOS.stream()
                .collect(Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));
        List<CollectionValueVO> transportTypeList = ipsFeign.getByCollectionCode("TRANSPORT_TYPE");
        Map<String, CollectionValueVO> transportTypeMap = transportTypeList.stream()
                .collect(Collectors.toMap(CollectionValueVO::getCollectionValue, Function.identity(), (v1, v2) -> v1));
        Iterator<TransportRoutingDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            TransportRoutingDTO transportRoutingDTO = iterator.next();
            if (isFieldEmpty(transportRoutingDTO.getRoutingCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[路径编码]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[路径编码]未填写");
//                continue;
            }
            if (isFieldEmpty(String.valueOf(transportRoutingDTO.getEffectiveTime()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[生效时间]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[生效时间]未填写");
            }
            if (isFieldEmpty(String.valueOf(transportRoutingDTO.getExpiryTime()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[失效时间]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[失效时间]未填写");
            }
            if (isFieldEmpty(transportRoutingDTO.getOriginStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[起点库存点]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[起点库存点]未填写");
            }
            if(!newStockPointVOMap.containsKey(transportRoutingDTO.getOriginStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[起点库存点]不存在");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[起点库存点]不存在");

            }
            if (isFieldEmpty(transportRoutingDTO.getDestinStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[终点库存点]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[终点库存点]未填写");

            }
            if(!newStockPointVOMap.containsKey(transportRoutingDTO.getDestinStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[终点库存点]不存在");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[终点库存点]不存在");

            }
            if (Objects.isNull(transportRoutingDTO.getPriority())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[优先级]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[优先级]未填写");

            }
            if (isFieldEmpty(transportRoutingDTO.getTransportType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[运输方式]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[运输方式]未填写");

            }
            if(!transportTypeMap.containsKey(transportRoutingDTO.getTransportType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[运输方式]不存在");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[运输方式]不存在");

            }
            if (isFieldEmpty(transportRoutingDTO.getEnabled())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[是否启用]未填写");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[是否启用]未填写");

            }

            if (!YesOrNoEnum.YES.getCode().equals(transportRoutingDTO.getEnabled()) && !YesOrNoEnum.NO.getCode().equals(transportRoutingDTO.getEnabled())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportRoutingDTO.getRowIndex() + ";[是否启用]不存在");
                dataImportInfo.setDisplayIndex(transportRoutingDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportRoutingDTO.getRowIndex() + ";[是否启用]不存在");

            }
        }
    }
    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

}
