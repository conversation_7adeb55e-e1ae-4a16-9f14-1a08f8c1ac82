package com.yhl.scp.mrp.material.purchase.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewVersionPO</code>
 * <p>
 * 材料采购评审版本PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
public class MaterialPurchaseReviewVersionPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 982882473760820874L;

    /**
     * 版本编码
     */
    private String versionCode;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
