package com.yhl.scp.mrp.material.purchase.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewDetailDO</code>
 * <p>
 * 材料采购评审明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:06:05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPurchaseReviewDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 769599144703424995L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 版本id
     */
    private String materialPurchaseReviewVersionId;
    /**
     * 评审类型
     */
    private String reviewType;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂编码
     */
    private String oemName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 最小起订量
     */
    private BigDecimal minOrderQuantity;
    /**
     * 评审意见
     */
    private String reviewComments;
    /**
     * 批准
     */
    private String approve;
    /**
     * 审核
     */
    private String toExamine;
    /**
     * 制表
     */
    private String tabulation;
    /**
     * 一致性需求预测版本ID
     */
    private String consistenceDemandForecastVersionId;
    /**
     * 预测提供者
     */
    private String forecastProvider;
    /**
     * 行描述
     */
    private String lineDescription;
    /**
     * 车型EOP时间
     */
    private String vehicleModelEopDate;
    /**
     * 主机厂车型
     */
    private String vehicleModel;
    /**
     * 年月值（一）
     */
    private String oneYearMonthValue;
    /**
     * 年月值（二）
     */
    private String twoYearMonthValue;
    /**
     * 年月值（三）
     */
    private String threeYearMonthValue;
    /**
     * 年月值（四）
     */
    private String fourYearMonthValue;
    /**
     * 年月值（五）
     */
    private String fiveYearMonthValue;
    /**
     * 年月值（六）
     */
    private String sixYearMonthValue;
    /**
     * 年月值（七）
     */
    private String sevenYearMonthValue;
    /**
     * 年月值（八）
     */
    private String eightYearMonthValue;
    /**
     * 年月值（九）
     */
    private String nineYearMonthValue;
    /**
     * 年月值（十）
     */
    private String tenYearMonthValue;
    /**
     * 年月值（十一）
     */
    private String elevenYearMonthValue;
    /**
     * 年月值（十二）
     */
    private String twelveYearMonthValue;
    /**
     * 指定年月
     */
    private String appointYearMonth;
    /**
     * 版本
     */
    private Integer versionValue;

}
