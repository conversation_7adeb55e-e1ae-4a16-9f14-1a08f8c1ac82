package com.yhl.scp.mrp.material.purchase.convertor;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDataDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDataDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDataPO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewDataConvertor</code>
 * <p>
 * 材料采购评审转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:30
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialPurchaseReviewDataConvertor {

    MaterialPurchaseReviewDataConvertor INSTANCE = Mappers.getMapper(MaterialPurchaseReviewDataConvertor.class);

    MaterialPurchaseReviewDataDO dto2Do(MaterialPurchaseReviewDataDTO obj);

    MaterialPurchaseReviewDataDTO do2Dto(MaterialPurchaseReviewDataDO obj);

    List<MaterialPurchaseReviewDataDO> dto2Dos(List<MaterialPurchaseReviewDataDTO> list);

    List<MaterialPurchaseReviewDataDTO> do2Dtos(List<MaterialPurchaseReviewDataDO> list);

    MaterialPurchaseReviewDataVO do2Vo(MaterialPurchaseReviewDataDO obj);

    MaterialPurchaseReviewDataVO po2Vo(MaterialPurchaseReviewDataPO obj);

    List<MaterialPurchaseReviewDataVO> po2Vos(List<MaterialPurchaseReviewDataPO> list);

    MaterialPurchaseReviewDataPO do2Po(MaterialPurchaseReviewDataDO obj);

    MaterialPurchaseReviewDataDO po2Do(MaterialPurchaseReviewDataPO obj);

    MaterialPurchaseReviewDataPO dto2Po(MaterialPurchaseReviewDataDTO obj);

    List<MaterialPurchaseReviewDataPO> dto2Pos(List<MaterialPurchaseReviewDataDTO> obj);

}
