package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequestDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseRequestController</code>
 * <p>
 * ERP材料采购申请单控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 10:19:36
 */
@Slf4j
@Api(tags = "ERP材料采购申请单控制器")
@RestController
@RequestMapping("materialPurchaseRequest")
public class MaterialPurchaseRequestController extends BaseController {

    @Resource
    private MaterialPurchaseRequestService materialPurchaseRequestService;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialPurchaseRequestVO>> page() {
        List<MaterialPurchaseRequestVO> materialPurchaseRequestList = materialPurchaseRequestService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseRequestVO> pageInfo = new PageInfo<>(materialPurchaseRequestList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        return materialPurchaseRequestService.doCreate(materialPurchaseRequestDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        return materialPurchaseRequestService.doUpdate(materialPurchaseRequestDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseRequestService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialPurchaseRequestVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseRequestService.selectByPrimaryKey(id));
    }
    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncMaterialPurchaseRequest() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");

        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步材料在途数据（送货单）job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            materialPurchaseRequestService.syncMaterialPurchaseRequest(scenario.getTenantId());
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步材料在途数据（送货单）job结束", scenario);
        }
        return materialPurchaseRequestService.syncMaterialPurchaseRequest(SystemHolder.getTenantCode());
    }

}
