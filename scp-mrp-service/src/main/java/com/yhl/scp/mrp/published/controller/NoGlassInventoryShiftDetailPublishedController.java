package com.yhl.scp.mrp.published.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.published.dto.NoGlassInventoryShiftDetailPublishedDTO;
import com.yhl.scp.mrp.published.service.NoGlassInventoryShiftDetailPublishedService;
import com.yhl.scp.mrp.published.vo.NoGlassInventoryShiftDetailPublishedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>NoGlassInventoryShiftDetailPublishedController</code>
 * <p>
 * 非原片库存推移发布详情表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 14:03:35
 */
@Slf4j
@Api(tags = "非原片库存推移发布详情表控制器")
@RestController
@RequestMapping("noGlassInventoryShiftDetailPublished")
public class NoGlassInventoryShiftDetailPublishedController extends BaseController {

    @Resource
    private NoGlassInventoryShiftDetailPublishedService noGlassInventoryShiftDetailPublishedService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<NoGlassInventoryShiftDetailPublishedVO>> page() {
        List<NoGlassInventoryShiftDetailPublishedVO> noGlassInventoryShiftDetailPublishedList = noGlassInventoryShiftDetailPublishedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NoGlassInventoryShiftDetailPublishedVO> pageInfo = new PageInfo<>(noGlassInventoryShiftDetailPublishedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NoGlassInventoryShiftDetailPublishedDTO noGlassInventoryShiftDetailPublishedDTO) {
        return noGlassInventoryShiftDetailPublishedService.doCreate(noGlassInventoryShiftDetailPublishedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NoGlassInventoryShiftDetailPublishedDTO noGlassInventoryShiftDetailPublishedDTO) {
        return noGlassInventoryShiftDetailPublishedService.doUpdate(noGlassInventoryShiftDetailPublishedDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        noGlassInventoryShiftDetailPublishedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<NoGlassInventoryShiftDetailPublishedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, noGlassInventoryShiftDetailPublishedService.selectByPrimaryKey(id));
    }

}
