package com.yhl.scp.mrp.material.purchase.domain.factory;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDetailDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDetailDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialPurchaseReviewDetailFactory</code>
 * <p>
 * 材料采购评审明细领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:06:06
 */
@Component
public class MaterialPurchaseReviewDetailFactory {

    @Resource
    private MaterialPurchaseReviewDetailDao materialPurchaseReviewDetailDao;

    MaterialPurchaseReviewDetailDO create(MaterialPurchaseReviewDetailDTO dto) {
        // TODO
        return null;
    }

}
