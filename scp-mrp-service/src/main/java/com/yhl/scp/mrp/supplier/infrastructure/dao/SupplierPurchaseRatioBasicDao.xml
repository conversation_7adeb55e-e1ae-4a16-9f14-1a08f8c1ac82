<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.supplier.infrastructure.dao.SupplierPurchaseRatioBasicDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO">
        <!--@Table mds_sup_supplier_purchase_ratio-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="supplier_product_id" jdbcType="VARCHAR" property="supplierProductId"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="purchase_ratio" jdbcType="VARCHAR" property="purchaseRatio"/>
        <result column="historical_purchase_quantity" jdbcType="VARCHAR" property="historicalPurchaseQuantity"/>
        <result column="consignment" jdbcType="VARCHAR" property="consignment"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="agreement_type" jdbcType="VARCHAR" property="agreementType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO">
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,supplier_product_id,supplier_id,purchase_ratio,historical_purchase_quantity,remark,enabled,creator,create_time,modifier,modify_time,item_status,begin_time,end_time,consignment,agreement_type
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,supplier_code,supplier_name
    </sql>
    <sql id="Base_Where_Condition">
        <if test="params.id != null and params.id != ''">
            and id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.supplierProductId != null and params.supplierProductId != ''">
            and supplier_product_id = #{params.supplierProductId,jdbcType=VARCHAR}
        </if>
        <if test="params.supplierProductIds != null and params.supplierProductIds.size() > 0">
            and supplier_product_id in
            <foreach collection="params.supplierProductIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.ids != null and params.ids.size() > 0">
            and id in
            <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.supplierId != null and params.supplierId != ''">
            and supplier_id = #{params.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="params.purchaseRatio != null">
            and purchase_ratio = #{params.purchaseRatio,jdbcType=VARCHAR}
        </if>
        <if test="params.historicalPurchaseQuantity != null">
            and historical_purchase_quantity = #{params.historicalPurchaseQuantity,jdbcType=VARCHAR}
        </if>
        <if test="params.consignment != null and params.consignment != ''">
            and consignment = #{params.consignment,jdbcType=VARCHAR}
        </if>
        <if test="params.itemStatus != null and params.itemStatus != ''">
            and item_status = #{params.itemStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.beginTime != null">
            and begin_time = #{params.beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.endTime != null">
            and end_time = #{params.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.agreementType != null and params.agreementType != ''">
            and agreement_type = #{params.agreementType,jdbcType=VARCHAR}
        </if>
        <if test="params.remark != null and params.remark != ''">
            and remark = #{params.remark,jdbcType=VARCHAR}
        </if>
        <if test="params.enabled != null and params.enabled != ''">
            and enabled = #{params.enabled,jdbcType=VARCHAR}
        </if>
        <if test="params.creator != null and params.creator != ''">
            and creator = #{params.creator,jdbcType=VARCHAR}
        </if>
        <if test="params.createTime != null">
            and create_time = #{params.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.modifier != null and params.modifier != ''">
            and modifier = #{params.modifier,jdbcType=VARCHAR}
        </if>
        <if test="params.modifyTime != null">
            and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
        </if>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier_purchase_ratio
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier_purchase_ratio
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_sup_supplier_purchase_ratio
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier_purchase_ratio
        <where>
            <include refid="Base_Where_Condition"/>
        </where>
    </select>

    <select id="selectBySupplierPurchaseRatioIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier_purchase_ratio
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                supplier_id = #{item.supplierId,jdbcType=VARCHAR} and supplier_product_id = #{item.supplierProductId,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectSupplierProductIdSupplierId" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier_purchase_ratio
        where supplier_product_id in (
        select id
        from mrp_material_supplier_purchase
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                organization_id = #{item.orgId,jdbcType=VARCHAR} and material_code = #{item.itemCode,jdbcType=VARCHAR}
                )
            </foreach>
        </where>
        )
        and supplier_id in (
        select id
        from mds_sup_supplier
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                organization_id = #{item.orgId,jdbcType=VARCHAR} and supplier_code = #{item.vendorCode,jdbcType=VARCHAR}
                )
            </foreach>
        </where>
        )
    </select>

    <!-- 新增 -->
    <sql id="insertColumns">
        id,
        supplier_product_id,
        supplier_id,
        purchase_ratio,
        historical_purchase_quantity,
        consignment,
        item_status,
        begin_time,
        end_time,
        agreement_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time
    </sql>
    <sql id="insertValues">
        #{id,jdbcType=VARCHAR},
        #{supplierProductId,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{purchaseRatio,jdbcType=VARCHAR},
        #{historicalPurchaseQuantity,jdbcType=VARCHAR},
        #{consignment,jdbcType=VARCHAR},
        #{itemStatus,jdbcType=VARCHAR},
        #{beginTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{agreementType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP}
    </sql>
    <insert id="insert" parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO">
        insert into mds_sup_supplier_purchase_ratio(<include refid="insertColumns"/>)
        values (<include refid="insertValues"/>)
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO">
        insert into mds_sup_supplier_purchase_ratio(
        id,
        supplier_product_id,
        supplier_id,
        purchase_ratio,
        historical_purchase_quantity,
        consignment,
        item_status,
        begin_time,
        end_time,
        agreement_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{supplierProductId,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{purchaseRatio,jdbcType=VARCHAR},
        #{historicalPurchaseQuantity,jdbcType=VARCHAR},
        #{consignment,jdbcType=VARCHAR},
        #{itemStatus,jdbcType=VARCHAR},
        #{beginTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{agreementType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <sql id="insertBatchValues">
        #{entity.id,jdbcType=VARCHAR},
        #{entity.supplierProductId,jdbcType=VARCHAR},
        #{entity.supplierId,jdbcType=VARCHAR},
        #{entity.purchaseRatio,jdbcType=VARCHAR},
        #{entity.historicalPurchaseQuantity,jdbcType=VARCHAR},
        #{entity.consignment,jdbcType=VARCHAR},
        #{entity.itemStatus,jdbcType=VARCHAR},
        #{entity.beginTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.agreementType,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP}
    </sql>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_sup_supplier_purchase_ratio(<include refid="insertColumns"/>)
        values
        <foreach collection="list" item="entity" separator=",">
            (<include refid="insertBatchValues"/>)
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_sup_supplier_purchase_ratio(
        id,
        supplier_product_id,
        supplier_id,
        purchase_ratio,
        historical_purchase_quantity,
        consignment,
        item_status,
        begin_time,
        end_time,
        agreement_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.supplierProductId,jdbcType=VARCHAR},
            #{entity.supplierId,jdbcType=VARCHAR},
            #{entity.purchaseRatio,jdbcType=VARCHAR},
            #{entity.historicalPurchaseQuantity,jdbcType=VARCHAR},
            #{entity.consignment,jdbcType=VARCHAR},
            #{entity.itemStatus,jdbcType=VARCHAR},
            #{entity.beginTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.agreementType,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <sql id="updateColumns">
        supplier_product_id = #{supplierProductId,jdbcType=VARCHAR},
        supplier_id = #{supplierId,jdbcType=VARCHAR},
        purchase_ratio = #{purchaseRatio,jdbcType=VARCHAR},
        historical_purchase_quantity = #{historicalPurchaseQuantity,jdbcType=VARCHAR},
        consignment = #{consignment,jdbcType=VARCHAR},
        item_status = #{itemStatus,jdbcType=VARCHAR},
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        agreement_type = #{agreementType,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    </sql>
    <update id="update" parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO">
        update mds_sup_supplier_purchase_ratio set
        <include refid="updateColumns"/>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <sql id="updateSelectiveColumns">
        <if test="item.supplierProductId != null and item.supplierProductId != ''">
            supplier_product_id = #{item.supplierProductId,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierId != null and item.supplierId != ''">
            supplier_id = #{item.supplierId,jdbcType=VARCHAR},
        </if>
        <if test="item.purchaseRatio != null">
            purchase_ratio = #{item.purchaseRatio,jdbcType=VARCHAR},
        </if>
        <if test="item.historicalPurchaseQuantity != null">
            historical_purchase_quantity = #{item.historicalPurchaseQuantity,jdbcType=VARCHAR},
        </if>
        <if test="item.consignment != null and item.consignment != ''">
            consignment = #{item.consignment,jdbcType=VARCHAR},
        </if>
        <if test="item.itemStatus != null and item.itemStatus != ''">
            item_status = #{item.itemStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.beginTime != null">
            begin_time = #{item.beginTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null">
            end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.agreementType != null and item.agreementType != ''">
            agreement_type = #{item.agreementType,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null and item.remark != ''">
            remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.enabled != null and item.enabled != ''">
            enabled = #{item.enabled,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null and item.creator != ''">
            creator = #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
            create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifier != null and item.modifier != ''">
            modifier = #{item.modifier,jdbcType=VARCHAR},
        </if>
        <if test="item.modifyTime != null">
            modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
        </if>
    </sql>
    <!-- 批量修改 -->
    <sql id="updateBatchColumns">
        <trim prefix="supplier_product_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierProductId,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="supplier_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierId,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="purchase_ratio = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseRatio,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="historical_purchase_quantity = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.historicalPurchaseQuantity,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="consignment = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.consignment,jdbcType=VARCHAR}
        </foreach>
        </trim>
        <trim prefix="item_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.itemStatus,jdbcType=VARCHAR}
        </foreach>
        </trim>
        <trim prefix="begin_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.beginTime,jdbcType=TIMESTAMP}
        </foreach>
        </trim>
        <trim prefix="end_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
        </foreach>
        </trim>
        <trim prefix="agreement_type = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.agreementType,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="remark = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="enabled = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="modifier = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="modify_time = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
            </foreach>
        </trim>
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        update mds_sup_supplier_purchase_ratio
        <trim prefix="set" suffixOverrides=",">
            <include refid="updateBatchColumns"/>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>    <!-- 批量选择修改 -->
    <sql id="updateBatchSelectiveColumns">
        <if test="item.supplierProductId != null and item.supplierProductId != ''">
            supplier_product_id = #{item.supplierProductId,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierId != null and item.supplierId != ''">
            supplier_id = #{item.supplierId,jdbcType=VARCHAR},
        </if>
        <if test="item.purchaseRatio != null">
            purchase_ratio = #{item.purchaseRatio,jdbcType=VARCHAR},
        </if>
        <if test="item.historicalPurchaseQuantity != null">
            historical_purchase_quantity = #{item.historicalPurchaseQuantity,jdbcType=VARCHAR},
        </if>
        <if test="item.consignment != null">
            consignment = #{item.consignment,jdbcType=VARCHAR},
        </if>
        <if test="item.itemStatus != null">
            item_status = #{item.itemStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.beginTime != null">
            begin_time = #{item.beginTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null">
            end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.agreementType != null and item.agreementType != ''">
            agreement_type = #{item.agreementType,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null and item.remark != ''">
            remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.enabled != null and item.enabled != ''">
            enabled = #{item.enabled,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null and item.creator != ''">
            creator = #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
            create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifier != null and item.modifier != ''">
            modifier = #{item.modifier,jdbcType=VARCHAR},
        </if>
        <if test="item.modifyTime != null">
            modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
        </if>
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_sup_supplier_purchase_ratio
            <set>
                <include refid="updateBatchSelectiveColumns"/>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_sup_supplier_purchase_ratio where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_sup_supplier_purchase_ratio where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
