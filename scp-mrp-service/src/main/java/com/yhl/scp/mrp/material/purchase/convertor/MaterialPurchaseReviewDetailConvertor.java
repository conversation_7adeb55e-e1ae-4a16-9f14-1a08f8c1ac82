package com.yhl.scp.mrp.material.purchase.convertor;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDetailDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialPurchaseReviewDetailConvertor</code>
 * <p>
 * 材料采购评审明细转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:06:06
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialPurchaseReviewDetailConvertor {

    MaterialPurchaseReviewDetailConvertor INSTANCE = Mappers.getMapper(MaterialPurchaseReviewDetailConvertor.class);

    MaterialPurchaseReviewDetailDO dto2Do(MaterialPurchaseReviewDetailDTO obj);

    MaterialPurchaseReviewDetailDTO do2Dto(MaterialPurchaseReviewDetailDO obj);

    List<MaterialPurchaseReviewDetailDO> dto2Dos(List<MaterialPurchaseReviewDetailDTO> list);

    List<MaterialPurchaseReviewDetailDTO> do2Dtos(List<MaterialPurchaseReviewDetailDO> list);

    MaterialPurchaseReviewDetailVO do2Vo(MaterialPurchaseReviewDetailDO obj);

    MaterialPurchaseReviewDetailVO po2Vo(MaterialPurchaseReviewDetailPO obj);

    List<MaterialPurchaseReviewDetailVO> po2Vos(List<MaterialPurchaseReviewDetailPO> list);

    MaterialPurchaseReviewDetailPO do2Po(MaterialPurchaseReviewDetailDO obj);

    MaterialPurchaseReviewDetailDO po2Do(MaterialPurchaseReviewDetailPO obj);

    MaterialPurchaseReviewDetailPO dto2Po(MaterialPurchaseReviewDetailDTO obj);

    List<MaterialPurchaseReviewDetailPO> dto2Pos(List<MaterialPurchaseReviewDetailDTO> obj);

}
