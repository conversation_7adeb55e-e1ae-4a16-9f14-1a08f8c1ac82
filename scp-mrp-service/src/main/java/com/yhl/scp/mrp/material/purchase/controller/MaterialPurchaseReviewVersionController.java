package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewVersionService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewVersionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewVersionController</code>
 * <p>
 * 材料采购评审版本控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:58
 */
@Slf4j
@Api(tags = "材料采购评审版本控制器")
@RestController
@RequestMapping("materialPurchaseReviewVersion")
public class MaterialPurchaseReviewVersionController extends BaseController {

    @Resource
    private MaterialPurchaseReviewVersionService materialPurchaseReviewVersionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialPurchaseReviewVersionVO>> page() {
        List<MaterialPurchaseReviewVersionVO> materialPurchaseReviewVersionList = materialPurchaseReviewVersionService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseReviewVersionVO> pageInfo = new PageInfo<>(materialPurchaseReviewVersionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO) {
        return materialPurchaseReviewVersionService.doCreate(materialPurchaseReviewVersionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO) {
        return materialPurchaseReviewVersionService.doUpdate(materialPurchaseReviewVersionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseReviewVersionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialPurchaseReviewVersionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewVersionService.selectByPrimaryKey(id));
    }

}
