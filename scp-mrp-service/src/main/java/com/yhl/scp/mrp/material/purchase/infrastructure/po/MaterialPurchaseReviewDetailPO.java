package com.yhl.scp.mrp.material.purchase.infrastructure.po;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPurchaseReviewDetailPO</code>
 * <p>
 * 材料采购评审明细PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:06:06
 */
public class MaterialPurchaseReviewDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 419547761092421667L;

    /**
     * 版本id
     */
    private String materialPurchaseReviewVersionId;
    /**
     * 评审类型
     */
    private String reviewType;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂编码
     */
    private String oemName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 采购周期
     */
    private BigDecimal purchaseLot;
    /**
     * 最小起订量
     */
    private BigDecimal minOrderQuantity;
    /**
     * 评审意见
     */
    private String reviewComments;
    /**
     * 批准
     */
    private String approve;
    /**
     * 审核
     */
    private String toExamine;
    /**
     * 制表
     */
    private String tabulation;
    /**
     * 一致性需求预测版本ID
     */
    private String consistenceDemandForecastVersionId;
    /**
     * 预测提供者
     */
    private String forecastProvider;
    /**
     * 行描述
     */
    private String lineDescription;
    /**
     * 车型EOP时间
     */
    private String vehicleModelEopDate;
    /**
     * 主机厂车型
     */
    private String vehicleModel;
    /**
     * 年月值（一）
     */
    private String oneYearMonthValue;
    /**
     * 年月值（二）
     */
    private String twoYearMonthValue;
    /**
     * 年月值（三）
     */
    private String threeYearMonthValue;
    /**
     * 年月值（四）
     */
    private String fourYearMonthValue;
    /**
     * 年月值（五）
     */
    private String fiveYearMonthValue;
    /**
     * 年月值（六）
     */
    private String sixYearMonthValue;
    /**
     * 年月值（七）
     */
    private String sevenYearMonthValue;
    /**
     * 年月值（八）
     */
    private String eightYearMonthValue;
    /**
     * 年月值（九）
     */
    private String nineYearMonthValue;
    /**
     * 年月值（十）
     */
    private String tenYearMonthValue;
    /**
     * 年月值（十一）
     */
    private String elevenYearMonthValue;
    /**
     * 年月值（十二）
     */
    private String twelveYearMonthValue;
    /**
     * 指定年月
     */
    private String appointYearMonth;
    /**
     * 单片面积
     */
    private String singlePieceArea;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getMaterialPurchaseReviewVersionId() {
        return materialPurchaseReviewVersionId;
    }

    public void setMaterialPurchaseReviewVersionId(String materialPurchaseReviewVersionId) {
        this.materialPurchaseReviewVersionId = materialPurchaseReviewVersionId;
    }

    public String getReviewType() {
        return reviewType;
    }

    public void setReviewType(String reviewType) {
        this.reviewType = reviewType;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductFactoryCode() {
        return productFactoryCode;
    }

    public void setProductFactoryCode(String productFactoryCode) {
        this.productFactoryCode = productFactoryCode;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOemName() {
        return oemName;
    }

    public void setOemName(String oemName) {
        this.oemName = oemName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public BigDecimal getPurchaseLot() {
        return purchaseLot;
    }

    public void setPurchaseLot(BigDecimal purchaseLot) {
        this.purchaseLot = purchaseLot;
    }

    public BigDecimal getMinOrderQuantity() {
        return minOrderQuantity;
    }

    public void setMinOrderQuantity(BigDecimal minOrderQuantity) {
        this.minOrderQuantity = minOrderQuantity;
    }

    public String getReviewComments() {
        return reviewComments;
    }

    public void setReviewComments(String reviewComments) {
        this.reviewComments = reviewComments;
    }

    public String getApprove() {
        return approve;
    }

    public void setApprove(String approve) {
        this.approve = approve;
    }

    public String getToExamine() {
        return toExamine;
    }

    public void setToExamine(String toExamine) {
        this.toExamine = toExamine;
    }

    public String getTabulation() {
        return tabulation;
    }

    public void setTabulation(String tabulation) {
        this.tabulation = tabulation;
    }

    public String getConsistenceDemandForecastVersionId() {
        return consistenceDemandForecastVersionId;
    }

    public void setConsistenceDemandForecastVersionId(String consistenceDemandForecastVersionId) {
        this.consistenceDemandForecastVersionId = consistenceDemandForecastVersionId;
    }

    public String getForecastProvider() {
        return forecastProvider;
    }

    public void setForecastProvider(String forecastProvider) {
        this.forecastProvider = forecastProvider;
    }

    public String getLineDescription() {
        return lineDescription;
    }

    public void setLineDescription(String lineDescription) {
        this.lineDescription = lineDescription;
    }

    public String getVehicleModelEopDate() {
        return vehicleModelEopDate;
    }

    public void setVehicleModelEopDate(String vehicleModelEopDate) {
        this.vehicleModelEopDate = vehicleModelEopDate;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getOneYearMonthValue() {
        return oneYearMonthValue;
    }

    public void setOneYearMonthValue(String oneYearMonthValue) {
        this.oneYearMonthValue = oneYearMonthValue;
    }

    public String getTwoYearMonthValue() {
        return twoYearMonthValue;
    }

    public void setTwoYearMonthValue(String twoYearMonthValue) {
        this.twoYearMonthValue = twoYearMonthValue;
    }

    public String getThreeYearMonthValue() {
        return threeYearMonthValue;
    }

    public void setThreeYearMonthValue(String threeYearMonthValue) {
        this.threeYearMonthValue = threeYearMonthValue;
    }

    public String getFourYearMonthValue() {
        return fourYearMonthValue;
    }

    public void setFourYearMonthValue(String fourYearMonthValue) {
        this.fourYearMonthValue = fourYearMonthValue;
    }

    public String getFiveYearMonthValue() {
        return fiveYearMonthValue;
    }

    public void setFiveYearMonthValue(String fiveYearMonthValue) {
        this.fiveYearMonthValue = fiveYearMonthValue;
    }

    public String getSixYearMonthValue() {
        return sixYearMonthValue;
    }

    public void setSixYearMonthValue(String sixYearMonthValue) {
        this.sixYearMonthValue = sixYearMonthValue;
    }

    public String getSevenYearMonthValue() {
        return sevenYearMonthValue;
    }

    public void setSevenYearMonthValue(String sevenYearMonthValue) {
        this.sevenYearMonthValue = sevenYearMonthValue;
    }

    public String getEightYearMonthValue() {
        return eightYearMonthValue;
    }

    public void setEightYearMonthValue(String eightYearMonthValue) {
        this.eightYearMonthValue = eightYearMonthValue;
    }

    public String getNineYearMonthValue() {
        return nineYearMonthValue;
    }

    public void setNineYearMonthValue(String nineYearMonthValue) {
        this.nineYearMonthValue = nineYearMonthValue;
    }

    public String getTenYearMonthValue() {
        return tenYearMonthValue;
    }

    public void setTenYearMonthValue(String tenYearMonthValue) {
        this.tenYearMonthValue = tenYearMonthValue;
    }

    public String getElevenYearMonthValue() {
        return elevenYearMonthValue;
    }

    public void setElevenYearMonthValue(String elevenYearMonthValue) {
        this.elevenYearMonthValue = elevenYearMonthValue;
    }

    public String getTwelveYearMonthValue() {
        return twelveYearMonthValue;
    }

    public void setTwelveYearMonthValue(String twelveYearMonthValue) {
        this.twelveYearMonthValue = twelveYearMonthValue;
    }

    public String getAppointYearMonth() {
        return appointYearMonth;
    }

    public void setAppointYearMonth(String appointYearMonth) {
        this.appointYearMonth = appointYearMonth;
    }

    public String getSinglePieceArea() {
        return singlePieceArea;
    }

    public void setSinglePieceArea(String singlePieceArea) {
        this.singlePieceArea = singlePieceArea;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
