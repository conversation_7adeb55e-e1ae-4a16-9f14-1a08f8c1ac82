package com.yhl.scp.mrp.material.purchase.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewVersionPO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewVersionVO;

/**
 * <code>MaterialPurchaseReviewVersionDao</code>
 * <p>
 * 材料采购评审版本DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:58
 */
public interface MaterialPurchaseReviewVersionDao extends BaseDao<MaterialPurchaseReviewVersionPO, MaterialPurchaseReviewVersionVO> {

    MaterialPurchaseReviewVersionPO selectLastVersion();

}
