package com.yhl.scp.mrp.material.purchase.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseReviewDetailConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDetailDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseReviewDetailDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDetailDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDetailService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseReviewDetailServiceImpl</code>
 * <p>
 * 材料采购评审明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:05:55
 */
@Slf4j
@Service
public class MaterialPurchaseReviewDetailServiceImpl extends AbstractService implements MaterialPurchaseReviewDetailService {

    @Resource
    private MaterialPurchaseReviewDetailDao materialPurchaseReviewDetailDao;

    @Resource
    private MaterialPurchaseReviewDetailDomainService materialPurchaseReviewDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO) {
        // 0.数据转换
        MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Do(materialPurchaseReviewDetailDTO);
        MaterialPurchaseReviewDetailPO materialPurchaseReviewDetailPO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Po(materialPurchaseReviewDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewDetailDomainService.validation(materialPurchaseReviewDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseReviewDetailPO);
        materialPurchaseReviewDetailDao.insert(materialPurchaseReviewDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO) {
        // 0.数据转换
        MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Do(materialPurchaseReviewDetailDTO);
        MaterialPurchaseReviewDetailPO materialPurchaseReviewDetailPO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Po(materialPurchaseReviewDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewDetailDomainService.validation(materialPurchaseReviewDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseReviewDetailPO);
        materialPurchaseReviewDetailDao.update(materialPurchaseReviewDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdateSelective(MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO) {
        // 0.数据转换
        MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Do(materialPurchaseReviewDetailDTO);
        MaterialPurchaseReviewDetailPO materialPurchaseReviewDetailPO = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Po(materialPurchaseReviewDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewDetailDomainService.validation(materialPurchaseReviewDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseReviewDetailPO);
        materialPurchaseReviewDetailDao.updateSelective(materialPurchaseReviewDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseReviewDetailDTO> list) {
        List<MaterialPurchaseReviewDetailPO> newList = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseReviewDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseReviewDetailDTO> list) {
        List<MaterialPurchaseReviewDetailPO> newList = MaterialPurchaseReviewDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseReviewDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseReviewDetailDao.deleteBatch(idList);
        }
        return materialPurchaseReviewDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseReviewDetailVO selectByPrimaryKey(String id) {
        MaterialPurchaseReviewDetailPO po = materialPurchaseReviewDetailDao.selectByPrimaryKey(id);
        return MaterialPurchaseReviewDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_DETAIL")
    public List<MaterialPurchaseReviewDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_DETAIL")
    public List<MaterialPurchaseReviewDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseReviewDetailVO> dataList = materialPurchaseReviewDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseReviewDetailServiceImpl target = SpringBeanUtils.getBean(MaterialPurchaseReviewDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseReviewDetailVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseReviewDetailPO> list = materialPurchaseReviewDetailDao.selectByParams(params);
        return MaterialPurchaseReviewDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseReviewDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialPurchaseReviewDetailVO> invocation(List<MaterialPurchaseReviewDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
