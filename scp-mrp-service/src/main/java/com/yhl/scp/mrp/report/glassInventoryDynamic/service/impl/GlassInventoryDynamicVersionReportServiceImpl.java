package com.yhl.scp.mrp.report.glassInventoryDynamic.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.SourceTypeEnum;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.transactions.service.MaterialTransactionsService;
import com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.convertor.GlassInventoryDynamicVersionReportConvertor;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.entity.GlassInventoryDynamicVersionReportDO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.service.GlassInventoryDynamicVersionReportDomainService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.dto.GlassInventoryDynamicDataReportDTO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.dto.GlassInventoryDynamicVersionReportDTO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicVersionReportDao;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicBasicReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicDataReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicVersionReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicAttributeVO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicBasicReportVO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicVersionReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Array;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>GlassInventoryDynamicVersionReportServiceImpl</code>
 * <p>
 * 原片库存动态版本表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 22:03:59
 */
@Slf4j
@Service
public class GlassInventoryDynamicVersionReportServiceImpl extends AbstractService implements GlassInventoryDynamicVersionReportService {

    @Resource
    private GlassInventoryDynamicVersionReportDao glassInventoryDynamicVersionReportDao;

    @Resource
    private GlassInventoryDynamicVersionReportDomainService glassInventoryDynamicVersionReportDomainService;

    @Resource
    private GlassInventoryDynamicDataReportService glassInventoryDynamicDataReportService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @Resource
    private GlassInventoryDynamicBasicReportService glassInventoryDynamicBasicReportService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private MaterialTransactionsService materialTransactionsService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(GlassInventoryDynamicVersionReportDTO glassInventoryDynamicVersionReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicVersionReportDO glassInventoryDynamicVersionReportDO = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicVersionReportDTO);
        GlassInventoryDynamicVersionReportPO glassInventoryDynamicVersionReportPO = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicVersionReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicVersionReportDomainService.validation(glassInventoryDynamicVersionReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassInventoryDynamicVersionReportPO);
        glassInventoryDynamicVersionReportDao.insertWithPrimaryKey(glassInventoryDynamicVersionReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(GlassInventoryDynamicVersionReportDTO glassInventoryDynamicVersionReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicVersionReportDO glassInventoryDynamicVersionReportDO = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicVersionReportDTO);
        GlassInventoryDynamicVersionReportPO glassInventoryDynamicVersionReportPO = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicVersionReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicVersionReportDomainService.validation(glassInventoryDynamicVersionReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassInventoryDynamicVersionReportPO);
        glassInventoryDynamicVersionReportDao.update(glassInventoryDynamicVersionReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassInventoryDynamicVersionReportDTO> list) {
        List<GlassInventoryDynamicVersionReportPO> newList = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassInventoryDynamicVersionReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassInventoryDynamicVersionReportDTO> list) {
        List<GlassInventoryDynamicVersionReportPO> newList = GlassInventoryDynamicVersionReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassInventoryDynamicVersionReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassInventoryDynamicVersionReportDao.deleteBatch(idList);
        }
        return glassInventoryDynamicVersionReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassInventoryDynamicVersionReportVO selectByPrimaryKey(String id) {
        GlassInventoryDynamicVersionReportPO po = glassInventoryDynamicVersionReportDao.selectByPrimaryKey(id);
        return GlassInventoryDynamicVersionReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_VERSION_REPORT")
    public List<GlassInventoryDynamicVersionReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_VERSION_REPORT")
    public List<GlassInventoryDynamicVersionReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassInventoryDynamicVersionReportVO> dataList = glassInventoryDynamicVersionReportDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassInventoryDynamicVersionReportServiceImpl target = SpringBeanUtils.getBean(GlassInventoryDynamicVersionReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassInventoryDynamicVersionReportVO> selectByParams(Map<String, Object> params) {
        List<GlassInventoryDynamicVersionReportPO> list = glassInventoryDynamicVersionReportDao.selectByParams(params);
        return GlassInventoryDynamicVersionReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassInventoryDynamicVersionReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<GlassInventoryDynamicVersionReportVO> invocation(List<GlassInventoryDynamicVersionReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> doReportGeneration(String scenario) {
        Date date = new Date();

        // 维护版本数据
        GlassInventoryDynamicVersionReportDTO versionReportDTO = new GlassInventoryDynamicVersionReportDTO();
        versionReportDTO.setId(UUID.randomUUID().toString());

        List<GlassInventoryDynamicDataReportDTO> insertList = new ArrayList<>();

        //1.查询固定固表数据
        List<GlassInventoryDynamicBasicReportVO> basicReportVOList = glassInventoryDynamicBasicReportService.selectAll();
        basicReportVOList = basicReportVOList.stream()
                .sorted(Comparator.comparing(GlassInventoryDynamicBasicReportVO::getShowSequence))
                .collect(Collectors.toList());

        //1.查询本地库存
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectAllGlassInventoryBatch(scenario);
        Map<String, GlassInventoryDynamicAttributeVO> localInventoryDataMap = assembleLocalInventoryData(inventoryBatchDetailVOList);

        //2.查询在途库存(浮法已发运+码头库存)
        //2.1.浮法已发运库存
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS = inventoryFloatGlassShippedDetailService.selectByParams(new HashMap<>());
        //2.2.码头库存
        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "storageFlag", YesOrNoEnum.NO.getCode(),
                        "delivered", YesOrNoEnum.NO.getCode()));
        Map<String, GlassInventoryDynamicAttributeVO> transitInventoryDataMap = assembleTransitInventoryData(inventoryFloatGlassShippedDetailVOS, inventoryQuayDetailVOS);


        //3.查询供应商/内部浮法库存
        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOS = inventoryFloatGlassDetailService.selectByParams(new HashMap<>());
        //查询浮法库存数据来源为"接口"的
        List<InventoryFloatGlassDetailVO> interfaceInventoryFloatGlassDetailList = inventoryFloatGlassDetailVOS.stream()
                .filter(t -> SourceTypeEnum.INTERFACE.getCode().equals(t.getSourceType()))
                .collect(Collectors.toList());
        Map<String, GlassInventoryDynamicAttributeVO> internalFloatInventoryDataMap = assembleFloatInventoryData(interfaceInventoryFloatGlassDetailList);


        //4.查询供应商/外部浮法库存
        List<InventoryFloatGlassDetailVO> importInventoryFloatGlassDetailList = inventoryFloatGlassDetailVOS.stream()
                .filter(t -> SourceTypeEnum.IMPORT.getCode().equals(t.getSourceType()))
                .collect(Collectors.toList());
        Map<String, GlassInventoryDynamicAttributeVO> externalFloatInventoryDataMap = assembleFloatInventoryData(importInventoryFloatGlassDetailList);

        //6.查询积压库存
        // TODO

        //8.查询有效库存
        // TODO

        //9.查询当月消耗库存
        List<MaterialTransactionsVO> materialTransactionsVOList = materialTransactionsService.selectByParams(
                ImmutableMap.of("category", "RA.A", "transactionType", "Inventory direct organization transfer"));
        Map<String, GlassInventoryDynamicAttributeVO> materialTransactionsDataMap = assembleMaterialTransactionsData(materialTransactionsVOList);

        //10.查询后三个月滚动预测
        Date moveMonth = DateUtils.moveMonth(date, 3);
        List<String> futureMonthList = getFutureMonth();
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandService.selectByParams(ImmutableMap.of(
                "productCategory", "RA.A", "startDate", date, "endDate", moveMonth));
        Map<String, GlassInventoryDynamicAttributeVO> nextOneMonthInventoryDataMap = new HashMap<>();
        Map<String, GlassInventoryDynamicAttributeVO> nextTwoMonthInventoryDataMap = new HashMap<>();
        Map<String, GlassInventoryDynamicAttributeVO> nextThreeMonthInventoryDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materialGrossDemandVOList)) {
            // 按照月份去分组
            Map<String, List<MaterialGrossDemandVO>> materialGrossMap = materialGrossDemandVOList.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(), "MM")));
            nextOneMonthInventoryDataMap = assembleFutureDemandData(materialGrossMap, futureMonthList.get(0));
            nextTwoMonthInventoryDataMap = assembleFutureDemandData(materialGrossMap, futureMonthList.get(1));
            nextThreeMonthInventoryDataMap = assembleFutureDemandData(materialGrossMap, futureMonthList.get(2));
        }

        // 组装报表Data数据
        for (GlassInventoryDynamicBasicReportVO basicReportVO : basicReportVOList) {
            String key = String.join("_", basicReportVO.getProductColor(), basicReportVO.getProductThickness());
            GlassInventoryDynamicDataReportDTO dataReportDTO = new GlassInventoryDynamicDataReportDTO();
            dataReportDTO.setId(UUID.randomUUID().toString());
            dataReportDTO.setVersionId(versionReportDTO.getId());
            dataReportDTO.setBasicReportId(basicReportVO.getId());
            dataReportDTO.setProductColor(null != basicReportVO.getProductColorDesc() ? String.join("/", basicReportVO.getProductColor(), basicReportVO.getProductColorDesc()) : basicReportVO.getProductColor());
            dataReportDTO.setProductThickness(basicReportVO.getProductThickness());
            dataReportDTO.setGroupFlag(basicReportVO.getGroupFlag());
            if (localInventoryDataMap.containsKey(key)) {
                dataReportDTO.setLocalInventoryArea(localInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setLocalInventoryWeight(localInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }
            if (transitInventoryDataMap.containsKey(key)) {
                dataReportDTO.setTransitInventoryArea(transitInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setTransitInventoryWeight(transitInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }

            dataReportDTO.setInternalFloatGlassInventoryArea(BigDecimal.ZERO);
            dataReportDTO.setInternalFloatGlassInventoryWeight(BigDecimal.ZERO);
            if (internalFloatInventoryDataMap.containsKey(key)) {
                dataReportDTO.setInternalFloatGlassInventoryArea(internalFloatInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setInternalFloatGlassInventoryWeight(internalFloatInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }
            dataReportDTO.setExternalFloatGlassInventoryArea(BigDecimal.ZERO);
            dataReportDTO.setExternalFloatGlassInventoryWeight(BigDecimal.ZERO);
            if (externalFloatInventoryDataMap.containsKey(key)){
                dataReportDTO.setExternalFloatGlassInventoryArea(externalFloatInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setExternalFloatGlassInventoryWeight(externalFloatInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }

            dataReportDTO.setFloatGlassInventoryArea(dataReportDTO.getInternalFloatGlassInventoryArea().add(dataReportDTO.getExternalFloatGlassInventoryArea()).setScale(0, RoundingMode.HALF_UP));
            dataReportDTO.setFloatGlassInventoryWeight(dataReportDTO.getInternalFloatGlassInventoryWeight().add(dataReportDTO.getExternalFloatGlassInventoryWeight()).setScale(0, RoundingMode.HALF_UP));

            // TODO 积压库存-调用建圻的报表
            dataReportDTO.setBacklogInventoryArea(BigDecimal.ZERO);
            dataReportDTO.setBacklogInventoryWeight(BigDecimal.ZERO);

            // 总库存=本厂库存+浮法库存
            dataReportDTO.setTotalInventoryArea(dataReportDTO.getLocalInventoryArea().add(dataReportDTO.getFloatGlassInventoryArea()).setScale(0, RoundingMode.HALF_UP));
            dataReportDTO.setTotalInventoryWeight(dataReportDTO.getLocalInventoryWeight().add(dataReportDTO.getFloatGlassInventoryWeight()).setScale(0, RoundingMode.HALF_UP));

            // TODO 有效库存 = 总库存-积压库存
            dataReportDTO.setEffectiveTotalInventoryArea(dataReportDTO.getTotalInventoryArea().subtract(dataReportDTO.getBacklogInventoryArea()).setScale(0, RoundingMode.HALF_UP));
            dataReportDTO.setEffectiveTotalInventoryWeight(dataReportDTO.getTotalInventoryWeight().subtract(dataReportDTO.getBacklogInventoryWeight()).setScale(0, RoundingMode.HALF_UP));

            if (materialTransactionsDataMap.containsKey(key)){
                dataReportDTO.setCurrentMonthConsumeInventoryArea(materialTransactionsDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setCurrentMonthConsumeInventoryWeight(materialTransactionsDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }

            if (nextOneMonthInventoryDataMap.containsKey(key)){
                dataReportDTO.setNextOneMonthConsumeInventoryArea(nextOneMonthInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setNextOneMonthConsumeInventoryWeight(nextOneMonthInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }

            if (nextTwoMonthInventoryDataMap.containsKey(key)){
                dataReportDTO.setNextTwoMonthConsumeInventoryArea(nextTwoMonthInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setNextTwoMonthConsumeInventoryWeight(nextTwoMonthInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }

            if (nextThreeMonthInventoryDataMap.containsKey(key)){
                dataReportDTO.setNextThreeMonthConsumeInventoryArea(nextThreeMonthInventoryDataMap.get(key).getArea().setScale(0, RoundingMode.HALF_UP));
                dataReportDTO.setNextThreeMonthConsumeInventoryWeight(nextThreeMonthInventoryDataMap.get(key).getWeight().setScale(0, RoundingMode.HALF_UP));
            }
            // 上月的吨位 / 当月毛需求吨位 * 30
            if (dataReportDTO.getNextOneMonthConsumeInventoryWeight().compareTo(BigDecimal.ZERO) != 0){
                dataReportDTO.setInventoryDays(dataReportDTO.getCurrentMonthConsumeInventoryWeight()
                        .divide(dataReportDTO.getNextOneMonthConsumeInventoryWeight(), 1, RoundingMode.UP)
                        .multiply(BigDecimal.valueOf(30)).setScale(0, RoundingMode.HALF_UP));
            }
            insertList.add(dataReportDTO);
        }

        // 维护kpi数据
        BigDecimal currentMonthConsumeInventoryWeightSum = insertList.stream()
                .map(GlassInventoryDynamicDataReportDTO::getCurrentMonthConsumeInventoryWeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (currentMonthConsumeInventoryWeightSum.compareTo(BigDecimal.ZERO) != 0){
            // (后一个月的吨位 - 当月消耗的吨位) / 当月消耗的吨位 * 100
            BigDecimal nextOneMonthConsumeInventoryWeightSum = insertList.stream()
                    .map(GlassInventoryDynamicDataReportDTO::getNextOneMonthConsumeInventoryWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal glassNextOneMonthGrowthRate = nextOneMonthConsumeInventoryWeightSum.subtract(currentMonthConsumeInventoryWeightSum)
                    .divide(currentMonthConsumeInventoryWeightSum, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            versionReportDTO.setGlassNextOneMonthGrowthRate(glassNextOneMonthGrowthRate.stripTrailingZeros().toPlainString() + "%");

            // (后二个月的吨位 - 当月消耗的吨位) / 当月消耗的吨位 * 100
            BigDecimal nextTwoMonthConsumeInventoryWeightSum = insertList.stream()
                    .map(GlassInventoryDynamicDataReportDTO::getNextTwoMonthConsumeInventoryWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal glassNextTwoMonthGrowthRate = nextTwoMonthConsumeInventoryWeightSum.subtract(currentMonthConsumeInventoryWeightSum)
                    .divide(currentMonthConsumeInventoryWeightSum, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            versionReportDTO.setGlassNextTwoMonthGrowthRate(glassNextTwoMonthGrowthRate.stripTrailingZeros().toPlainString() + "%");

            // (后三个月的吨位 - 当月消耗的吨位) / 当月消耗的吨位 * 100
            BigDecimal nextThreeMonthConsumeInventoryWeightSum = insertList.stream()
                    .map(GlassInventoryDynamicDataReportDTO::getNextThreeMonthConsumeInventoryWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal glassNextThreeMonthGrowthRate = nextThreeMonthConsumeInventoryWeightSum.subtract(currentMonthConsumeInventoryWeightSum)
                    .divide(currentMonthConsumeInventoryWeightSum, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            versionReportDTO.setGlassNextThreeMonthGrowthRate(glassNextThreeMonthGrowthRate.stripTrailingZeros().toPlainString() + "%");
        }

        // TODO 需求待确认
        versionReportDTO.setMainOperationOutputCurrentMonth(null);
        versionReportDTO.setMainOperationOutputNextOneMonth(null);
        versionReportDTO.setMainOperationOutputNextTwoMonth(null);
        versionReportDTO.setMainOperationOutputNextThreeMonth(null);

        // (主工序产量后一个月 - 主工序产量当前月) / 主工序产量当前月 * 100
        versionReportDTO.setPlanNextOneMonthGrowthRate(null);
        // (主工序产量后二个月 - 主工序产量当前月) / 主工序产量当前月 * 100
        versionReportDTO.setPlanNextTwoMonthGrowthRate(null);
        // (主工序产量后三个月 - 主工序产量当前月) / 主工序产量当前月 * 100
        versionReportDTO.setPlanNextThreeMonthGrowthRate(null);

        this.doCreate(versionReportDTO);
        glassInventoryDynamicDataReportService.doCreateBatch(insertList);
        return BaseResponse.success();
    }

    private Map<String, GlassInventoryDynamicAttributeVO> assembleMaterialTransactionsData(List<MaterialTransactionsVO> materialTransactionsVOList) {
        Map<String, GlassInventoryDynamicAttributeVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(materialTransactionsVOList)){
            return result;
        }
        List<MaterialTransactionsVO> filterList = new ArrayList<>();
        for (MaterialTransactionsVO materialTransactionsVO : materialTransactionsVOList) {
            NewProductStockPointVO productAttribute = getProductAttribute(materialTransactionsVO.getProductCode());
            if (null == productAttribute) {
                continue;
            }
            materialTransactionsVO.setProductColor(productAttribute.getProductColor());
            materialTransactionsVO.setProductLength(productAttribute.getProductLength());
            materialTransactionsVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
            materialTransactionsVO.setProductWidth(productAttribute.getProductWidth());
            filterList.add(materialTransactionsVO);
        }
        if (CollectionUtils.isEmpty(filterList)) {
            return result;
        }

        for (MaterialTransactionsVO materialTransactionsVO : filterList) {
            if (null != materialTransactionsVO.getTransactionQty() && materialTransactionsVO.getTransactionQty().compareTo(BigDecimal.ZERO) < 0){
                materialTransactionsVO.setTransactionQty(materialTransactionsVO.getTransactionQty().abs());
            }
            // 汇总面积
            BigDecimal area = calculateArea(materialTransactionsVO.getTransactionQty(), materialTransactionsVO.getProductLength(), materialTransactionsVO.getProductWidth());
            // 汇总吨位
            BigDecimal weight = calculateWeight(area, materialTransactionsVO.getProductThickness());
            materialTransactionsVO.setAreaNumber(area);
            materialTransactionsVO.setWeight(weight);
        }

        // 按照颜色和厚度分组
        Map<String, List<MaterialTransactionsVO>> materialGrossDemandMap = filterList.stream()
                .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));
        for (Map.Entry<String, List<MaterialTransactionsVO>> entry : materialGrossDemandMap.entrySet()) {
            List<MaterialTransactionsVO> detailVOS = entry.getValue();
            // 统计面积
            BigDecimal totalArea = detailVOS.stream()
                    .map(MaterialTransactionsVO::getAreaNumber)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 统计重量
            BigDecimal totalWeight = detailVOS.stream()
                    .map(MaterialTransactionsVO::getWeight)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
            glassInventoryDynamicAttributeVO.setArea(totalArea);
            glassInventoryDynamicAttributeVO.setWeight(totalWeight);
            result.put(entry.getKey(), glassInventoryDynamicAttributeVO);
        }
        return result;
    }

    private Map<String, GlassInventoryDynamicAttributeVO> assembleFutureDemandData(Map<String, List<MaterialGrossDemandVO>> materialGrossMap, String month) {
        Map<String, GlassInventoryDynamicAttributeVO> result = new HashMap<>();
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossMap.get(month);
        if (CollectionUtils.isEmpty(materialGrossDemandVOList)) {
            return result;
        }

        List<MaterialGrossDemandVO> filterList = new ArrayList<>();
        for (MaterialGrossDemandVO materialGrossDemandVO : materialGrossDemandVOList) {
            NewProductStockPointVO productAttribute = getProductAttribute(materialGrossDemandVO.getProductCode());
            if (null == productAttribute) {
                continue;
            }
            materialGrossDemandVO.setProductColor(productAttribute.getProductColor());
            materialGrossDemandVO.setProductLength(productAttribute.getProductLength());
            materialGrossDemandVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
            materialGrossDemandVO.setProductWidth(productAttribute.getProductWidth());
            filterList.add(materialGrossDemandVO);
        }
        if (CollectionUtils.isEmpty(filterList)) {
            return result;
        }

        // 按照颜色和厚度分组
        Map<String, List<MaterialGrossDemandVO>> materialGrossDemandMap = filterList.stream()
                .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));

        for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : materialGrossDemandMap.entrySet()) {
            String key = entry.getKey();
            List<MaterialGrossDemandVO> grossDemandVOList = entry.getValue();
            // 库存数量求和
            // 库存数量求和
            BigDecimal totalQuantity = grossDemandVOList.stream()
                    .map(MaterialGrossDemandVO::getDemandQuantity)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 汇总面积
            BigDecimal area = calculateArea(totalQuantity, grossDemandVOList.get(0).getProductLength(), grossDemandVOList.get(0).getProductWidth());
            // 汇总吨位
            BigDecimal weight = calculateWeight(area, grossDemandVOList.get(0).getProductThickness());
            GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
            glassInventoryDynamicAttributeVO.setArea(area);
            glassInventoryDynamicAttributeVO.setWeight(weight);
            result.put(key, glassInventoryDynamicAttributeVO);
        }
        return result;
    }

    private Map<String, GlassInventoryDynamicAttributeVO> assembleFloatInventoryData(List<InventoryFloatGlassDetailVO> list) {
        Map<String, GlassInventoryDynamicAttributeVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<InventoryFloatGlassDetailVO> filterList = new ArrayList<>();
        for (InventoryFloatGlassDetailVO inventoryFloatGlassShippedDetailVO : list) {
            NewProductStockPointVO productAttribute = getProductAttribute(inventoryFloatGlassShippedDetailVO.getProductCode());
            if (null == productAttribute) {
                continue;
            }
            inventoryFloatGlassShippedDetailVO.setProductColor(productAttribute.getProductColor());
            inventoryFloatGlassShippedDetailVO.setProductLength(productAttribute.getProductLength());
            inventoryFloatGlassShippedDetailVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
            inventoryFloatGlassShippedDetailVO.setProductWidth(productAttribute.getProductWidth());
            filterList.add(inventoryFloatGlassShippedDetailVO);
        }
        if (CollectionUtils.isEmpty(filterList)) {
            return result;
        }
        for (InventoryFloatGlassDetailVO inventoryFloatGlassDetailVO : filterList) {
            // 汇总面积
            BigDecimal area = calculateArea(inventoryFloatGlassDetailVO.getQty(), inventoryFloatGlassDetailVO.getProductLength(), inventoryFloatGlassDetailVO.getProductWidth());
            // 汇总吨位
            BigDecimal weight = calculateWeight(area, inventoryFloatGlassDetailVO.getProductThickness());
            inventoryFloatGlassDetailVO.setArea(area);
            inventoryFloatGlassDetailVO.setWeight(weight);
        }
        // 按照颜色和厚度分组
        Map<String, List<InventoryFloatGlassDetailVO>> inventoryBatchDetailVOMap = filterList.stream()
                .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));
        for (Map.Entry<String, List<InventoryFloatGlassDetailVO>> entry : inventoryBatchDetailVOMap.entrySet()) {
            List<InventoryFloatGlassDetailVO> detailVOS = entry.getValue();
            // 统计面积
            BigDecimal totalArea = detailVOS.stream()
                    .map(InventoryFloatGlassDetailVO::getArea)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 统计重量
            BigDecimal totalWeight = detailVOS.stream()
                    .map(InventoryFloatGlassDetailVO::getWeight)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
            glassInventoryDynamicAttributeVO.setArea(totalArea);
            glassInventoryDynamicAttributeVO.setWeight(totalWeight);
            result.put(entry.getKey(), glassInventoryDynamicAttributeVO);
        }
        return result;
    }

    private Map<String, GlassInventoryDynamicAttributeVO> assembleTransitInventoryData(List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS,
                                                                                       List<InventoryQuayDetailVO> inventoryQuayDetailVOS) {
        Map<String, GlassInventoryDynamicAttributeVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(inventoryQuayDetailVOS) && CollectionUtils.isEmpty(inventoryFloatGlassShippedDetailVOS)) {
            return result;
        }
        List<InventoryFloatGlassShippedDetailVO> filterInventoryFloatList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOS)) {
            for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : inventoryFloatGlassShippedDetailVOS) {
                NewProductStockPointVO productAttribute = getProductAttribute(inventoryFloatGlassShippedDetailVO.getProductCode());
                if (null == productAttribute) {
                    continue;
                }
                inventoryFloatGlassShippedDetailVO.setProductColor(productAttribute.getProductColor());
                inventoryFloatGlassShippedDetailVO.setProductLength(productAttribute.getProductLength());
                inventoryFloatGlassShippedDetailVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
                inventoryFloatGlassShippedDetailVO.setProductWidth(productAttribute.getProductWidth());
                filterInventoryFloatList.add(inventoryFloatGlassShippedDetailVO);
            }
        }

        List<InventoryQuayDetailVO> filterInventoryQuayDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryQuayDetailVOS)) {
            for (InventoryQuayDetailVO inventoryQuayDetailVO : inventoryQuayDetailVOS) {
                // 解析颜色、厚度、长、宽
                NewProductStockPointVO productAttribute = getProductAttribute(inventoryQuayDetailVO.getProductCode());
                if (null == productAttribute) {
                    continue;
                }
                inventoryQuayDetailVO.setProductColor(productAttribute.getProductColor());
                inventoryQuayDetailVO.setProductLength(productAttribute.getProductLength());
                inventoryQuayDetailVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
                inventoryQuayDetailVO.setProductWidth(productAttribute.getProductWidth());
                filterInventoryQuayDetailList.add(inventoryQuayDetailVO);
            }
        }

        if (CollectionUtils.isEmpty(filterInventoryQuayDetailList)){
            for (InventoryQuayDetailVO inventoryQuayDetailVO : filterInventoryQuayDetailList) {
                // 汇总面积
                BigDecimal area = calculateArea(inventoryQuayDetailVO.getActualSentQuantity(), inventoryQuayDetailVO.getProductLength(), inventoryQuayDetailVO.getProductWidth());
                // 汇总吨位
                BigDecimal weight = calculateWeight(area, inventoryQuayDetailVO.getProductThickness());
                inventoryQuayDetailVO.setArea(area);
                inventoryQuayDetailVO.setWeight(weight);
            }
            // 按照颜色和厚度分组
            Map<String, List<InventoryQuayDetailVO>> inventoryQuayDetailMap = filterInventoryQuayDetailList.stream()
                    .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));
            for (Map.Entry<String, List<InventoryQuayDetailVO>> entry : inventoryQuayDetailMap.entrySet()) {
                List<InventoryQuayDetailVO> detailVOS = entry.getValue();
                // 统计面积
                BigDecimal totalArea = detailVOS.stream()
                        .map(InventoryQuayDetailVO::getArea)
                        .filter(Objects::nonNull) // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 统计重量
                BigDecimal totalWeight = detailVOS.stream()
                        .map(InventoryQuayDetailVO::getWeight)
                        .filter(Objects::nonNull) // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
                glassInventoryDynamicAttributeVO.setArea(totalArea);
                glassInventoryDynamicAttributeVO.setWeight(totalWeight);
                result.put(entry.getKey(), glassInventoryDynamicAttributeVO);
            }
        }

        if (CollectionUtils.isNotEmpty(filterInventoryFloatList)) {
            for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : filterInventoryFloatList) {
                if (null == inventoryFloatGlassShippedDetailVO.getActualSentQuantity()){
                    continue;
                }
                // 汇总面积
                BigDecimal area = calculateArea(inventoryFloatGlassShippedDetailVO.getActualSentQuantity(), inventoryFloatGlassShippedDetailVO.getProductLength(), inventoryFloatGlassShippedDetailVO.getProductWidth());
                // 汇总吨位
                BigDecimal weight = calculateWeight(area, inventoryFloatGlassShippedDetailVO.getProductThickness());
                inventoryFloatGlassShippedDetailVO.setArea(area);
                inventoryFloatGlassShippedDetailVO.setWeight(weight);
            }
            // 按照颜色和厚度分组
            Map<String, List<InventoryFloatGlassShippedDetailVO>> inventoryBatchDetailVOMap = filterInventoryFloatList.stream()
                    .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));
            for (Map.Entry<String, List<InventoryFloatGlassShippedDetailVO>> entry : inventoryBatchDetailVOMap.entrySet()) {
                String key = entry.getKey();
                List<InventoryFloatGlassShippedDetailVO> detailVOS = entry.getValue();
                // 统计面积
                BigDecimal totalArea = detailVOS.stream()
                        .map(InventoryFloatGlassShippedDetailVO::getArea)
                        .filter(Objects::nonNull) // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 统计重量
                BigDecimal totalWeight = detailVOS.stream()
                        .map(InventoryFloatGlassShippedDetailVO::getWeight)
                        .filter(Objects::nonNull) // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (result.containsKey(key)) {
                    // 更新
                    GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = result.get(key);
                    glassInventoryDynamicAttributeVO.setArea(glassInventoryDynamicAttributeVO.getArea().add(totalArea));
                    glassInventoryDynamicAttributeVO.setWeight(glassInventoryDynamicAttributeVO.getWeight().add(totalWeight));
                } else {
                    // 新增
                    GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
                    glassInventoryDynamicAttributeVO.setArea(totalArea);
                    glassInventoryDynamicAttributeVO.setWeight(totalWeight);
                    result.put(key, glassInventoryDynamicAttributeVO);
                }
            }
        }
        return result;
    }

    private Map<String, GlassInventoryDynamicAttributeVO> assembleLocalInventoryData(List<InventoryBatchDetailVO> inventoryBatchDetailVOList) {
        Map<String, GlassInventoryDynamicAttributeVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(inventoryBatchDetailVOList)) {
            return result;
        }

        List<InventoryBatchDetailVO> filterList = new ArrayList<>();
        for (InventoryBatchDetailVO inventoryBatchDetailVO : inventoryBatchDetailVOList) {
            // 解析颜色、厚度、长、宽
            NewProductStockPointVO productAttribute = getProductAttribute(inventoryBatchDetailVO.getProductCode());
            if (null == productAttribute || StringUtils.isBlank(inventoryBatchDetailVO.getCurrentQuantity())) {
                continue;
            }
            inventoryBatchDetailVO.setProductColor(productAttribute.getProductColor());
            inventoryBatchDetailVO.setProductLength(productAttribute.getProductLength());
            inventoryBatchDetailVO.setProductThickness(productAttribute.getProductThickness() != null ? productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) : null);
            inventoryBatchDetailVO.setProductWidth(productAttribute.getProductWidth());
            filterList.add(inventoryBatchDetailVO);
        }
        if (CollectionUtils.isEmpty(filterList)) {
            return result;
        }

        for (InventoryBatchDetailVO inventoryBatchDetailVO : filterList) {
            // 汇总面积
            BigDecimal area = calculateArea(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()), inventoryBatchDetailVO.getProductLength(), inventoryBatchDetailVO.getProductWidth());
            // 汇总吨位
            BigDecimal weight = calculateWeight(area, inventoryBatchDetailVO.getProductThickness());
            inventoryBatchDetailVO.setArea(area);
            inventoryBatchDetailVO.setWeight(weight);
        }

        // 按照颜色和厚度分组
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMap = filterList.stream()
                .collect(Collectors.groupingBy(item -> item.getProductColor() + "_" + item.getProductThickness()));

        for (Map.Entry<String, List<InventoryBatchDetailVO>> entry : inventoryBatchDetailVOMap.entrySet()) {
            List<InventoryBatchDetailVO> detailVOS = entry.getValue();
            // 统计面积
            BigDecimal totalArea = detailVOS.stream()
                    .map(InventoryBatchDetailVO::getArea)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 统计重量
            BigDecimal totalWeight = detailVOS.stream()
                    .map(InventoryBatchDetailVO::getWeight)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            GlassInventoryDynamicAttributeVO glassInventoryDynamicAttributeVO = new GlassInventoryDynamicAttributeVO();
            glassInventoryDynamicAttributeVO.setArea(totalArea);
            glassInventoryDynamicAttributeVO.setWeight(totalWeight);
            result.put(entry.getKey(), glassInventoryDynamicAttributeVO);
        }
        return result;
    }

    /**
     * 根据物料（原片）编码 截取获取物料属性
     *
     * @param productCode
     * @return
     */
    public static NewProductStockPointVO getProductAttribute(String productCode) {
        if (productCode == null || productCode.length() < 12) {
            return null;
        }
        String trimmed = productCode.substring(3);
        BigDecimal productLength = new BigDecimal(trimmed.substring(0, 4));
        BigDecimal productWidth = new BigDecimal(trimmed.substring(4, 8));
        BigDecimal productThickness = new BigDecimal(trimmed.substring(9, 10) + "." + trimmed.substring(10, 11));
        String productColor = trimmed.substring(12);

        return NewProductStockPointVO.builder()
                .productCode(productCode)
                .productLength(productLength)
                .productWidth(productWidth)
                .productThickness(productThickness)
                .productColor(productColor).build();
    }

    private BigDecimal calculateArea(BigDecimal quantity, BigDecimal productLength, BigDecimal productWidth) {
        // 长 *  宽 * 数量 / 1000000
        return productLength.multiply(productWidth).multiply(quantity).divide(new BigDecimal(1000000), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateWeight(BigDecimal area, BigDecimal productThickness) {
        // 面积 *  厚度 * 2.5 / 1000
        return area.multiply(productThickness).multiply(new BigDecimal("2.5")).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
    }

    private List<String> getFutureMonth() {
        List<String> monthList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM");

        // 获取当前年月
        YearMonth currentYearMonth = YearMonth.now();

        // 添加当前月、下个月、下下个月（自动处理跨年）
        monthList.add(currentYearMonth.format(formatter));
        monthList.add(currentYearMonth.plusMonths(1).format(formatter));
        monthList.add(currentYearMonth.plusMonths(2).format(formatter));
        return monthList;
    }

}