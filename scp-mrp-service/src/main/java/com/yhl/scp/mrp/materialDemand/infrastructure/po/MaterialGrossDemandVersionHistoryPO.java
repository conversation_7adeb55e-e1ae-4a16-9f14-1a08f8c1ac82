package com.yhl.scp.mrp.materialDemand.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialGrossDemandVersionHistoryPO</code>
 * <p>
 * 材料毛需求计算版本（历史）PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11 10:37:34
 */
public class MaterialGrossDemandVersionHistoryPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 939807838897993892L;

    /**
     * 主键
     */
    private String id;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

}
