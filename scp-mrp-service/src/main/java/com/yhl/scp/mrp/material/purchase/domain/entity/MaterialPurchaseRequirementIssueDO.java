package com.yhl.scp.mrp.material.purchase.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>MaterialPurchaseRequirementIssueDO</code>
 * <p>
 * 材料采购需求下发记录DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:01:52
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPurchaseRequirementIssueDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 515711711142794898L;

    /**
     * 主键
     */
        
    private String id;
        
    /**
     * 下发状态
     */
    private String issueStatus;
        
    /**
     * 下发时间
     */
    private Date issueTime;
        
    /**
     * 下发人
     */
    private String issuer;
        
    /**
     * 下发批次号
     */
    private String issueBatchCode;
        
    /**
     * 版本号
     */
    private Integer versionValue;

}
