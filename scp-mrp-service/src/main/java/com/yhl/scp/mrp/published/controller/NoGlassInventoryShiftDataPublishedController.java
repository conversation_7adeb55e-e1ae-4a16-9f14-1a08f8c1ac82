package com.yhl.scp.mrp.published.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.published.dto.NoGlassInventoryShiftDataPublishedDTO;
import com.yhl.scp.mrp.published.service.NoGlassInventoryShiftDataPublishedService;
import com.yhl.scp.mrp.published.vo.NoGlassInventoryShiftDataPublishedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>NoGlassInventoryShiftDataPublishedController</code>
 * <p>
 * 非原片库存推移发布主表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 14:02:42
 */
@Slf4j
@Api(tags = "非原片库存推移发布主表控制器")
@RestController
@RequestMapping("noGlassInventoryShiftDataPublished")
public class NoGlassInventoryShiftDataPublishedController extends BaseController {

    @Resource
    private NoGlassInventoryShiftDataPublishedService noGlassInventoryShiftDataPublishedService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<NoGlassInventoryShiftDataPublishedVO>> page() {
        List<NoGlassInventoryShiftDataPublishedVO> noGlassInventoryShiftDataPublishedList = noGlassInventoryShiftDataPublishedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NoGlassInventoryShiftDataPublishedVO> pageInfo = new PageInfo<>(noGlassInventoryShiftDataPublishedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NoGlassInventoryShiftDataPublishedDTO noGlassInventoryShiftDataPublishedDTO) {
        return noGlassInventoryShiftDataPublishedService.doCreate(noGlassInventoryShiftDataPublishedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NoGlassInventoryShiftDataPublishedDTO noGlassInventoryShiftDataPublishedDTO) {
        return noGlassInventoryShiftDataPublishedService.doUpdate(noGlassInventoryShiftDataPublishedDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        noGlassInventoryShiftDataPublishedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<NoGlassInventoryShiftDataPublishedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, noGlassInventoryShiftDataPublishedService.selectByPrimaryKey(id));
    }

}
