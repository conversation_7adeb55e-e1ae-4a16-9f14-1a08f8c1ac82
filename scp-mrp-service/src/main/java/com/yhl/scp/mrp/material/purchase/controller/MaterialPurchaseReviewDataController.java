package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDataDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDataService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewDataController</code>
 * <p>
 * 材料采购评审控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:24
 */
@Slf4j
@Api(tags = "材料采购评审控制器")
@RestController
@RequestMapping("materialPurchaseReviewData")
public class MaterialPurchaseReviewDataController extends BaseController {

    @Resource
    private MaterialPurchaseReviewDataService materialPurchaseReviewDataService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialPurchaseReviewDataVO>> page() {
        List<MaterialPurchaseReviewDataVO> materialPurchaseReviewDataList = materialPurchaseReviewDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseReviewDataVO> pageInfo = new PageInfo<>(materialPurchaseReviewDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {
        return materialPurchaseReviewDataService.doCreate(materialPurchaseReviewDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {
        return materialPurchaseReviewDataService.doUpdate(materialPurchaseReviewDataDTO);
    }

    @ApiOperation(value = "修改（选择性的）")
    @PostMapping(value = "updateSelective")
    public BaseResponse<Void> updateSelective(@RequestBody MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {
        return materialPurchaseReviewDataService.doUpdateSelective(materialPurchaseReviewDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseReviewDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialPurchaseReviewDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewDataService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "计算")
    @PostMapping(value = "calc")
    @BusinessMonitorLog(businessCode = "材料采购评审计算", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> calc(@RequestBody MaterialPurchaseReviewDTO materialPurchaseReviewDTO) {
        String redisKey = String.join("#", RedisKeyManageEnum.MATERIAL_PURCHASE_REVIEW_CALC.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有采购评审正在计算中，请等待计算完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 采购评审计算
            return materialPurchaseReviewDataService.calc(materialPurchaseReviewDTO);
        } catch (Exception e) {
            log.error("材料采购评审计算失败", e);
            throw new BusinessException("材料采购评审计算失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "release")
    @BusinessMonitorLog(businessCode = "材料采购评审发布", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> release() {
        String redisKey = String.join("#", RedisKeyManageEnum.MATERIAL_PURCHASE_REVIEW_RELEASE.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有采购评审正在发布中，请等待发布完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 采购评审计算
            return materialPurchaseReviewDataService.release();
        } catch (Exception e) {
            log.error("材料采购评审发布失败", e);
            throw new BusinessException("材料采购评审发布失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

    @ApiOperation(value = "报表导出")
    @GetMapping(value = "exportData")
    public void export(@RequestParam(required = false, value = "versionId") String versionId) {
        materialPurchaseReviewDataService.exportData(this.response, versionId);
    }
}
