package com.yhl.scp.mrp.material.arrival.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialPurchaseStorage;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.TransferStatusEnum;
import com.yhl.scp.mrp.inventory.convertor.InventoryQuayDetailConvertor;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryQuayDetailDTO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialPurchaseStorageConvertor;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialPurchaseStorageDO;
import com.yhl.scp.mrp.material.arrival.domain.service.MaterialPurchaseStorageDomainService;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseStorageDTO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialPurchaseStorageDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialPurchaseStoragePO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseStorageService;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseStorageVO;

import cn.hutool.core.collection.CollUtil;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanTransferDTO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanTransferService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialPurchaseStorageServiceImpl</code>
 * <p>
 * 材料采购入库记录应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:12:40
 */
@Slf4j
@Service
public class MaterialPurchaseStorageServiceImpl extends AbstractService implements MaterialPurchaseStorageService {

    @Resource
    private MaterialPurchaseStorageDao materialPurchaseStorageDao;

    @Resource
    private MaterialPurchaseStorageDomainService materialPurchaseStorageDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialPurchaseStorageService materialPurchaseStorageService;

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;

    @Resource
    private MaterialPlanTransferService materialPlanTransferService;

    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @Resource
    private NewDcpFeign newDcpFeign;


    @Override
    public BaseResponse<Void> doCreate(MaterialPurchaseStorageDTO materialPurchaseStorageDTO) {
        // 0.数据转换
        MaterialPurchaseStorageDO materialPurchaseStorageDO = MaterialPurchaseStorageConvertor.INSTANCE.dto2Do(materialPurchaseStorageDTO);
        MaterialPurchaseStoragePO materialPurchaseStoragePO = MaterialPurchaseStorageConvertor.INSTANCE.dto2Po(materialPurchaseStorageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseStorageDomainService.validation(materialPurchaseStorageDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseStoragePO);
        materialPurchaseStorageDao.insertWithPrimaryKey(materialPurchaseStoragePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPurchaseStorageDTO materialPurchaseStorageDTO) {
        // 0.数据转换
        MaterialPurchaseStorageDO materialPurchaseStorageDO = MaterialPurchaseStorageConvertor.INSTANCE.dto2Do(materialPurchaseStorageDTO);
        MaterialPurchaseStoragePO materialPurchaseStoragePO = MaterialPurchaseStorageConvertor.INSTANCE.dto2Po(materialPurchaseStorageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseStorageDomainService.validation(materialPurchaseStorageDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseStoragePO);
        materialPurchaseStorageDao.update(materialPurchaseStoragePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseStorageDTO> list) {
        List<MaterialPurchaseStoragePO> newList = MaterialPurchaseStorageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseStorageDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseStorageDTO> list) {
        List<MaterialPurchaseStoragePO> newList = MaterialPurchaseStorageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseStorageDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialPurchaseStorageDTO> list) {
        List<MaterialPurchaseStoragePO> newList = MaterialPurchaseStorageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseStorageDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseStorageDao.deleteBatch(idList);
        }
        return materialPurchaseStorageDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseStorageVO selectByPrimaryKey(String id) {
        MaterialPurchaseStoragePO po = materialPurchaseStorageDao.selectByPrimaryKey(id);
        return MaterialPurchaseStorageConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_storage")
    public List<MaterialPurchaseStorageVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_storage")
    public List<MaterialPurchaseStorageVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseStorageVO> dataList = materialPurchaseStorageDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseStorageServiceImpl target = springBeanUtils.getBean(MaterialPurchaseStorageServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseStorageVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseStoragePO> list = materialPurchaseStorageDao.selectByParams(params);
        return MaterialPurchaseStorageConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseStorageVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_PURCHASE_STORAGE.getCode();
    }

    @Override
    public List<MaterialPurchaseStorageVO> invocation(List<MaterialPurchaseStorageVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doDisposePurchaseStorageForJob(Integer moveMinute) {
        log.info("材料到货跟踪根据入库记录数据维护到货跟踪状态开始");
        // 36小时前
        Date startModifyTime = DateUtils.moveHour(new Date(), -36);

        // 获取采购入库记录数据
        List<MaterialPurchaseStorageVO> purchaseStoragList = this.selectByParams(ImmutableMap.of("startModifyTime", startModifyTime));
        purchaseStoragList = purchaseStoragList.stream().filter(
                        e -> StringUtils.isNotEmpty(e.getPurchaseOrderCode())
                                && StringUtils.isNotEmpty(e.getPurchaseOrderLineCode())
                                && StringUtils.isNotEmpty(e.getDeliveryNoteCode())
                                && StringUtils.isNotEmpty(e.getTicketNum())
                                && StringUtils.isNotEmpty(e.getProductCode())
                                && !org.apache.commons.lang3.StringUtils.equals(e.getWhetherMatches(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(purchaseStoragList)) {
            log.info("材料到货跟踪根据入库记录维护送货单状态，未获取入库记录数据");
            return;
        }

        // 根据采购单号 + 采购订单行号 + 送货单号 + 送货明细号 + 物料编码分组
        Map<String, MaterialPurchaseStorageVO> materialPurchaseStorageMap = purchaseStoragList.stream()
                .collect(Collectors.toMap(data -> String.join("_",
                                data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(),
                                data.getDeliveryNoteCode(), data.getTicketNum(), data.getProductCode()),
                        Function.identity(), (v1, v2) -> {
                            v1.setStorageQuantity(v1.getStorageQuantity().add(v2.getStorageQuantity()));
                            return v1;
                        }));

        // 根据采购单号 + 采购订单行号 + 物料编码分组
        Map<String, List<MaterialPurchaseStorageVO>> materialPurchaseStorageMap02 = purchaseStoragList.stream()
                .collect(Collectors.groupingBy(data -> String.join("_",
                        data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(), data.getProductCode())));

        // 根据采购单号 + 采购订单行号 + 物料编码汇总
        List<String> combineKeys = purchaseStoragList.stream()
                .map(data -> String.join("_", data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(), data.getProductCode()))
                .distinct()
                .collect(Collectors.toList());

        // 根据采购单号 + 采购订单行号 + 送货单号 + 送货明细号 + 物料编码汇总
        List<String> combineKeys02 = purchaseStoragList.stream()
                .map(data -> String.join("_", data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(),
                        data.getDeliveryNoteCode(), data.getTicketNum(), data.getProductCode()))
                .distinct()
                .collect(Collectors.toList());

        // 获取对应的材料到货跟踪数据（采购单号 + 采购订单行号 + 物料编码）
        List<MaterialArrivalTrackingVO> trackingList =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("combineKeys", combineKeys));

        if (CollUtil.isEmpty(trackingList)) {
            log.info("材料到货跟踪根据入库记录维护送货单状态，未获取<到货跟踪>数据");
            return;
        }

        // 获取送货单（采购单号 + 采购订单行号 + 送货单号 + 物料编码）
        List<MaterialDeliveryNoteVO> materialDeliveryNoteVOList =
                materialDeliveryNoteService.selectByParams(ImmutableMap.of("combineKeys02", combineKeys02));
        // 根据采购单号 + 采购订单行号 + 送货单号 + 送货明细号 + 物料编码分组
        Map<String, MaterialDeliveryNoteVO> materialDeliveryNoteMap = materialDeliveryNoteVOList.stream()
                .collect(Collectors.toMap(data -> String.join("_",
                                data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(),
                                data.getDeliveryNoteCode(), data.getTicketNum(), data.getProductCode()),
                        Function.identity(), (v1, v2) -> v1));

        //根据ID跟新对应的状态为已入库
        List<MaterialArrivalTrackingDTO> batchAdd = new ArrayList<>();
        List<MaterialArrivalTrackingDTO> batchUpdate = new ArrayList<>();
        List<MaterialPurchaseStorageDTO> materialPurchaseStorageUpdateList = new ArrayList<>();

        for (MaterialArrivalTrackingVO sourceTrackingVO : trackingList) {

            if (ArrivalStatusEnum.DELIVERED.getCode().equals(sourceTrackingVO.getArrivalStatus()) && !materialPurchaseStorageMap.isEmpty()) {
                // 已送货的改为已入库，送货入库
                // 获取入库记录通过 采购单号 + 采购订单行号 + 送货单号 + 物料编码
                MaterialPurchaseStorageVO materialPurchaseStorageVO = materialPurchaseStorageMap.get(
                        String.join("_",
                                sourceTrackingVO.getPurchaseOrderCode(), sourceTrackingVO.getPurchaseOrderLineCode(),
                                sourceTrackingVO.getDeliveryNoteCode(), sourceTrackingVO.getTicketNum(), sourceTrackingVO.getMaterialCode()));

                if (Objects.isNull(materialPurchaseStorageVO)) {
                    log.info("送货入库{}未获取到入库记录", String.join("_",
                            sourceTrackingVO.getPurchaseOrderCode(), sourceTrackingVO.getPurchaseOrderLineCode(),
                            sourceTrackingVO.getDeliveryNoteCode(), sourceTrackingVO.getTicketNum(), sourceTrackingVO.getMaterialCode()));
                    continue;
                }

                handleDelivered(sourceTrackingVO, materialPurchaseStorageVO, trackingList, batchAdd,
                        batchUpdate,materialPurchaseStorageUpdateList);

            } else if (ArrivalStatusEnum.PLAN_PRUCHASE.getCode().equals(sourceTrackingVO.getArrivalStatus()) && !materialPurchaseStorageMap02.isEmpty()) {

                // 计划采购的改为已入库，直接入库
                // 获取入库记录通过 采购单号 + 采购订单行号 + 物料编码
                List<MaterialPurchaseStorageVO> materialPurchaseStorageVOS = materialPurchaseStorageMap02.get(
                        String.join("_", sourceTrackingVO.getPurchaseOrderCode(),
                                sourceTrackingVO.getPurchaseOrderLineCode(), sourceTrackingVO.getMaterialCode()));

                if (CollectionUtils.isEmpty(materialPurchaseStorageVOS)) {
                    log.info("直接入库{}未获取到入库记录", String.join("_", sourceTrackingVO.getPurchaseOrderCode(),
                            sourceTrackingVO.getPurchaseOrderLineCode(), sourceTrackingVO.getMaterialCode()));
                    continue;
                }

                handlePlanPurchased(sourceTrackingVO, materialPurchaseStorageVOS,
                        materialDeliveryNoteMap,batchAdd, batchUpdate,materialPurchaseStorageUpdateList);
            }
        }

        if (CollUtil.isNotEmpty(batchUpdate)) {
            materialArrivalTrackingService.doUpdateBatchSelective(batchUpdate);
        }
        if (CollUtil.isNotEmpty(batchAdd)) {
            materialArrivalTrackingService.doCreateBatch(batchAdd);
        }
        if (CollectionUtils.isNotEmpty(materialPurchaseStorageUpdateList)) {
            this.doUpdateBatchSelective(materialPurchaseStorageUpdateList);
        }
        log.info("材料到货跟踪根据入库记录数据维护到货跟踪状态结束");
    }

    /**
     * 处理到货跟踪入库（发货状态）
     *
     * @param sourceTrackingVO               到货跟踪
     * @param materialPurchaseStorageVO      入库记录
     * @param trackingList                   到货跟踪总数据
     * @param batchAdd                       添加对象
     * @param batchUpdate                    修改对象
     * @param materialPurchaseStorageUpdateList  修改入库记录集合
     */
    private void handleDelivered(MaterialArrivalTrackingVO sourceTrackingVO,
                                 MaterialPurchaseStorageVO materialPurchaseStorageVO,
                                 List<MaterialArrivalTrackingVO> trackingList,
                                 List<MaterialArrivalTrackingDTO> batchAdd,
                                 List<MaterialArrivalTrackingDTO> batchUpdate,
                                 List<MaterialPurchaseStorageDTO> materialPurchaseStorageUpdateList) {

        // 在途数量和入库数量一致，正好用完，直接修改状态为和入库数量
        if (sourceTrackingVO.getPredictArrivalQuantity().compareTo(materialPurchaseStorageVO.getStorageQuantity()) == 0) {
            // 正好用完
            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            update.setArrivalStatus(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode());
            update.setInventoryQuantity(materialPurchaseStorageVO.getStorageQuantity());
            update.setPredictArrivalQuantity(BigDecimal.ZERO);
            update.setPredictArrivalDate(null);
            update.setRemark("正好用完");
            batchUpdate.add(update);
        } else if (sourceTrackingVO.getPredictArrivalQuantity().compareTo(materialPurchaseStorageVO.getStorageQuantity()) > 0) {
            // 用不完
            log.info("{}在途数量{}入库数量{}，待发货数量大于入库数量，一次用不完，要补单", sourceTrackingVO.getPurchaseOrderCode() + "_" + sourceTrackingVO.getDeliveryNoteCode() + "_" + sourceTrackingVO.getMaterialCode(),
                    sourceTrackingVO.getWaitDeliveryQuantity(), materialPurchaseStorageVO.getStorageQuantity());

            // 预计到货数量减去入库数量的差值
            BigDecimal subtract = sourceTrackingVO.getPredictArrivalQuantity().subtract(materialPurchaseStorageVO.getStorageQuantity());

            // 根据采购订单和物料编码匹配到货跟踪表中是否还有到货状态为“计划采购”的行，过滤掉自己本身这条，如果有，则将送货差值加到待发货数量中
            List<MaterialArrivalTrackingVO> filterMaterialArrivalTrackingList = trackingList.stream()
                    .filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()))
                    .filter(data -> !data.getId().equals(sourceTrackingVO.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterMaterialArrivalTrackingList)) {
                MaterialArrivalTrackingVO materialArrivalTrackingVO = filterMaterialArrivalTrackingList.get(0);
                MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
                BeanUtils.copyProperties(materialArrivalTrackingVO, materialArrivalTrackingDTO);
                materialArrivalTrackingDTO.setWaitDeliveryQuantity(materialArrivalTrackingDTO.getWaitDeliveryQuantity().add(subtract));
                materialArrivalTrackingDTO.setRemark("在途数量大于入库数量，用不完，修改待发货数量");
                batchUpdate.add(materialArrivalTrackingDTO);
            } else {
                // 用不完
                MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                        .id(sourceTrackingVO.getId())
                        .build();
                update.setArrivalStatus(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode());
                update.setInventoryQuantity(materialPurchaseStorageVO.getStorageQuantity());
                update.setPredictArrivalQuantity(BigDecimal.ZERO);
                update.setPredictArrivalDate(null);
                update.setRemark("在途数量大于入库数量，用不完，拆单");
                batchUpdate.add(update);

                // 如果到货跟踪表中没有到货状态为“计划采购”的行，则复制当前采购订单、物料编码的数据，新增一行，待发货数量为0，预计送货数量为差值
                MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
                BeanUtils.copyProperties(sourceTrackingVO, materialArrivalTrackingDTO);
                materialArrivalTrackingDTO.setId(null);
                materialArrivalTrackingDTO.setWaitDeliveryQuantity(BigDecimal.ZERO);
                materialArrivalTrackingDTO.setPredictArrivalQuantity(subtract);
                materialArrivalTrackingDTO.setArrivalStatus(ArrivalStatusEnum.DELIVERED.getCode());
                materialArrivalTrackingDTO.setRemark("在途数量大于入库数量，用不完，拆单");
                batchAdd.add(materialArrivalTrackingDTO);
            }
        } else if (sourceTrackingVO.getPredictArrivalQuantity().compareTo(materialPurchaseStorageVO.getStorageQuantity()) < 0) {
            // 在途小于入库，就把记录更新成入库状态就好了，入库多的量也没关系
            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            update.setRemark("不够用");
            update.setArrivalStatus(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode());
            batchUpdate.add(update);
        }

        MaterialPurchaseStorageDTO materialPurchaseStorageDTO = new MaterialPurchaseStorageDTO();
        materialPurchaseStorageDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
        materialPurchaseStorageDTO.setId(materialPurchaseStorageVO.getId());
        materialPurchaseStorageUpdateList.add(materialPurchaseStorageDTO);
    }

    /**
     * 处理到货跟踪入库（采购状态）
     *
     * @param sourceTrackingVO                  到货跟踪
     * @param materialPurchaseStorageVOS        入库记录
     * @param materialDeliveryNoteMap           送货单
     * @param batchAdd                          添加对象
     * @param batchUpdate                       修改对象
     * @param materialPurchaseStorageUpdateList
     */
    private void handlePlanPurchased(MaterialArrivalTrackingVO sourceTrackingVO,
                                     List<MaterialPurchaseStorageVO> materialPurchaseStorageVOS,
                                     Map<String, MaterialDeliveryNoteVO> materialDeliveryNoteMap,
                                     List<MaterialArrivalTrackingDTO> batchAdd,
                                     List<MaterialArrivalTrackingDTO> batchUpdate,
                                     List<MaterialPurchaseStorageDTO> materialPurchaseStorageUpdateList) {

        // 过滤出在送货单中不存在的入库数据，就是直接入库的，并汇总入库数量
        MaterialPurchaseStorageVO materialPurchaseStorageVO = materialPurchaseStorageVOS.stream()
                .filter(data -> !materialDeliveryNoteMap.containsKey(String.join("_",
                        data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(),
                        data.getDeliveryNoteCode(), data.getTicketNum(), data.getProductCode())))
                .reduce((first, second) -> {
                    MaterialPurchaseStorageVO combined = new MaterialPurchaseStorageVO();
                    BeanUtils.copyProperties(first, combined);
                    BigDecimal total = first.getStorageQuantity().add(second.getStorageQuantity());
                    combined.setStorageQuantity(total);
                    return combined;
                }).orElse(null);

        if (Objects.isNull(materialPurchaseStorageVO)) return;

        // 区分两种情况：入库入完 和 入库入不完
        BigDecimal waitDeliveryQuantity = BigDecimal.ZERO;
        if (sourceTrackingVO.getWaitDeliveryQuantity().compareTo(materialPurchaseStorageVO.getStorageQuantity()) > 0) {

            // 待发货数量（原待发货数量 减去 入库数量）
            waitDeliveryQuantity = sourceTrackingVO.getWaitDeliveryQuantity().subtract(materialPurchaseStorageVO.getStorageQuantity());

            // 入库入不完，添加一条采购的用于存储剩余计划采购数量
            MaterialArrivalTrackingDTO add = new MaterialArrivalTrackingDTO();
            BeanUtils.copyProperties(sourceTrackingVO, add);
            add.setId(UUID.randomUUID().toString());
            add.setWaitDeliveryQuantity(waitDeliveryQuantity);
            add.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
            add.setDeliveryNoteCode(null);
            add.setTicketNum(null);
            add.setShippingDate(null);
            add.setPredictArrivalDate(null);
            add.setPredictArrivalQuantity(null);
            add.setRemark("没有送货直接入库（入库入不完）");
            batchAdd.add(add);

            // 入库入不完，修改入库数量
            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            update.setArrivalStatus(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode());
            update.setWaitDeliveryQuantity(BigDecimal.ZERO);
            update.setInventoryQuantity(materialPurchaseStorageVO.getStorageQuantity());
            update.setPredictArrivalQuantity(BigDecimal.ZERO);
            update.setPredictArrivalDate(null);
            update.setRemark("没有送货直接入库（入库入不完）");
            batchUpdate.add(update);
        }else {

            // 入库入完，修改入库数量
            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            update.setArrivalStatus(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode());
            update.setWaitDeliveryQuantity(waitDeliveryQuantity);
            update.setInventoryQuantity(materialPurchaseStorageVO.getStorageQuantity());
            update.setPredictArrivalQuantity(BigDecimal.ZERO);
            update.setPredictArrivalDate(null);
            update.setRemark("没有送货直接入库（入库入完）");
            batchUpdate.add(update);
        }

        for (MaterialPurchaseStorageVO vo : materialPurchaseStorageVOS) {
            MaterialPurchaseStorageDTO materialPurchaseStorageDTO = new MaterialPurchaseStorageDTO();
            materialPurchaseStorageDTO.setId(vo.getId());
            materialPurchaseStorageDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
            materialPurchaseStorageUpdateList.add(materialPurchaseStorageDTO);
        }
    }

    @Override
    public BaseResponse<Void> syncMaterialPurchaseStorage(String scenario, List<MesMaterialPurchaseStorage> materialPurchaseStorages) {
        if (CollectionUtils.isEmpty(materialPurchaseStorages)) {
            return BaseResponse.success();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);

        List<String> productCodes =
                materialPurchaseStorages.stream().map(MesMaterialPurchaseStorage::getItemCode).distinct().collect(Collectors.toList());
        List<String> purchaseOrderCodes =
                materialPurchaseStorages.stream().map(MesMaterialPurchaseStorage::getPoNumber).distinct().collect(Collectors.toList());
        List<String> warehouseEntryCodes =
                materialPurchaseStorages.stream().map(MesMaterialPurchaseStorage::getTicketId).distinct().collect(Collectors.toList());

        Map<String, Object> params = MapUtil.newHashMap();
        params.put("productCodes", productCodes);
        params.put("purchaseOrderCodes", purchaseOrderCodes);
        params.put("warehouseEntryCodes",warehouseEntryCodes);

        List<MaterialPurchaseStorageVO> oldPOList =
                materialPurchaseStorageService.selectByProductOrPurchaseOrderOrWarehouseEntry(params);
        Map<String, MaterialPurchaseStorageVO> oldPOMap = oldPOList.stream().collect(Collectors.toMap(t -> t.getProductCode() +
                "|" + t.getPurchaseOrderCode() + "|" + t.getWarehouseEntryCode(), Function.identity(), (v1, v2) -> v1));
        List<MaterialPurchaseStorageDTO> insertMaterialPurchaseStorageDTOS = org.apache.commons.compress.utils.Lists.newArrayList();
        List<MaterialPurchaseStorageDTO> updateMaterialPurchaseStorageDTOS = Lists.newArrayList();
        List<String> poNumbers=materialPurchaseStorages.stream().map(MesMaterialPurchaseStorage::getPoNumber).distinct().collect(Collectors.toList());
        List<InventoryFloatGlassShippedDetailVO> shippedDetailVOS = inventoryFloatGlassShippedDetailService
                .selectByParams(ImmutableMap.of("shipmentMethod", "汽车运输", "planNumbers", poNumbers));
        Map<String, List<InventoryFloatGlassShippedDetailVO>> shippedDetailMap = shippedDetailVOS.stream()
                .collect(Collectors.groupingBy(t -> t.getProductCode() + "|" + t.getPlanNumber()));
        List<InventoryFloatGlassShippedDetailDTO> updateDetailDTOS = Lists.newArrayList();

        // 获取已发布并且目标库存点类型是本厂的调拨计划，并根据物料编码分组
        List<MaterialPlanTransferVO> materialPlanTransferVOList = materialPlanTransferService.selectVOByParams(
                ImmutableMap.of("transferStatus", TransferStatusEnum.PUBLISHED.getCode(),"stockPointTypeTo", StockPointTypeEnum.BC.getCode()));
        Map<String, List<MaterialPlanTransferVO>> materialPlanTransferProductCodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materialPlanTransferVOList)) {
            materialPlanTransferProductCodeMap = materialPlanTransferVOList.stream()
                    .collect(Collectors.groupingBy(MaterialPlanTransferVO::getProductCode));
        }

        for (MesMaterialPurchaseStorage mesMaterialPurchaseStorage : materialPurchaseStorages) {
            MaterialPurchaseStorageDTO materialPurchaseStorageDTO = new MaterialPurchaseStorageDTO();
            String dataKey = mesMaterialPurchaseStorage.getItemCode() + "|" + mesMaterialPurchaseStorage.getPoNumber()
                    + "|" + mesMaterialPurchaseStorage.getTicketId();
            String shippedDetailKey = mesMaterialPurchaseStorage.getItemCode() + "|" + mesMaterialPurchaseStorage.getPoNumber();
            if (oldPOMap.containsKey(dataKey)) {
                MaterialPurchaseStorageVO oldPO = oldPOMap.get(dataKey);
                BeanUtils.copyProperties(oldPO, materialPurchaseStorageDTO);
                materialPurchaseStorageDTO.setProductCode(mesMaterialPurchaseStorage.getItemCode());
                materialPurchaseStorageDTO.setProductName(mesMaterialPurchaseStorage.getDescriptions());
                materialPurchaseStorageDTO.setPurchaseOrderCode(mesMaterialPurchaseStorage.getPoNumber());
                materialPurchaseStorageDTO.setProductUnit(mesMaterialPurchaseStorage.getUom());
                materialPurchaseStorageDTO.setWarehouseEntryCode(mesMaterialPurchaseStorage.getTicketId());
                materialPurchaseStorageDTO.setDeliveryNoteCode(mesMaterialPurchaseStorage.getGroupNum());
                materialPurchaseStorageDTO.setCompanyCode(mesMaterialPurchaseStorage.getScheduleRegionCode());
                materialPurchaseStorageDTO.setConsignmentFlag(mesMaterialPurchaseStorage.getNotificationType());
                materialPurchaseStorageDTO.setPurchaseOrderLineCode(mesMaterialPurchaseStorage.getLineNum());
                materialPurchaseStorageDTO.setTicketNum(mesMaterialPurchaseStorage.getTicketNum());
                if (Objects.nonNull(mesMaterialPurchaseStorage.getInstockTime())){
                    materialPurchaseStorageDTO.setStorageTime(mesMaterialPurchaseStorage.getInstockTime());
                }
                if (Objects.nonNull(mesMaterialPurchaseStorage.getInstockQty())) {
                    materialPurchaseStorageDTO.setStorageQuantity(new BigDecimal(mesMaterialPurchaseStorage.getInstockQty()));
                }

                updateMaterialPurchaseStorageDTOS.add(materialPurchaseStorageDTO);
            } else {
                materialPurchaseStorageDTO.setProductCode(mesMaterialPurchaseStorage.getItemCode());
                materialPurchaseStorageDTO.setProductName(mesMaterialPurchaseStorage.getDescriptions());
                materialPurchaseStorageDTO.setPurchaseOrderCode(mesMaterialPurchaseStorage.getPoNumber());
                materialPurchaseStorageDTO.setProductUnit(mesMaterialPurchaseStorage.getUom());
                materialPurchaseStorageDTO.setWarehouseEntryCode(mesMaterialPurchaseStorage.getTicketId());
                materialPurchaseStorageDTO.setDeliveryNoteCode(mesMaterialPurchaseStorage.getGroupNum());
                materialPurchaseStorageDTO.setCompanyCode(mesMaterialPurchaseStorage.getScheduleRegionCode());
                materialPurchaseStorageDTO.setConsignmentFlag(mesMaterialPurchaseStorage.getNotificationType());
                materialPurchaseStorageDTO.setPurchaseOrderLineCode(mesMaterialPurchaseStorage.getLineNum());
                materialPurchaseStorageDTO.setTicketNum(mesMaterialPurchaseStorage.getTicketNum());
                if (Objects.nonNull(mesMaterialPurchaseStorage.getInstockTime())){
                    materialPurchaseStorageDTO.setStorageTime(mesMaterialPurchaseStorage.getInstockTime());
                }
                if (Objects.nonNull(mesMaterialPurchaseStorage.getInstockQty())) {
                    materialPurchaseStorageDTO.setStorageQuantity(new BigDecimal(mesMaterialPurchaseStorage.getInstockQty()));
                }

                insertMaterialPurchaseStorageDTOS.add(materialPurchaseStorageDTO);
                if(shippedDetailMap.containsKey(shippedDetailKey)){
                    List<InventoryFloatGlassShippedDetailVO> detailVOS = shippedDetailMap.get(shippedDetailKey);
                    for (InventoryFloatGlassShippedDetailVO detailVO : detailVOS) {
                        InventoryFloatGlassShippedDetailDTO detailDTO = new InventoryFloatGlassShippedDetailDTO();
                        BeanUtils.copyProperties(detailVO, detailDTO);
                        detailDTO.setStorageFlag(YesOrNoEnum.YES.getCode());
                        detailDTO.setEnabled(YesOrNoEnum.NO.getCode());
                        updateDetailDTOS.add(detailDTO);
                    }
                }
            }

        }
        if (CollectionUtils.isNotEmpty(insertMaterialPurchaseStorageDTOS)) {
            List<List<MaterialPurchaseStorageDTO>> partition = com.google.common.collect.Lists.partition(insertMaterialPurchaseStorageDTOS, 3000);
            for (List<MaterialPurchaseStorageDTO> poDtos : partition) {
                materialPurchaseStorageService.doCreateBatch(poDtos);
            }
            // 维护码头库存的是否入库字段
            matchMaterialInventory(insertMaterialPurchaseStorageDTOS);

        }
        if (CollectionUtils.isNotEmpty(updateMaterialPurchaseStorageDTOS)) {
            List<List<MaterialPurchaseStorageDTO>> partition = com.google.common.collect.Lists.partition(updateMaterialPurchaseStorageDTOS, 3000);
            for (List<MaterialPurchaseStorageDTO> poDtos : partition) {
                materialPurchaseStorageService.doUpdateBatch(poDtos);
            }
        }
        if (CollectionUtils.isNotEmpty(updateDetailDTOS)) {
            List<List<InventoryFloatGlassShippedDetailDTO>> partition = com.google.common.collect.Lists.partition(updateDetailDTOS, 3000);
            for (List<InventoryFloatGlassShippedDetailDTO> shippedDetailDTOs : partition) {
                inventoryFloatGlassShippedDetailService.doUpdateBatch(shippedDetailDTOs);
            }
        }

        // 匹配原片调拨计划，状态为“已发布”，目的地为本厂，且原片编码与采购入库记录一致的，更新“是否入库”为“是”
        List<MaterialPlanTransferDTO> updateMaterialPlanTransferList = materialPurchaseStorages.stream()
                .map(MesMaterialPurchaseStorage::getItemCode)
                .map(materialPlanTransferProductCodeMap::get)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(BaseVO::getId)
                .distinct()
                .map(id -> {
                    MaterialPlanTransferDTO dto = new MaterialPlanTransferDTO();
                    dto.setId(id);
                    dto.setStorageFlag(YesOrNoEnum.YES.getCode());
                    return dto;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateMaterialPlanTransferList)){
            com.google.common.collect.Lists.partition(updateMaterialPlanTransferList, 500).forEach(materialPlanTransferService::doUpdateBatchSelective);
        }

        DynamicDataSourceContextHolder.clearDataSource();

        return BaseResponse.success("同步成功");
    }

    private void matchMaterialInventory(List<MaterialPurchaseStorageDTO> insertMaterialPurchaseStorageDTOS){
        log.info("码头库存匹配入库记录开始******");
        List<String> poCodeList = insertMaterialPurchaseStorageDTOS.stream()
                .map(MaterialPurchaseStorageDTO::getPurchaseOrderCode)
                .distinct().collect(Collectors.toList());
        // 查询原片码头库存
        Map<String, Object> params = new HashMap<>();
        params.put("poList", poCodeList);
        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(params);
        log.info("根据po查询码头库存数据:{}条", inventoryQuayDetailVOS.size());
        if (CollectionUtils.isEmpty(inventoryQuayDetailVOS)) {
            return;
        }
        List<InventoryQuayDetailDTO> updateInventoryQuayDetailDTOList = new ArrayList<>();

        Map<String, MaterialPurchaseStorageDTO> materialPurchaseStorageMap = insertMaterialPurchaseStorageDTOS.stream()
                .collect(Collectors.toMap(item -> String.join(item.getPurchaseOrderCode(), item.getPurchaseOrderLineCode()), Function.identity(), (k1, k2) -> k2));

        Map<String, List<InventoryQuayDetailVO>> inventoryQuayDetailMap = inventoryQuayDetailVOS.stream()
                .collect(Collectors.groupingBy(item -> String.join(item.getPo(), item.getPoNumber())));

        for (Map.Entry<String, List<InventoryQuayDetailVO>> entry : inventoryQuayDetailMap.entrySet()) {
            String key = entry.getKey();
            if (!materialPurchaseStorageMap.containsKey(key)){
                continue;
            }
            // 更新为已入库
            List<InventoryQuayDetailVO> value = entry.getValue();
            value.forEach(item -> item.setStorageFlag(YesOrNoEnum.YES.getCode()));
            List<InventoryQuayDetailDTO> inventoryQuayDetailDTOList = InventoryQuayDetailConvertor.INSTANCE.vo2Dtos(value);
            updateInventoryQuayDetailDTOList.addAll(inventoryQuayDetailDTOList);
        }

        if (CollectionUtils.isNotEmpty(updateInventoryQuayDetailDTOList)){
            com.google.common.collect.Lists.partition(updateInventoryQuayDetailDTOList, 500).forEach(inventoryQuayDetailService::doUpdateBatchSelective);
        }
        log.info("码头库存匹配入库记录结束******");
    }

    @Override
    public List<MaterialPurchaseStorageVO> selectByProductOrPurchaseOrderOrWarehouseEntry(Map<String, Object> params) {
        return MaterialPurchaseStorageConvertor.INSTANCE.po2Vos(materialPurchaseStorageDao.selectByProductOrPurchaseOrderOrWarehouseEntry(params));
    }

    @Override
    public BaseResponse<MaterialPurchaseStorageVO> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步采购入库记录接口");
            if (StringUtils.isEmpty(scenario)) {
                scenario = SystemHolder.getScenario();
                tenantCode = SystemHolder.getTenantId();
            }
            HashMap<String, Object> map = MapUtil.newHashMap();

            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.MATERIAL_PURCHASE_STORAGE.getCode(), map);

        } catch (Exception e) {
            log.error("同步采购入库记录数据报错,{}", e.getMessage());
            throw new BusinessException("同步采购入库记录数据报错", e.getMessage());
        }
        return BaseResponse.success("同步操作完成");
    }


}
