package com.yhl.scp.mrp.materialDemand.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialGrossDemandHistoryDO</code>
 * <p>
 * 材料计划毛需求（历史）DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11 09:51:53
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialGrossDemandHistoryDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 155378466219431308L;

    /**
     * 主键
     */
    private String id;
    /**
     * 毛需求计算版本ID
     */
    private String materialGrossDemandVersionId;
    /**
     * 主料编码
     */
    private String mainProductCode;
    /**
     * 物品编码
     */
    private String productCode;
    /**
     * 物品ID
     */
    private String productId;
    /**
     * 物品名称
     */
    private String productName;
    /**
     * 物料分类
     */
    private String productClassify;
    /**
     * 物料类别(PVB、B、RA.A)
     */
    private String productCategory;
    /**
     * 本厂编码
     */
    private String productFactoryCode;
    /**
     * 车型编码
     */
    private String vehicleModeCode;
    /**
     * 单耗
     */
    private BigDecimal inputFactor;
    /**
     * 需求时间
     */
    private Date demandTime;
    /**
     * 需求数量
     */
    private BigDecimal demandQuantity;
    /**
     * 未分配数量
     */
    private BigDecimal unFulfillmentQuantity;
    /**
     * 工序代码
     */
    private String operationCode;
    /**
     * 供应方式
     */
    private String supplyModel;
    /**
     * 颜色
     */
    private String productColor;
    /**
     * 厚度
     */
    private BigDecimal productThickness;
    /**
     * 需求来源
     */
    private String demandSource;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

}
