package com.yhl.scp.mrp.material.purchase.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewVersionDO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewVersionDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewVersionPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseReviewVersionDomainService</code>
 * <p>
 * 材料采购评审版本领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
@Service
public class MaterialPurchaseReviewVersionDomainService {

    @Resource
    private MaterialPurchaseReviewVersionDao materialPurchaseReviewVersionDao;

    /**
     * 数据校验
     *
     * @param materialPurchaseReviewVersionDO 领域对象
     */
    public void validation(MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO) {
        checkNotNull(materialPurchaseReviewVersionDO);
        checkUniqueCode(materialPurchaseReviewVersionDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialPurchaseReviewVersionDO 领域对象
     */
    private void checkNotNull(MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO) {}

    /**
     * 唯一性校验
     *
     * @param materialPurchaseReviewVersionDO 领域对象
     */
    private void checkUniqueCode(MaterialPurchaseReviewVersionDO materialPurchaseReviewVersionDO) {}

}
