package com.yhl.scp.mrp.material.purchase.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDataPO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;

/**
 * <code>MaterialPurchaseReviewDataDao</code>
 * <p>
 * 材料采购评审DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:25
 */
public interface MaterialPurchaseReviewDataDao extends BaseDao<MaterialPurchaseReviewDataPO, MaterialPurchaseReviewDataVO> {
    void deleteAll();

}
