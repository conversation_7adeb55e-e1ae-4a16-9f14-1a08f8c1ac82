package com.yhl.scp.mrp.material.arrival.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.MaterialPurchaseReviewFixedEnum;
import com.yhl.scp.mrp.enums.MaterialPurchaseReviewTypeEnum;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseReviewService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialInventoryAndDemandVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelForecastVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelHistoryVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialStatisticsVO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDataService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDetailService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yhl.scp.mrp.material.purchase.service.impl.MaterialPurchaseReviewDataServiceImpl.getMonthRange;


/**
 * <code>MaterialPurchaseReviewServiceImpl</code>
 * <p>
 * 材料采购评审应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 17:17:14
 */
@Slf4j
@Service
public class MaterialPurchaseReviewServiceImpl implements MaterialPurchaseReviewService {

    @Resource
    private MaterialPurchaseReviewDataService materialPurchaseReviewDataService;

    @Resource
    private MaterialPurchaseReviewDetailService materialPurchaseReviewDetailService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public List<MaterialInventoryAndDemandVO> selectTableInventoryAndDemand(MaterialPurchaseReviewDTO dto) {
        List<MaterialInventoryAndDemandVO> result = new ArrayList<>();

        List<MaterialPurchaseReviewDataVO> reviewDataList;
        if (StringUtils.isEmpty(dto.getMaterialPurchaseReviewVersionId())) {
            // 查询基础审核数据
            reviewDataList = materialPurchaseReviewDataService.selectByParams(
                    ImmutableMap.of(
                            "reviewType", MaterialPurchaseReviewTypeEnum.INVENTORY_AND_DEMAND.getCode(),
                            "productCodes", dto.getProductCodeList()
                    )
            );
        } else {
            // 根据版本查询基础审核明细数据
            reviewDataList = materialPurchaseReviewDetailService.selectByParams(
                            ImmutableMap.of("materialPurchaseReviewVersionId", dto.getMaterialPurchaseReviewVersionId(),
                                    "reviewType", MaterialPurchaseReviewTypeEnum.INVENTORY_AND_DEMAND.getCode(),
                                    "productCodes", dto.getProductCodeList())).stream()
                    .map(data -> {
                        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = new MaterialPurchaseReviewDataVO();
                        BeanUtils.copyProperties(data, materialPurchaseReviewDataVO);
                        return materialPurchaseReviewDataVO;
                    }).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(reviewDataList)) {
            return result;
        }

        // 过滤供应商（多个供应商是逗号分割的）
        if (StringUtils.isNotEmpty(dto.getSupplierCode())) {
            reviewDataList = reviewDataList.stream()
                    .filter(data -> data.getSupplierCode().contains(dto.getSupplierCode())).collect(Collectors.toList());
        }

        // 获取月份范围列表
        List<String> monthRangeList = getMonthRange(
                dto.getStartYearMonthDimension(),
                dto.getEndYearMonthDimension(),
                dto.getAppointYearMonth(),
                false
        );

        // 按物料编码分组处理
        Map<String, List<MaterialPurchaseReviewDataVO>> productDataMap = reviewDataList.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductCode));

        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> productEntry : productDataMap.entrySet()) {
            result.add(buildMaterialInventoryVO(productEntry.getValue(), monthRangeList));
        }

        return result;
    }

    @Override
    public List<MaterialModelHistoryVO> selectTableModelHistory(MaterialPurchaseReviewDTO dto) {
        List<MaterialModelHistoryVO> result = new ArrayList<>();

        List<MaterialPurchaseReviewDataVO> reviewDataList;
        if (StringUtils.isEmpty(dto.getMaterialPurchaseReviewVersionId())) {
            // 查询基础审核数据
            reviewDataList = materialPurchaseReviewDataService.selectByParams(
                    ImmutableMap.of(
                            "reviewType", MaterialPurchaseReviewTypeEnum.MODEL_HISTORY.getCode(),
                            "productCodes", dto.getProductCodeList()
                    )
            );
        } else {
            // 根据版本查询基础审核明细数据
            reviewDataList = materialPurchaseReviewDetailService.selectByParams(
                            ImmutableMap.of("materialPurchaseReviewVersionId", dto.getMaterialPurchaseReviewVersionId(),
                                    "reviewType", MaterialPurchaseReviewTypeEnum.MODEL_HISTORY.getCode(),
                                    "productCodes", dto.getProductCodeList())).stream()
                    .map(data -> {
                        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = new MaterialPurchaseReviewDataVO();
                        BeanUtils.copyProperties(data, materialPurchaseReviewDataVO);
                        return materialPurchaseReviewDataVO;
                    }).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(reviewDataList)) {
            return result;
        }

        // 过滤供应商（多个供应商是逗号分割的）
        if (StringUtils.isNotEmpty(dto.getSupplierCode())) {
            reviewDataList = reviewDataList.stream()
                    .filter(data -> data.getSupplierCode().contains(dto.getSupplierCode())).collect(Collectors.toList());
        }

        // 获取月份范围列表
        List<String> monthRangeList = getMonthRange(
                dto.getStartYearMonthDimension(),
                dto.getEndYearMonthDimension(),
                dto.getAppointYearMonth(),
                false
        );

        Map<String, List<MaterialPurchaseReviewDataVO>> productDataMap = reviewDataList.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductCode));

        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry : productDataMap.entrySet()) {
            // 按本厂编码分组处理
            Map<String, List<MaterialPurchaseReviewDataVO>> productFatoryDataMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductFactoryCode));
            for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> productEntry : productFatoryDataMap.entrySet()) {
                List<MaterialPurchaseReviewDataVO> value = productEntry.getValue();
                result.add(buildMaterialInventoryVO02(value, monthRangeList));
            }
        }
        result.sort(Comparator.comparing(MaterialModelHistoryVO::getProductCode, Comparator.nullsFirst(String::compareTo))
                .thenComparing(MaterialModelHistoryVO::getVehicleModel, Comparator.nullsLast(String::compareTo).reversed()));
        return result;
    }

    @Override
    public List<MaterialModelForecastVO> selectTableModelForecast(MaterialPurchaseReviewDTO dto) {
        List<MaterialModelForecastVO> result = new ArrayList<>();

        List<MaterialPurchaseReviewDataVO> reviewDataList;
        if (StringUtils.isEmpty(dto.getMaterialPurchaseReviewVersionId())) {
            // 查询基础审核数据
            reviewDataList = materialPurchaseReviewDataService.selectByParams(
                    ImmutableMap.of(
                            "reviewType", MaterialPurchaseReviewTypeEnum.MODEL_FORECAST.getCode(),
                            "productCodes", dto.getProductCodeList()
                    )
            );
        } else {
            // 根据版本查询基础审核明细数据
            reviewDataList = materialPurchaseReviewDetailService.selectByParams(
                            ImmutableMap.of("materialPurchaseReviewVersionId", dto.getMaterialPurchaseReviewVersionId(),
                                    "reviewType", MaterialPurchaseReviewTypeEnum.MODEL_FORECAST.getCode(),
                                    "productCodes", dto.getProductCodeList())).stream()
                    .map(data -> {
                        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = new MaterialPurchaseReviewDataVO();
                        BeanUtils.copyProperties(data, materialPurchaseReviewDataVO);
                        return materialPurchaseReviewDataVO;
                    }).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(reviewDataList)) {
            return result;
        }

        // 过滤供应商（多个供应商是逗号分割的）
        if (StringUtils.isNotEmpty(dto.getSupplierCode())) {
            reviewDataList = reviewDataList.stream()
                    .filter(data -> data.getSupplierCode().contains(dto.getSupplierCode())).collect(Collectors.toList());
        }

        // 获取月份范围列表
        List<String> monthRangeList = getMonthRange(
                dto.getStartYearMonthDimension(),
                dto.getEndYearMonthDimension(),
                dto.getAppointYearMonth(),
                false
        );

        // 按本厂编码分组处理
        Map<String, List<MaterialPurchaseReviewDataVO>> productDataMap = reviewDataList.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductFactoryCode));

        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry : productDataMap.entrySet()) {
            // 按本厂编码分组处理
            Map<String, List<MaterialPurchaseReviewDataVO>> productFatoryDataMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductFactoryCode));
            for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> productEntry : productFatoryDataMap.entrySet()) {
                List<MaterialPurchaseReviewDataVO> value = productEntry.getValue();
                result.add(buildMaterialInventoryVO03(value, monthRangeList));
            }
        }
        return result;
    }

    /**
     * 构建单个产品的库存需求VO
     */
    private MaterialInventoryAndDemandVO buildMaterialInventoryVO(
            List<MaterialPurchaseReviewDataVO> productDataList,
            List<String> monthRangeList) {

        // 基础信息从第一个数据对象提取（假设同产品数据基础信息一致）
        MaterialPurchaseReviewDataVO firstData = productDataList.get(0);
        MaterialInventoryAndDemandVO resultVO = new MaterialInventoryAndDemandVO();

        // 设置基础属性
        resultVO.setYearMonthArrange(monthRangeList);
        resultVO.setProductCode(firstData.getProductCode());
        resultVO.setProductName(firstData.getProductName());
        resultVO.setVehicleModel(firstData.getVehicleModelCode());
        resultVO.setOemCode(firstData.getOemCode());
        resultVO.setOemName(firstData.getOemName());
        resultVO.setSupplierCode(firstData.getSupplierCode());
        resultVO.setPurchaseLot(firstData.getPurchaseLot());
        resultVO.setMinOrderQuantity(firstData.getMinOrderQuantity());
        resultVO.setReviewComments(firstData.getReviewComments());
        resultVO.setApprove(firstData.getApprove());
        resultVO.setToExamine(firstData.getToExamine());
        resultVO.setTabulation(firstData.getTabulation());

        // 构建统计列表
        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 处理按行描述分组的数据
        Map<String, List<MaterialPurchaseReviewDataVO>> lineDescDataMap = productDataList.stream()
                .collect(Collectors.groupingBy(
                        MaterialPurchaseReviewDataVO::getLineDescription,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> lineEntry : lineDescDataMap.entrySet()) {
            statisticsList.add(buildLineStatisticsVO(
                    lineEntry.getKey(),
                    lineEntry.getValue(),
                    monthRangeList
            ));
        }

        // 处理固定枚举类型的统计数据
//        processFixedEnumStatistics(statisticsList, productDataList, monthRangeList);
        sortStatisticsList(statisticsList);
        resultVO.setStatisticsList(statisticsList);
        return resultVO;
    }

    /**
     * 构建单个产品的库存需求VO
     */
    private MaterialModelHistoryVO buildMaterialInventoryVO02(
            List<MaterialPurchaseReviewDataVO> productDataList,
            List<String> monthRangeList) {

        // 基础信息从第一个数据对象提取（假设同产品数据基础信息一致）
        MaterialPurchaseReviewDataVO firstData = productDataList.get(0);
        MaterialModelHistoryVO resultVO = new MaterialModelHistoryVO();

        // 设置基础属性
        resultVO.setYearMonthArrange(monthRangeList);
        resultVO.setProductCode(firstData.getProductCode());
        resultVO.setProductName(firstData.getProductName());
        resultVO.setProductFactoryCode(firstData.getProductFactoryCode());
        resultVO.setVehicleModel(firstData.getVehicleModelCode());
        resultVO.setVehicleModelEOPDate(firstData.getVehicleModelEopDate());

        // 构建统计列表
        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 处理按行描述分组的数据
        Map<String, List<MaterialPurchaseReviewDataVO>> lineDescDataMap = productDataList.stream()
                .collect(Collectors.groupingBy(
                        MaterialPurchaseReviewDataVO::getLineDescription,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> lineEntry : lineDescDataMap.entrySet()) {
            statisticsList.add(buildLineStatisticsVO(
                    lineEntry.getKey(),
                    lineEntry.getValue(),
                    monthRangeList
            ));
        }
        // 处理固定枚举类型的统计数据
//        processFixedEnumStatistics(statisticsList, productDataList, monthRangeList);
        sortStatisticsList(statisticsList);
        resultVO.setStatisticsList(statisticsList);
        return resultVO;
    }

    /**
     * 构建单个产品的库存需求VO
     */
    private MaterialModelForecastVO buildMaterialInventoryVO03(
            List<MaterialPurchaseReviewDataVO> productDataList,
            List<String> monthRangeList) {

        // 基础信息从第一个数据对象提取
        MaterialPurchaseReviewDataVO firstData = productDataList.get(0);
        MaterialModelForecastVO resultVO = new MaterialModelForecastVO();

        // 设置基础属性
        resultVO.setYearMonthArrange(monthRangeList);
        resultVO.setProductCode(firstData.getProductCode());
        resultVO.setProductName(firstData.getProductName());
        resultVO.setProductFactoryCode(firstData.getProductFactoryCode());
        if (null != firstData.getSinglePieceArea()) {
            resultVO.setSingleAcreage(new BigDecimal(firstData.getSinglePieceArea()));
        }

        // 构建统计列表
        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 处理按行描述分组的数据
        Map<String, List<MaterialPurchaseReviewDataVO>> lineDescDataMap = productDataList.stream()
                .collect(Collectors.groupingBy(
                        MaterialPurchaseReviewDataVO::getLineDescription,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> lineEntry : lineDescDataMap.entrySet()) {
            statisticsList.add(buildLineStatisticsVO(
                    lineEntry.getKey(),
                    lineEntry.getValue(),
                    monthRangeList
            ));
        }

        sortStatisticsList(statisticsList);
        resultVO.setStatisticsList(statisticsList);
        return resultVO;
    }


    /**
     * 构建行描述对应的统计VO
     */
    private MaterialStatisticsVO buildLineStatisticsVO(
            String lineDescription,
            List<MaterialPurchaseReviewDataVO> dataList,
            List<String> monthRangeList) {

        MaterialStatisticsVO statisticsVO = new MaterialStatisticsVO();
        statisticsVO.setLineDescription(lineDescription);

        // 构建月份数据映射（取第一条数据的月份值，如需聚合可调整逻辑）
        MaterialPurchaseReviewDataVO firstData = dataList.get(0);
        statisticsVO.setId(firstData.getId());
        Map<String, Object> monthlyMap = buildMonthlyMap(monthRangeList, firstData);
        statisticsVO.setMonthlyMap(monthlyMap);
        statisticsVO.setMaterialPurchaseReviewVersionId(firstData.getMaterialPurchaseReviewVersionId());
        statisticsVO.setForecastProvider(firstData.getForecastProvider());
        // 计算总和
        statisticsVO.setAmount(calculateSum(monthlyMap));
        return statisticsVO;
    }

    /**
     * 处理固定枚举类型的统计数据
     */
    private void processFixedEnumStatistics(
            List<MaterialStatisticsVO> statisticsList,
            List<MaterialPurchaseReviewDataVO> productDataList,
            List<String> monthRangeList) {

        for (MaterialPurchaseReviewFixedEnum fixedEnum : MaterialPurchaseReviewFixedEnum.values()) {
            // 筛选当前枚举对应的行数据
            List<MaterialPurchaseReviewDataVO> enumDataList = productDataList.stream()
                    .filter(data -> fixedEnum.getCode().equals(data.getLineDescription()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(enumDataList)) {
                continue;
            }

            addStatisticsVO(statisticsList, fixedEnum, enumDataList, monthRangeList);
        }
    }

    /**
     * 添加枚举对应的统计VO
     */
    private void addStatisticsVO(
            List<MaterialStatisticsVO> statisticsList,
            MaterialPurchaseReviewFixedEnum fixedEnum,
            List<MaterialPurchaseReviewDataVO> dataList,
            List<String> monthRangeList) {

        MaterialStatisticsVO statisticsVO = new MaterialStatisticsVO();
        statisticsVO.setLineDescription(fixedEnum.getDesc());

        // 根据枚举类型获取对应的月份值数组
        MaterialPurchaseReviewDataVO firstData = dataList.get(0);
        Object[] monthValues = getMonthValuesByEnum(firstData);

        // 构建月份数据映射
        Map<String, Object> monthlyMap = buildMonthlyMap(monthRangeList, monthValues);
        statisticsVO.setMonthlyMap(monthlyMap);

        // 计算总和（非数值类型不需要求和）
        statisticsVO.setAmount(calculateSum(monthlyMap));

        statisticsList.add(statisticsVO);
    }

    /**
     * 构建月份数据映射
     */
    private Map<String, Object> buildMonthlyMap(List<String> monthRangeList, MaterialPurchaseReviewDataVO data) {
        Object[] monthValues = {
                data.getOneYearMonthValue(),
                data.getTwoYearMonthValue(),
                data.getThreeYearMonthValue(),
                data.getFourYearMonthValue(),
                data.getFiveYearMonthValue(),
                data.getSixYearMonthValue(),
                data.getSevenYearMonthValue(),
                data.getEightYearMonthValue(),
                data.getNineYearMonthValue(),
                data.getTenYearMonthValue(),
                data.getElevenYearMonthValue(),
                data.getTwelveYearMonthValue()
        };
        return buildMonthlyMap(monthRangeList, monthValues);
    }

    /**
     * 构建月份数据映射（重载）
     */
    private Map<String, Object> buildMonthlyMap(List<String> monthRangeList, Object[] monthValues) {
        Map<String, Object> monthlyMap = new HashMap<>(16);
        for (int i = 0; i < monthRangeList.size(); i++) {
            String monthRange = monthRangeList.get(i);
            Object value = (i < monthValues.length) ? monthValues[i] : null;
            monthlyMap.put(monthRange, value);
        }
        return monthlyMap;
    }

    public Object[] getYearMonthValue(MaterialPurchaseReviewDataVO data) {
        return new Object[]{
                data.getOneYearMonthValue(),
                data.getTwoYearMonthValue(),
                data.getThreeYearMonthValue(),
                data.getFourYearMonthValue(),
                data.getFiveYearMonthValue(),
                data.getSixYearMonthValue(),
                data.getSevenYearMonthValue(),
                data.getEightYearMonthValue(),
                data.getNineYearMonthValue(),
                data.getTenYearMonthValue(),
                data.getElevenYearMonthValue(),
                data.getTwelveYearMonthValue()
        };
    }

    /**
     * 根据枚举类型获取对应的月份值数组（所有枚举都返回年份月份值数组）
     */
    private Object[] getMonthValuesByEnum(MaterialPurchaseReviewDataVO data) {
        // 所有枚举类型都返回getYearMonthValue(data)，不再区分case
        return getYearMonthValue(data);
    }

    /**
     * 计算月份数据总和（仅保留可成功转换为数值的值，转换失败的直接过滤）
     *
     * @param monthlyMap 存储月份与对应数值的映射
     * @return 总和（BigDecimal类型，保留原始精度）
     */
    private BigDecimal calculateSum(Map<String, Object> monthlyMap) {
        // 避免空指针：如果map为空直接返回0
        if (monthlyMap == null || monthlyMap.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal total = BigDecimal.ZERO;

        for (Object value : monthlyMap.values()) {
            if (value == null) {
                continue;
            }

            BigDecimal num = null;
            try {
                // 尝试转换为BigDecimal
                if (value instanceof Number) {
                    // 处理Number类型（Integer、Double、Long等）
                    num = BigDecimal.valueOf(((Number) value).doubleValue());
                } else if (value instanceof String) {
                    // 处理字符串类型（仅保留有效数字格式）
                    String str = ((String) value).trim();
                    if (!str.isEmpty()) {
                        num = new BigDecimal(str);
                    }
                } else {
                    // 其他类型尝试通过toString()转换
                    String str = value.toString().trim();
                    if (!str.isEmpty()) {
                        num = new BigDecimal(str);
                    }
                }
            } catch (NumberFormatException e) {
                // 转换失败（如非数字字符串、格式错误等），直接过滤
                continue;
            }

            // 仅累加转换成功的数值
            if (num != null) {
                total = total.add(num);
            }
        }

        return total;
    }

    /**
     * 对统计列表按LineDescription排序：含日期的按日期升序，无日期的放最后
     *
     * @param statisticsList 需要排序的列表
     */
    private void sortStatisticsList(List<MaterialStatisticsVO> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return;
        }

        // 自定义排序规则
        statisticsList.sort((vo1, vo2) -> {
            String line1 = vo1.getLineDescription();
            String line2 = vo2.getLineDescription();

            // 解析日期
            YearMonth date1 = parseDateFromLineDescription(line1);
            YearMonth date2 = parseDateFromLineDescription(line2);

            // 排序逻辑：有日期的在前，按日期升序；无日期的在后，按自然顺序
            if (date1 != null && date2 != null) {
                return date1.compareTo(date2);
            } else if (date1 != null) {
                return -1;
            } else if (date2 != null) {
                return 1;
            } else {
                // 都无日期：按字符串自然排序
                return line1.compareTo(line2);
            }
        });
    }

    /**
     * 从LineDescription中提取日期（格式：YY.M月）
     */
    private YearMonth parseDateFromLineDescription(String lineDescription) {
        if (StringUtils.isEmpty(lineDescription)) {
            return null;
        }
        // 匹配"数字.数字月"格式（如"26.1月"）
        Pattern pattern = Pattern.compile("(\\d+)\\.(\\d+)月");
        Matcher matcher = pattern.matcher(lineDescription);

        if (matcher.find()) {
            try {
                // 转换为4位年份（26→2026）
                int year = 2000 + Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));
                if (month >= 1 && month <= 12) {
                    return YearMonth.of(year, month);
                }
            } catch (NumberFormatException e) {
                // 数字解析失败，视为无日期
                return null;
            }
        }
        return null;
    }
}
