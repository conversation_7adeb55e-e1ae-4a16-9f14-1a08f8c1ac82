package com.yhl.scp.mrp.material.arrival.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseReviewService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialInventoryAndDemandVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelForecastVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialModelHistoryVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseReviewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialProcurementReviewController</code>
 * <p>
 * 材料采购评审控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:10:22
 */
@Slf4j
@Api(tags = "材料采购评审控制器")
@RestController
@RequestMapping("materialPurchaseReview")
public class MaterialPurchaseReviewController extends BaseController {

    @Resource
    private MaterialPurchaseReviewService materialPurchaseReviewService;

    @ApiOperation(value = "查询报表（库存与需求）")
    @PostMapping(value = "selectTableInventoryAndDemand")
    public BaseResponse<List<MaterialInventoryAndDemandVO>> selectTableInventoryAndDemand(@RequestBody MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewService.selectTableInventoryAndDemand(materialPurchaseReviewTableDTO));
    }

    @ApiOperation(value = "查询报表（车型历史）")
    @PostMapping(value = "selectTableModelHistory")
    public BaseResponse<List<MaterialModelHistoryVO>> selectTableModelHistory(@RequestBody MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewService.selectTableModelHistory(materialPurchaseReviewTableDTO));
    }

    @ApiOperation(value = "查询报表（车型预测）")
    @PostMapping(value = "selectTableModelForecast")
    public BaseResponse<List<MaterialModelForecastVO>> selectTableModelForecast(@RequestBody MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewService.selectTableModelForecast(materialPurchaseReviewTableDTO));
    }
}
