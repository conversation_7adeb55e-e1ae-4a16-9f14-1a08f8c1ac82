package com.yhl.scp.mrp.material.arrival.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialDeliveryNoteDO</code>
 * <p>
 * 材料送货单数据DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:09:03
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class
MaterialDeliveryNoteDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 728949999518350540L;

    /**
     * 主键
     */
    private String id;
        
    /**
     * 送货单号
     */
    private String deliveryNoteCode;
        
    /**
     * 库存点代码
     */
    private String stockPointCode;
        
    /**
     * 库存点名称
     */
    private String stockPointName;
        
    /**
     * 本厂编号
     */
    private String productCode;
        
    /**
     * 物料名称
     */
    private String productName;
        
    /**
     * 采购单号
     */
    private String purchaseOrderCode;
        
    /**
     * 采购单行号
     */
    private String purchaseOrderLineCode;
        
    /**
     * 预计到货日期
     */
    private Date predictArrivalDate;
        
    /**
     * 预计到货数量
     */
    private BigDecimal predictArrivalQuantity;
        
    /**
     * 单位
     */
    private String productUnit;
    /**
     * 送货单创建时间
     */
    private Date shippingDate;

    /**
     * 状态
     */
    private String ticketStatus;
        
    /**
     * 版本
     */
    private Integer versionValue;

    /**
     * 是否匹配
     */
    private String whetherMatches;

    /**
     * 送货单Id
     */
    private String groupId;

    /**
     * 送货明细号
     */
    private String ticketNum;
}
