package com.yhl.scp.mrp.material.purchase.domain.factory;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewVersionDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewVersionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialPurchaseReviewVersionFactory</code>
 * <p>
 * 材料采购评审版本领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:09:59
 */
@Component
public class MaterialPurchaseReviewVersionFactory {

    @Resource
    private MaterialPurchaseReviewVersionDao materialPurchaseReviewVersionDao;

    MaterialPurchaseReviewVersionDO create(MaterialPurchaseReviewVersionDTO dto) {
        // TODO
        return null;
    }

}
