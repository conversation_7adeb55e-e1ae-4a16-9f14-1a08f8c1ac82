package com.yhl.scp.mrp.material.arrival.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialDeliveryNotePO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialDeliveryNoteDao</code>
 * <p>
 * 材料送货单数据DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:09:03
 */
public interface MaterialDeliveryNoteDao extends BaseDao<MaterialDeliveryNotePO, MaterialDeliveryNoteVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MaterialDeliveryNoteVO}
     */
    List<MaterialDeliveryNoteVO> selectVOByParams(@Param("params") Map<String, Object> params);

    List<MaterialDeliveryNotePO> selectMaterialDeliveryNoteIds(@Param("list") List<MesMaterialDeliveryNote> list);

}
