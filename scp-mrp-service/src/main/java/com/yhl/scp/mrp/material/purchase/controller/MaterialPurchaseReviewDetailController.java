package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDetailService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseReviewDetailController</code>
 * <p>
 * 材料采购评审明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:05:54
 */
@Slf4j
@Api(tags = "材料采购评审明细控制器")
@RestController
@RequestMapping("materialPurchaseReviewDetail")
public class MaterialPurchaseReviewDetailController extends BaseController {

    @Resource
    private MaterialPurchaseReviewDetailService materialPurchaseReviewDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialPurchaseReviewDetailVO>> page() {
        List<MaterialPurchaseReviewDetailVO> materialPurchaseReviewDetailList = materialPurchaseReviewDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseReviewDetailVO> pageInfo = new PageInfo<>(materialPurchaseReviewDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO) {
        return materialPurchaseReviewDetailService.doCreate(materialPurchaseReviewDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO) {
        return materialPurchaseReviewDetailService.doUpdate(materialPurchaseReviewDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseReviewDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialPurchaseReviewDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseReviewDetailService.selectByPrimaryKey(id));
    }

}
