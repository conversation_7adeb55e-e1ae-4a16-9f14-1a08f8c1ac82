package com.yhl.scp.mrp.material.purchase.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDetailDO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDetailDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDetailPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseReviewDetailDomainService</code>
 * <p>
 * 材料采购评审明细领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22 10:06:06
 */
@Service
public class MaterialPurchaseReviewDetailDomainService {

    @Resource
    private MaterialPurchaseReviewDetailDao materialPurchaseReviewDetailDao;

    /**
     * 数据校验
     *
     * @param materialPurchaseReviewDetailDO 领域对象
     */
    public void validation(MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO) {
        checkNotNull(materialPurchaseReviewDetailDO);
        checkUniqueCode(materialPurchaseReviewDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialPurchaseReviewDetailDO 领域对象
     */
    private void checkNotNull(MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO) {}

    /**
     * 唯一性校验
     *
     * @param materialPurchaseReviewDetailDO 领域对象
     */
    private void checkUniqueCode(MaterialPurchaseReviewDetailDO materialPurchaseReviewDetailDO) {}

}
