package com.yhl.scp.mrp.material.plan.domain.factory;

import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanVersionDO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanVersionDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanVersionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialPlanVersionFactory</code>
 * <p>
 * 物料计划版本领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:14:51
 */
@Component
public class MaterialPlanVersionFactory {

    @Resource
    private MaterialPlanVersionDao materialPlanVersionDao;

    MaterialPlanVersionDO create(MaterialPlanVersionDTO dto) {
        // TODO
        return null;
    }

}
