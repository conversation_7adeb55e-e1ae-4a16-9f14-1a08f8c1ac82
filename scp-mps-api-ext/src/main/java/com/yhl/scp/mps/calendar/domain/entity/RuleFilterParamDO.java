package com.yhl.scp.mps.calendar.domain.entity;

import com.yhl.scp.mds.basic.calendar.domain.entity.RuleFilterParamBasicDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * <code>RuleFilterParam</code>
 * <p>
 * 日历规则过滤参数值对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-09 15:04:10
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
public class RuleFilterParamDO extends RuleFilterParamBasicDO {
}
