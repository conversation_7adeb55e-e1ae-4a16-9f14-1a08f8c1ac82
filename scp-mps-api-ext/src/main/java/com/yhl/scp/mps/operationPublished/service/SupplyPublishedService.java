package com.yhl.scp.mps.operationPublished.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.operationPublished.dto.SupplyPublishedDTO;
import com.yhl.scp.mps.operationPublished.vo.CopyPublishedStatusVO;
import com.yhl.scp.mps.operationPublished.vo.SupplyPublishedVO;

/**
 * <code>SupplyPublishedService</code>
 * <p>
 * 供应发布信息表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:03:54
 */
public interface SupplyPublishedService extends BaseService<SupplyPublishedDTO, SupplyPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link SupplyPublishedVO}
     */
    List<SupplyPublishedVO> selectAll();

    Boolean disposeSnapshotData(String oldPublishedLogId, String bindPublishedLogId, String publishedTime, Boolean copyFlag);

	CopyPublishedStatusVO doSnapshotDataForLineGroup(List<String> workOrderIds, List<String> publishedLogIds,
			Map<String, String> productLineGroupMap);

}
