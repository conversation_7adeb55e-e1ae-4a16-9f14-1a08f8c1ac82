package com.yhl.scp.mps.capacityBalance.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>CapacityBalanceType</code>
 * <p>
 * 产能平衡类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 10:14:47
 */
public enum CapacityBalanceTypeEnum implements CommonEnum {
    WEEK("WEEK", "周产能平衡"),
    DAY("DAY", "日产能平衡"),
    MONTH("MONTH", "周产能平衡"),

    NEW("NEW", "最新版本"),


    ;

    CapacityBalanceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
