package com.yhl.scp.mps.demand.vo;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>OperationEarlyWarningVO</code>
 * <p>
 * 工序异常预计信息VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-06 17:39:26
 */
@ApiModel(value = "工序异常预计信息VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationEarlyWarningVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 487002652537997259L;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 物品id
     */
    private String productId;
    
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
        
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
        
        
    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    @FieldInterpretation(value = "预警类型")
    private String earlyWarningType;
    
    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    @FieldInterpretation(value = "预警类型")
    private String earlyWarningTypeStr;
        
    /**
     * 预警原因
     */
    @ApiModelProperty(value = "预警原因")
    @FieldInterpretation(value = "预警原因")
    private String earlyWarningReason;
    
    /**
     * 制造订单交期（工序交期）
     */
    private Date dueDate;

    /**
     * 制造订单id
     */
    private String workOrderId;

    /**
     * 工序id
     */
    private String operationId;

    /**
     * 工序名称
     */
    private String operationName;
    
    /**
     * 工序名称
     */
    private String operationCode;
    
    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源代码
     */
    private String resourceCode;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 工单号
     */
    private String workOrderNumber;

    /**
     * 排产数量
     */
    private String plannedQuantity;

    /**
     * 排产日期
     */
    private String planDate;

    private Date planDateTime;

    /**
     * 排产结束日期
     */
    private String planEndDate;

    /**
     * 是否延期
     */
    private String delay;

    /**
     * 齐套状态
     */
    private String kitStatus;
    
    private String kitStatusCode;

    private String planStatus;
    
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @FieldInterpretation(value = "产品类型")
    private String productType;

    @Override
    public void clean() {

    }

}
