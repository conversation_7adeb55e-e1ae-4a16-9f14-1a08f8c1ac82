package com.yhl.scp.mps.plan.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>OperationDemandVO</code>
 * <p>
 * OperationDemandVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-06 23:52:49
 */
@Data
public class OperationDemandVO implements Serializable {

    private static final long serialVersionUID = -4716794747153345298L;

    private String parentOperationId;

    private String operationId;

    private String demandId;

    private BigDecimal quantity;

    private BigDecimal yield;

    private BigDecimal inputFactor;

}