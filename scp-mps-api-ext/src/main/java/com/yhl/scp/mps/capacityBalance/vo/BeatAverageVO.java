package com.yhl.scp.mps.capacityBalance.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>BeatAverageVO</code>
 * <p>
 * BeatAverageVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 19:31:50
 */
@Data
public class BeatAverageVO implements Serializable {

    private static final long serialVersionUID = -7807012483305738523L;

    private String resourceCode;

    private String yearMonth;

    private BigDecimal averageBeat;

}
