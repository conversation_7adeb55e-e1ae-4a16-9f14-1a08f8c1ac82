package com.yhl.scp.ips.rbac.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <code>UserCopy1DTO</code>
 * <p>
 * iam用户表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-12 08:49:48
 */
@ApiModel(value = "iam用户表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -25469146886158933L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @JSONField(name = "app_account__account_no")
    private String userName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @JSONField(name = "idt_user__user_uid")
    private String staffCode;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @JSONField(name = "app_account__account_name")
    private String cnName;
    /**
     * 英文名称
     */
    @ApiModelProperty(value = "英文名称")
    @JSONField(name = "idt_user__pinyin")
    private String enName;
    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private String userType;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String orgCode;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;
    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    @JSONField(name = "idt_user__mobile")
    private String phone;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @JSONField(name = "idt_user__email")
    private String email;
    /**
     * 灰度发布
     */
    @ApiModelProperty(value = "灰度发布")
    private String greyType;

    /**
     * 用户状态
     */
    @ApiModelProperty(value = "用户状态")
    @JSONField(name = "app_account__status")
    private String userStatus;

    /**
     * ERP账号
     */
    @ApiModelProperty(value = "ERP账号")
    private String erpUser;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatar;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

    @JSONField(name = "orgs")
    private Map<String, String> orgs;

    @JSONField(name = "request_log__id")
    private String requestLogId;

    @JSONField(name = "request_log__action_flag")
    private String requestLogActionFlag;

    @JSONField(name = "request_log__create_time")
    private Date requestLogCreateTime;

}
