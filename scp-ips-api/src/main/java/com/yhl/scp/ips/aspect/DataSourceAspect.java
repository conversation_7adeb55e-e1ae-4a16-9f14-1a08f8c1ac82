package com.yhl.scp.ips.aspect;

import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.ips.common.SystemHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <code>DataSourceAspect</code>
 * <p>
 * 动态数据源切换切面
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-09-07 14:09:09
 */
@Component
@Aspect
@Order(-1)
@Slf4j
public class DataSourceAspect {

    @Pointcut("execution(* com.yhl..controller..*Controller.*(..))")
    private void cutService() {
    }

    /**
     * 使用切面拦截用户请求，设置本次请求数据源
     *
     * @param joinPoint 切入点
     */
    @Before("cutService()")
    public void before(@SuppressWarnings("unused") JoinPoint joinPoint) {
        String scenario = SystemHolder.getScenario();
        if (StringUtils.isNotBlank(scenario)) {
            // 切换数据源
            DynamicDataSourceContextHolder.setDataSource(scenario);
        } else {
            // 清空线程数据源
            DynamicDataSourceContextHolder.clearDataSource();
        }
    }

}