package com.yhl.scp.ips.algorithm.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.algorithm.vo.AlgorithmStepLogVO;

import java.util.List;

/**
 * <code>AlgorithmStepLogService</code>
 * <p>
 * AlgorithmStepLogService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-10 10:03:26
 */
public interface AlgorithmStepLogService extends BaseService<AlgorithmStepLogDTO, AlgorithmStepLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link AlgorithmStepLogVO}
     */
    List<AlgorithmStepLogVO> selectAll();

}
