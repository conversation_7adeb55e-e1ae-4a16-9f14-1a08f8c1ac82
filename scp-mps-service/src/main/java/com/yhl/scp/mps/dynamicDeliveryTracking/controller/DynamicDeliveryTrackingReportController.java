package com.yhl.scp.mps.dynamicDeliveryTracking.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingReportDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingReportService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingReportVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVXToVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DynamicDeliveryTrackingReportController</code>
 * <p>
 * 动态交付跟踪工序任务明细反馈表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:48:52
 */
@Slf4j
@Api(tags = "动态交付跟踪工序任务明细反馈表控制器")
@RestController
@RequestMapping("dynamicDeliveryTrackingReport")
public class DynamicDeliveryTrackingReportController extends BaseController {

    @Resource
    private DynamicDeliveryTrackingReportService dynamicDeliveryTrackingReportService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DynamicDeliveryTrackingReportVO>> page() {
        List<DynamicDeliveryTrackingReportVO> dynamicDeliveryTrackingReportList = dynamicDeliveryTrackingReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DynamicDeliveryTrackingReportVO> pageInfo = new PageInfo<>(dynamicDeliveryTrackingReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DynamicDeliveryTrackingReportDTO dynamicDeliveryTrackingReportDTO) {
        return dynamicDeliveryTrackingReportService.doCreate(dynamicDeliveryTrackingReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DynamicDeliveryTrackingReportDTO dynamicDeliveryTrackingReportDTO) {
        return dynamicDeliveryTrackingReportService.doUpdate(dynamicDeliveryTrackingReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        dynamicDeliveryTrackingReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DynamicDeliveryTrackingReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dynamicDeliveryTrackingReportService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据任务工序id找到关联的数据")
    @GetMapping(value = "queryReportRecord")
    public BaseResponse<List<DynamicDeliveryTrackingReportVO>> queryReportRecord(@RequestParam(value = "subTaskId") String subTaskId) {
        return dynamicDeliveryTrackingReportService.queryReportRecord(subTaskId);
    }

}