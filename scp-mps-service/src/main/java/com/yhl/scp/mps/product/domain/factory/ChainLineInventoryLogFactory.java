package com.yhl.scp.mps.product.domain.factory;

import com.yhl.scp.mps.product.domain.entity.ChainLineInventoryLogDO;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.infrastructure.dao.ChainLineInventoryLogDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ChainLineInventoryLogFactory</code>
 * <p>
 * 链式生产线_中间表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-08 16:28:15
 */
@Component
public class ChainLineInventoryLogFactory {

    @Resource
    private ChainLineInventoryLogDao chainLineInventoryLogDao;

    ChainLineInventoryLogDO create(ChainLineInventoryLogDTO dto) {
        // TODO
        return null;
    }

}
