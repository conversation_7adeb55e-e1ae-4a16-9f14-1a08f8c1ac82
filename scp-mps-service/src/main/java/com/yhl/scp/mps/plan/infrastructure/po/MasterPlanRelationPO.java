package com.yhl.scp.mps.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MasterPlanRelationPO</code>
 * <p>
 * 计划单关联表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-04 15:33:42
 */
public class MasterPlanRelationPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -99940387194117440L;
    /**
     * 组织id
     */
    private String orgId;
    /**
     * 制造订单号
     */
    private String orderNo;
    /**
     * 计划单号
     */
    private String planNo;
    /**
     * 工序任务代码
     */
    private String operationStepCode;
    /**
     * 计划单状态
     */
    private String planStatus;
    /**
     * erp工单号
     */
    private String erpOrderNo;
    /**
     * erp计划单行号
     */
    private String lineNo;
    /**
     * 是否同步
     */
    private String logIsSync;
    /**
     * 订单数量
     */
    private BigDecimal orderQuantity;
    /**
     * 交付数量
     */
    private BigDecimal deliveryQuantity;
    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 物品代码
     */
    private String productCode;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlanNo() {
        return planNo;
    }

    public void setPlanNo(String planNo) {
        this.planNo = planNo;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public String getErpOrderNo() {
        return erpOrderNo;
    }

    public void setErpOrderNo(String erpOrderNo) {
        this.erpOrderNo = erpOrderNo;
    }

    public String getOperationStepCode() {
        return operationStepCode;
    }

    public void setOperationStepCode(String operationStepCode) {
        this.operationStepCode = operationStepCode;
    }

    public String getLineNo() {
        return lineNo;
    }

    public void setLineNo(String lineNo) {
        this.lineNo = lineNo;
    }

    public String getLogIsSync() {
        return logIsSync;
    }

    public void setLogIsSync(String logIsSync) {
        this.logIsSync = logIsSync;
    }

    public BigDecimal getDeliveryQuantity() {
        return deliveryQuantity;
    }

    public void setDeliveryQuantity(BigDecimal deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity;
    }

    public BigDecimal getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }
}
