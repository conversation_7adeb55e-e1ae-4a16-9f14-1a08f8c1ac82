package com.yhl.scp.mps.plan.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.plan.dto.MasterPlanIssuedDataDTO;
import com.yhl.scp.mps.plan.service.MasterPlanIssuedDataService;
import com.yhl.scp.mps.plan.vo.MasterPlanIssuedDataVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MasterPlanIssuedDataController</code>
 * <p>
 * 主计划发布数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 09:40:35
 */
@Slf4j
@Api(tags = "主计划发布数据控制器")
@RestController
@RequestMapping("masterPlanIssuedData")
public class MasterPlanIssuedDataController extends BaseController {

    @Resource
    private MasterPlanIssuedDataService masterPlanIssuedDataService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MasterPlanIssuedDataVO>> page() {
        List<MasterPlanIssuedDataVO> masterPlanIssuedDataList = masterPlanIssuedDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MasterPlanIssuedDataVO> pageInfo = new PageInfo<>(masterPlanIssuedDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MasterPlanIssuedDataDTO masterPlanIssuedDataDTO) {
        return masterPlanIssuedDataService.doCreate(masterPlanIssuedDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MasterPlanIssuedDataDTO masterPlanIssuedDataDTO) {
        return masterPlanIssuedDataService.doUpdate(masterPlanIssuedDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        masterPlanIssuedDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MasterPlanIssuedDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanIssuedDataService.selectByPrimaryKey(id));
    }

}
