package com.yhl.scp.mps.plan.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mps.plan.domain.entity.MasterPlanRelationDO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanRelationDao;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MasterPlanRelationDomainService</code>
 * <p>
 * 计划单关联表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-04 15:33:42
 */
@Service
public class MasterPlanRelationDomainService {

    @Resource
    private MasterPlanRelationDao masterPlanRelationDao;

    /**
     * 数据校验
     *
     * @param masterPlanRelationDO 领域对象
     */
    public void validation(MasterPlanRelationDO masterPlanRelationDO) {
        checkNotNull(masterPlanRelationDO);
        checkUniqueCode(masterPlanRelationDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param masterPlanRelationDO 领域对象
     */
    private void checkNotNull(MasterPlanRelationDO masterPlanRelationDO) {

    }

    /**
     * 唯一性校验
     *
     * @param masterPlanRelationDO 领域对象
     */
    private void checkUniqueCode(MasterPlanRelationDO masterPlanRelationDO) {

    }

}
