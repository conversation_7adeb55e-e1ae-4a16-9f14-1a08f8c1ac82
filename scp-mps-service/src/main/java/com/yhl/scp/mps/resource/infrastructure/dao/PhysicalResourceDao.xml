<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceDao">
    <resultMap id="BaseResultMap"
               extends="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.BaseResultMap"
               type="com.yhl.scp.mps.resource.infrastructure.po.PhysicalResourcePO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <resultMap id="VOResultMap"
               extends="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.VOResultMap"
               type="com.yhl.scp.mps.resource.vo.PhysicalResourceVO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <sql id="Base_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.Base_Column_List"/>
        <!--,new_column-->
    </sql>
    <sql id="VO_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.VO_Column_List"/>
        <!--,new_column-->
    </sql>
    <sql id="Base_Where_Condition">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.Base_Where_Condition"/>
        <!--项目自定义查询条件,参考示例-->
        <!--<if test="params.newColumn != null and params.newColumn != ''">
            and new_column = #{params.newColumn,jdbcType=VARCHAR}
        </if>-->
    </sql>
    <!-- ID查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_res_physical_resource
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_res_physical_resource
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mps_res_physical_resource
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_res_physical_resource
        <where>
            <include refid="Base_Where_Condition"/>
        </where>
    </select>
    <!-- 新增 -->
    <sql id="insertColumns">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.insertColumns"/>
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <!-- ,new_column -->
    </sql>
    <sql id="insertValues">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.insertValues"/>
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <!-- ,#{newColumn,jdbcType=VARCHAR} -->
    </sql>
    <insert id="insert" parameterType="com.yhl.scp.mps.resource.infrastructure.po.PhysicalResourcePO">
        insert into mps_res_physical_resource(<include refid="insertColumns"/>)
        values (<include refid="insertValues"/>)
    </insert>
    <!-- 批量新增 -->
    <sql id="insertBatchValues">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.insertBatchValues"/>
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <!-- ,#{entity.newColumn,jdbcType=VARCHAR} -->
    </sql>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_res_physical_resource(<include refid="insertColumns"/>)
        values
        <foreach collection="list" item="entity" separator=",">
            (<include refid="insertBatchValues"/>)
        </foreach>
    </insert>
    <!-- 修改 -->
    <sql id="updateColumns">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.updateColumns"/>
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <!--,new_column = #{newColumn,jdbcType=VARCHAR}-->
    </sql>
    <update id="update" parameterType="com.yhl.scp.mps.resource.infrastructure.po.PhysicalResourcePO">
        update mps_res_physical_resource set
        <include refid="updateColumns"/>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <sql id="updateSelectiveColumns">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.updateSelectiveColumns"/>
        <!--项目自定义字段,参考示例-->
        <!--<if test="newColumn != null and newColumn != ''">
            new_column = #{newColumn,jdbcType=VARCHAR}
        </if>-->
    </sql>
    <update id="updateSelective"
            parameterType="com.yhl.scp.mps.resource.infrastructure.po.PhysicalResourcePO">
        update mps_res_physical_resource
        <set>
            <include refid="updateSelectiveColumns"/>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <sql id="updateBatchColumns">
        <include refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.updateBatchColumns"/>
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <!--<trim prefix="new_column = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.newColumn,jdbcType=TIMESTAMP}
            </foreach>
        </trim>-->
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        update mps_res_physical_resource
        <trim prefix="set" suffixOverrides=",">
            <include refid="updateBatchColumns"/>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <sql id="updateBatchSelectiveColumns">
        <include
                refid="com.yhl.scp.mps.resource.infrastructure.dao.PhysicalResourceBasicDao.updateBatchSelectiveColumns"/>
        <!--项目自定义字段,参考示例-->
        <!--<if test="item.newColumn != null">
            new_column = #{item.newColumn,jdbcType=TIMESTAMP},
        </if>-->
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mps_res_physical_resource
            <set>
                <include refid="updateBatchSelectiveColumns"/>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
