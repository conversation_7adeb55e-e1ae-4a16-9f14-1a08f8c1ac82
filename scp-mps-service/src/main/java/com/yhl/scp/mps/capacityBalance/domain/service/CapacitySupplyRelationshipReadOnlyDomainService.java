package com.yhl.scp.mps.capacityBalance.domain.service;

import com.yhl.scp.mps.capacityBalance.domain.entity.CapacitySupplyRelationshipReadOnlyDO;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipReadOnlyDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>CapacitySupplyRelationshipReadOnlyDomainService</code>
 * <p>
 * 产能供应关系领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-16 11:03:51
 */
@Service
public class CapacitySupplyRelationshipReadOnlyDomainService {

    @Resource
    private CapacitySupplyRelationshipReadOnlyDao capacitySupplyRelationshipReadOnlyDao;

    /**
     * 数据校验
     *
     * @param capacitySupplyRelationshipReadOnlyDO 领域对象
     */
    public void validation(CapacitySupplyRelationshipReadOnlyDO capacitySupplyRelationshipReadOnlyDO) {
        checkNotNull(capacitySupplyRelationshipReadOnlyDO);
        checkUniqueCode(capacitySupplyRelationshipReadOnlyDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param capacitySupplyRelationshipReadOnlyDO 领域对象
     */
    private void checkNotNull(CapacitySupplyRelationshipReadOnlyDO capacitySupplyRelationshipReadOnlyDO) {

    }

    /**
     * 唯一性校验
     *
     * @param capacitySupplyRelationshipReadOnlyDO 领域对象
     */
    private void checkUniqueCode(CapacitySupplyRelationshipReadOnlyDO capacitySupplyRelationshipReadOnlyDO) {

    }

}
