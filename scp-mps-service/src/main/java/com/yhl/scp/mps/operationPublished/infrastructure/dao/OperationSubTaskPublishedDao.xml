<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.OperationSubTaskPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        <!--@Table sds_ord_operation_sub_task_published-->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.vo.OperationSubTaskPublishedVO">
        <!-- TODO -->
    </resultMap>
    
    <resultMap id="POResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        <!-- TODO -->
    </resultMap>
    
    <sql id="Base_Column_List">
        new_id,operation_id,physical_resource_id,task_id,task_type,start_time,end_time,remark,enabled,creator,create_time,modifier,modify_time,id,published_log_id
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.newId != null and params.newId != ''">
                and new_id = #{params.newId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationId != null and params.operationId != ''">
                and operation_id = #{params.operationId,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
                and physical_reid = #{params.physicalResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.taskId != null and params.taskId != ''">
                and task_id = #{params.taskId,jdbcType=VARCHAR}
            </if>
            <if test="params.taskType != null and params.taskType != ''">
                and task_type = #{params.taskType,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.publishedLogId != null and params.publishedLogId != ''">
                and published_log_id = #{params.publishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_sub_task_published
        where new_id = #{newId,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_sub_task_published
        where new_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sds_ord_operation_sub_task_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_sub_task_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_sds_ord_operation_sub_task_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_operation_sub_task_published(
        new_id,
        operation_id,
        physical_resource_id,
        task_id,
        task_type,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{taskId,jdbcType=VARCHAR},
        #{taskType,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        insert into sds_ord_operation_sub_task_published(
        new_id,
        operation_id,
        physical_resource_id,
        task_id,
        task_type,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{taskId,jdbcType=VARCHAR},
        #{taskType,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_operation_sub_task_published(
        new_id,
        operation_id,
        physical_resource_id,
        task_id,
        task_type,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.taskId,jdbcType=VARCHAR},
        #{entity.taskType,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_operation_sub_task_published(
        new_id,
        operation_id,
        physical_resource_id,
        task_id,
        task_type,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.newId,jdbcType=VARCHAR},
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.taskId,jdbcType=VARCHAR},
        #{entity.taskType,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        update sds_ord_operation_sub_task_published set
        operation_id = #{operationId,jdbcType=VARCHAR},
        physical_reid = #{physicalResourceId,jdbcType=VARCHAR},
        task_id = #{taskId,jdbcType=VARCHAR},
        task_type = #{taskType,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        id = #{id,jdbcType=VARCHAR},
        published_log_id = #{publishedLogId,jdbcType=VARCHAR}
        where new_id = #{newId,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationSubTaskPublishedPO">
        update sds_ord_operation_sub_task_published
        <set>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_reid = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.taskId != null and item.taskId != ''">
                task_id = #{item.taskId,jdbcType=VARCHAR},
            </if>
            <if test="item.taskType != null and item.taskType != ''">
                task_type = #{item.taskType,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_operation_sub_task_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="operation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_reid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.physicalResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.taskId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.taskType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.id,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_log_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.publishedLogId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where new_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sds_ord_operation_sub_task_published 
        <set>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_reid = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.taskId != null and item.taskId != ''">
                task_id = #{item.taskId,jdbcType=VARCHAR},
            </if>
            <if test="item.taskType != null and item.taskType != ''">
                task_type = #{item.taskType,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>  
        where new_id = #{item.newId,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sds_ord_operation_sub_task_published where new_id = #{newId,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_operation_sub_task_published where new_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <update id="truncateTable">
        TRUNCATE TABLE sds_ord_operation_sub_task_published
    </update>
    
    <insert id="doSnapshotData">
	 INSERT INTO sds_ord_operation_sub_task_published (
			new_id,
			id,
			operation_id,
			physical_resource_id,
			task_id,
			task_type,
			start_time,
			end_time,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			published_log_id
		) 
		SELECT
			CONCAT(id, ${publishedTime}) as new_id,
			id as id,
			operation_id,
			physical_resource_id,
			task_id,
			task_type,
			start_time,
			end_time,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			${publishedLogId} AS published_log_id
		FROM
			sds_ord_operation_sub_task
	</insert>
	
	<delete id="deleteByPublishedLogId" parameterType="java.lang.String">
        delete from 
        	sds_ord_operation_sub_task_published 
        where 
        	published_log_id = #{publishedLogId,jdbcType=VARCHAR}
    </delete>
    
    <select id="selectAllCount" resultType="java.lang.Integer">
        select
        	count(*)
        from sds_ord_operation_sub_task
    </select>
    
    <select id="selectOperationSubTaskByOperationIds" resultMap="POResultMap">
        SELECT
			id as id,
			operation_id,
			physical_resource_id,
			task_id,
			task_type,
			start_time,
			end_time,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time
		FROM
			sds_ord_operation_sub_task
		where 
			operation_id in	
		<foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <delete id="deleteByPublishedLogIds">
        delete 
        from 
        	sds_ord_operation_sub_task_published 
    	where 
    		published_log_id in 
    		<foreach collection="publishedLogIds" item="item" index="index" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </delete>
</mapper>
