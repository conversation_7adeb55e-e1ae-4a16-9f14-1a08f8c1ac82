package com.yhl.scp.mps.domain.workbench;

import com.yhl.scp.mps.domain.workbench.model.req.WorkBenchReq;
import com.yhl.scp.mps.domain.workbench.model.res.WorkBenchRes;

/**
 * <code>IWorkBench</code>
 * <p>
 * 工作台
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 11:50:09
 */
public interface IWorkBench {

    /**
     * 获取个人工作台信息
     *
     * @param req 请求参数
     * @return WorkBenchRes {@link WorkBenchRes}
     */
    WorkBenchRes getWorkBench(WorkBenchReq req);

}
