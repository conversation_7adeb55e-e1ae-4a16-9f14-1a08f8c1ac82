package com.yhl.scp.mps.operationPublished.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>FulfillmentPublishedPO</code>
 * <p>
 * 分配关系发布信息表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:05:30
 */
public class FulfillmentPublishedPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 502020360679522172L;

    /**
     * 供应ID
     */
    private String supplyId;
    /**
     * 需求ID
     */
    private String demandId;
    /**
     * 分配数量
     */
    private BigDecimal fulfillmentQuantity;
    /**
     * 供应订单ID
     */
    private String supplyOrderId;
    /**
     * 需求订单ID
     */
    private String demandOrderId;
    /**
     * 需求类型
     */
    private String demandType;
    /**
     * 供应类型
     */
    private String supplyType;
    /**
     * 需求库存点物品ID
     */
    private String demandProductStockPointId;
    /**
     * 需求物品ID
     */
    private String demandProductId;
    /**
     * 需求库存点ID
     */
    private String demandStockPointId;
    /**
     * 供应库存点物品ID
     */
    private String supplyProductStockPointId;
    /**
     * 供应物品ID
     */
    private String supplyProductId;
    /**
     * 供应库存点ID
     */
    private String supplyStockPointId;
    /**
     * 是否固定
     */
    private String fixed;
    /**
     * 是否使用了替代料
     */
    private String altMaterialUsed;
    /**
     * 替代比例
     */
    private BigDecimal altRate;
    /**
     * 单位换算比例
     */
    private String unitConversionRatio;
    
    /**
     * 主生产计划发布日志id
     */
    private String publishedLogId;
	
    /**
     * 源数据id
     */
    private String id;
    
    /**
     * 源数据id
     */
    private String newId;

    public String getSupplyId() {
        return supplyId;
    }

    public void setSupplyId(String supplyId) {
        this.supplyId = supplyId;
    }

    public String getDemandId() {
        return demandId;
    }

    public void setDemandId(String demandId) {
        this.demandId = demandId;
    }

    public BigDecimal getFulfillmentQuantity() {
        return fulfillmentQuantity;
    }

    public void setFulfillmentQuantity(BigDecimal fulfillmentQuantity) {
        this.fulfillmentQuantity = fulfillmentQuantity;
    }

    public String getSupplyOrderId() {
        return supplyOrderId;
    }

    public void setSupplyOrderId(String supplyOrderId) {
        this.supplyOrderId = supplyOrderId;
    }

    public String getDemandOrderId() {
        return demandOrderId;
    }

    public void setDemandOrderId(String demandOrderId) {
        this.demandOrderId = demandOrderId;
    }

    public String getDemandType() {
        return demandType;
    }

    public void setDemandType(String demandType) {
        this.demandType = demandType;
    }

    public String getSupplyType() {
        return supplyType;
    }

    public void setSupplyType(String supplyType) {
        this.supplyType = supplyType;
    }

    public String getDemandProductStockPointId() {
        return demandProductStockPointId;
    }

    public void setDemandProductStockPointId(String demandProductStockPointId) {
        this.demandProductStockPointId = demandProductStockPointId;
    }

    public String getDemandProductId() {
        return demandProductId;
    }

    public void setDemandProductId(String demandProductId) {
        this.demandProductId = demandProductId;
    }

    public String getDemandStockPointId() {
        return demandStockPointId;
    }

    public void setDemandStockPointId(String demandStockPointId) {
        this.demandStockPointId = demandStockPointId;
    }

    public String getSupplyProductStockPointId() {
        return supplyProductStockPointId;
    }

    public void setSupplyProductStockPointId(String supplyProductStockPointId) {
        this.supplyProductStockPointId = supplyProductStockPointId;
    }

    public String getSupplyProductId() {
        return supplyProductId;
    }

    public void setSupplyProductId(String supplyProductId) {
        this.supplyProductId = supplyProductId;
    }

    public String getSupplyStockPointId() {
        return supplyStockPointId;
    }

    public void setSupplyStockPointId(String supplyStockPointId) {
        this.supplyStockPointId = supplyStockPointId;
    }

    public String getFixed() {
        return fixed;
    }

    public void setFixed(String fixed) {
        this.fixed = fixed;
    }

    public String getAltMaterialUsed() {
        return altMaterialUsed;
    }

    public void setAltMaterialUsed(String altMaterialUsed) {
        this.altMaterialUsed = altMaterialUsed;
    }

    public BigDecimal getAltRate() {
        return altRate;
    }

    public void setAltRate(BigDecimal altRate) {
        this.altRate = altRate;
    }

    public String getUnitConversionRatio() {
        return unitConversionRatio;
    }

    public void setUnitConversionRatio(String unitConversionRatio) {
        this.unitConversionRatio = unitConversionRatio;
    }

	public String getPublishedLogId() {
		return publishedLogId;
	}

	public void setPublishedLogId(String publishedLogId) {
		this.publishedLogId = publishedLogId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getNewId() {
		return newId;
	}

	public void setNewId(String newId) {
		this.newId = newId;
	}

}
