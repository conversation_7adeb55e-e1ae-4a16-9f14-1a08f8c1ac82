package com.yhl.scp.mps.plan.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MasterPlanRelationLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -63671938121755872L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 组织id
     */
    private String orgId;
    /**
     * erp工单号
     */
    private String wipEntityName;
    /**
     * 计划单号
     */
    private String reqNumber;
    /**
     * 状态
     */
    private String statusType;
    /**
     * 数量
     */
    private Integer qty;
    /**
     * 计划单行号
     */
    private String lineNum;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 计划表的id
     */
    private String headId;

}
