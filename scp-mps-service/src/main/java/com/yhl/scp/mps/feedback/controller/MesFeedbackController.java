package com.yhl.scp.mps.feedback.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mps.feedback.dto.MesFeedbackDTO;
import com.yhl.scp.mps.feedback.service.MesFeedbackService;
import com.yhl.scp.mps.feedback.vo.MesFeedbackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MesFeedbackController</code>
 * <p>
 * MesFeedbackController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17 14:46:52
 */
@Slf4j
@Api(tags = "MES反馈记录表控制器")
@RestController
@RequestMapping("mesFeedback")
public class MesFeedbackController extends BaseController {

    @Resource
    private MesFeedbackService mesFeedbackService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @ApiOperation(value = "testConvert")
    @PostMapping(value = "testConvert")
    public BaseResponse<Void> testConvert(@RequestBody List<MesFeedbackDTO> mesFeedbackDTOS) {
        mesFeedbackService.doConvert(mesFeedbackDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "testSync")
    @PostMapping(value = "testSync")
    public BaseResponse<Void> testSync() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MPS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        mesFeedbackService.syncData(mdsScenario.getData(), TenantCodeEnum.FYQB.getCode());
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MesFeedbackVO>> page() {
        List<MesFeedbackVO> mesFeedbackList = mesFeedbackService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MesFeedbackVO> pageInfo = new PageInfo<>(mesFeedbackList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MesFeedbackDTO mesFeedbackDTO) {
        return mesFeedbackService.doCreate(mesFeedbackDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MesFeedbackDTO mesFeedbackDTO) {
        return mesFeedbackService.doUpdate(mesFeedbackDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        mesFeedbackService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MesFeedbackVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mesFeedbackService.selectByPrimaryKey(id));
    }

}
