package com.yhl.scp.mps.operationPublished.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.operationPublished.dto.OperationInputPublishedDTO;
import com.yhl.scp.mps.operationPublished.service.OperationInputPublishedService;
import com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OperationInputPublishedController</code>
 * <p>
 * 工序输入发布信息表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:33:28
 */
@Slf4j
@Api(tags = "工序输入发布信息表控制器")
@RestController
@RequestMapping("operationInputPublished")
public class OperationInputPublishedController extends BaseController {

    @Resource
    private OperationInputPublishedService operationInputPublishedService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OperationInputPublishedVO>> page() {
        List<OperationInputPublishedVO> operationInputPublishedList = operationInputPublishedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OperationInputPublishedVO> pageInfo = new PageInfo<>(operationInputPublishedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OperationInputPublishedDTO operationInputPublishedDTO) {
        return operationInputPublishedService.doCreate(operationInputPublishedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OperationInputPublishedDTO operationInputPublishedDTO) {
        return operationInputPublishedService.doUpdate(operationInputPublishedDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        operationInputPublishedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OperationInputPublishedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationInputPublishedService.selectByPrimaryKey(id));
    }

}
