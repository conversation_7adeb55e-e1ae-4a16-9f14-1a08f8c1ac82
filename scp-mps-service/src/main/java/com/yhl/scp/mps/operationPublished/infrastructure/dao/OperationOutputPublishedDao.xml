<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.OperationOutputPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        <!--@Table sds_ord_operation_output_published-->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="plan_unit_id" jdbcType="VARCHAR" property="planUnitId"/>
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
        <result column="routing_step_sequence_no" jdbcType="INTEGER" property="routingStepSequenceNo"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="main_product" jdbcType="VARCHAR" property="mainProduct"/>
        <result column="output_quantity" jdbcType="VARCHAR" property="outputQuantity"/>
        <result column="appoint_quantity" jdbcType="VARCHAR" property="appointQuantity"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="connection_task" jdbcType="VARCHAR" property="connectionTask"/>
        <result column="connection_type" jdbcType="VARCHAR" property="connectionType"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
     	<result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.vo.OperationOutputPublishedVO">
        <!-- TODO -->
    </resultMap>
    
    <resultMap id="POResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        <!-- TODO -->
    </resultMap>
    
    <sql id="Base_Column_List">
        new_id,order_id,plan_unit_id,operation_id,routing_step_id,routing_step_sequence_no,product_stock_point_id,product_id,stock_point_id,main_product,output_quantity,appoint_quantity,order_type,connection_task,connection_type,max_connection_duration,min_connection_duration,counting_unit_id,remark,enabled,creator,create_time,modifier,modify_time,id,published_log_id
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.newId != null and params.newId != ''">
                and new_id = #{params.newId,jdbcType=VARCHAR}
            </if>
            <if test="params.orderId != null and params.orderId != ''">
                and order_id = #{params.orderId,jdbcType=VARCHAR}
            </if>
            <if test="params.planUnitId != null and params.planUnitId != ''">
                and plan_unit_id = #{params.planUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationId != null and params.operationId != ''">
                and operation_id = #{params.operationId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepSequenceNo != null">
                and routing_step_sequence_no = #{params.routingStepSequenceNo,jdbcType=INTEGER}
            </if>
            <if test="params.productStockPointId != null and params.productStockPointId != ''">
                and product_stock_point_id = #{params.productStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.mainProduct != null and params.mainProduct != ''">
                and main_product = #{params.mainProduct,jdbcType=VARCHAR}
            </if>
            <if test="params.outputQuantity != null">
                and output_quantity = #{params.outputQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.appointQuantity != null">
                and appoint_quantity = #{params.appointQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.orderType != null and params.orderType != ''">
                and order_type = #{params.orderType,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionTask != null and params.connectionTask != ''">
                and connection_task = #{params.connectionTask,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionType != null and params.connectionType != ''">
                and connection_type = #{params.connectionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.publishedLogId != null and params.publishedLogId != ''">
                and published_log_id = #{params.publishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_output_published
        where new_id = #{newId ,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_output_published
        where new_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sds_ord_operation_output_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_output_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_sds_ord_operation_output_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_operation_output_published(
        new_id,
        order_id,
        plan_unit_id,
        operation_id,
        routing_step_id,
        routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        main_product,
        output_quantity,
        appoint_quantity,
        order_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{orderId,jdbcType=VARCHAR},
        #{planUnitId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{routingStepSequenceNo,jdbcType=INTEGER},
        #{productStockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{mainProduct,jdbcType=VARCHAR},
        #{outputQuantity,jdbcType=VARCHAR},
        #{appointQuantity,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        insert into sds_ord_operation_output_published(
        new_id,
        order_id,
        plan_unit_id,
        operation_id,
        routing_step_id,
        routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        main_product,
        output_quantity,
        appoint_quantity,
        order_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{orderId,jdbcType=VARCHAR},
        #{planUnitId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{routingStepSequenceNo,jdbcType=INTEGER},
        #{productStockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{mainProduct,jdbcType=VARCHAR},
        #{outputQuantity,jdbcType=VARCHAR},
        #{appointQuantity,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_operation_output_published(
        new_id,
        order_id,
        plan_unit_id,
        operation_id,
        routing_step_id,
        routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        main_product,
        output_quantity,
        appoint_quantity,
        order_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.orderId,jdbcType=VARCHAR},
        #{entity.planUnitId,jdbcType=VARCHAR},
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.routingStepSequenceNo,jdbcType=INTEGER},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.mainProduct,jdbcType=VARCHAR},
        #{entity.outputQuantity,jdbcType=VARCHAR},
        #{entity.appointQuantity,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_operation_output_published(
        new_id,
        order_id,
        plan_unit_id,
        operation_id,
        routing_step_id,
        routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        main_product,
        output_quantity,
        appoint_quantity,
        order_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.newId,jdbcType=VARCHAR},
        #{entity.orderId,jdbcType=VARCHAR},
        #{entity.planUnitId,jdbcType=VARCHAR},
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.routingStepSequenceNo,jdbcType=INTEGER},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.mainProduct,jdbcType=VARCHAR},
        #{entity.outputQuantity,jdbcType=VARCHAR},
        #{entity.appointQuantity,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        update sds_ord_operation_output_published set
        order_id = #{orderId,jdbcType=VARCHAR},
        plan_unit_id = #{planUnitId,jdbcType=VARCHAR},
        operation_id = #{operationId,jdbcType=VARCHAR},
        routing_step_id = #{routingStepId,jdbcType=VARCHAR},
        routing_step_sequence_no = #{routingStepSequenceNo,jdbcType=INTEGER},
        product_stock_point_id = #{productStockPointId,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        main_product = #{mainProduct,jdbcType=VARCHAR},
        output_quantity = #{outputQuantity,jdbcType=VARCHAR},
        appoint_quantity = #{appointQuantity,jdbcType=VARCHAR},
        order_type = #{orderType,jdbcType=VARCHAR},
        connection_task = #{connectionTask,jdbcType=VARCHAR},
        connection_type = #{connectionType,jdbcType=VARCHAR},
        max_connection_duration = #{maxConnectionDuration,jdbcType=INTEGER},
        min_connection_duration = #{minConnectionDuration,jdbcType=INTEGER},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        id = #{id,jdbcType=VARCHAR},
        published_log_id = #{publishedLogId,jdbcType=VARCHAR}
        where new_id = #{newId,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationOutputPublishedPO">
        update sds_ord_operation_output_published
        <set>
            <if test="item.orderId != null and item.orderId != ''">
                order_id = #{item.orderId,jdbcType=VARCHAR},
            </if>
            <if test="item.planUnitId != null and item.planUnitId != ''">
                plan_unit_id = #{item.planUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepSequenceNo != null">
                routing_step_sequence_no = #{item.routingStepSequenceNo,jdbcType=INTEGER},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.mainProduct != null and item.mainProduct != ''">
                main_product = #{item.mainProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantity != null">
                output_quantity = #{item.outputQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.appointQuantity != null">
                appoint_quantity = #{item.appointQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
                order_type = #{item.orderType,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_operation_output_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.planUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingStepSequenceNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_product = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.mainProduct,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.outputQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_task = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.connectionTask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.connectionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.id,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_log_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.publishedLogId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where new_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sds_ord_operation_output_published 
        <set>
            <if test="item.orderId != null and item.orderId != ''">
                order_id = #{item.orderId,jdbcType=VARCHAR},
            </if>
            <if test="item.planUnitId != null and item.planUnitId != ''">
                plan_unit_id = #{item.planUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepSequenceNo != null">
                routing_step_sequence_no = #{item.routingStepSequenceNo,jdbcType=INTEGER},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.mainProduct != null and item.mainProduct != ''">
                main_product = #{item.mainProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantity != null">
                output_quantity = #{item.outputQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.appointQuantity != null">
                appoint_quantity = #{item.appointQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
                order_type = #{item.orderType,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>  
        where new_id = #{item.newId,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sds_ord_operation_output_published where new_id = #{newId ,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_operation_output_published where new_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <update id="truncateTable">
        TRUNCATE TABLE sds_ord_operation_output_published
    </update>
    
    <insert id="doSnapshotData">
	 INSERT INTO sds_ord_operation_output_published (
			new_id,
			id,
			order_id,
			plan_unit_id,
			operation_id,
			routing_step_id,
			routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			main_product,
			output_quantity,
			appoint_quantity,
			order_type,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			counting_unit_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			published_log_id
		) 
		SELECT
			CONCAT(id, ${publishedTime}) as new_id,
			id as id,
			order_id,
			plan_unit_id,
			operation_id,
			routing_step_id,
			routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			main_product,
			output_quantity,
			appoint_quantity,
			order_type,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			counting_unit_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			${publishedLogId} AS published_log_id
		FROM
			sds_ord_operation_output
	</insert>
	
	<delete id="deleteByPublishedLogId" parameterType="java.lang.String">
        delete from 
        	sds_ord_operation_output_published 
        where 
        	published_log_id = #{publishedLogId,jdbcType=VARCHAR}
    </delete>
    
    <select id="selectAllCount" resultType="java.lang.Integer">
        select
        	count(*)
        from sds_ord_operation_output
    </select>
    
    <select id="selectOperationOutputByOrderIds" resultMap="POResultMap">
        SELECT
			id as id,
			order_id,
			plan_unit_id,
			operation_id,
			routing_step_id,
			routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			main_product,
			output_quantity,
			appoint_quantity,
			order_type,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			counting_unit_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time
		FROM
			sds_ord_operation_output
		where 
			order_id in	
		<foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <delete id="deleteByPublishedLogIds">
        delete 
        from 
        	sds_ord_operation_output_published 
    	where 
    		published_log_id in 
    		<foreach collection="publishedLogIds" item="item" index="index" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </delete>
</mapper>
