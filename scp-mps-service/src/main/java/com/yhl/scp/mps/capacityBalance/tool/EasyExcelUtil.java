package com.yhl.scp.mps.capacityBalance.tool;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <code>EasyExcelUtil</code>
 * <p>
 * excel 工具类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-30 10:55:28
 */
public class EasyExcelUtil {

    /**
     * 初始化响应体
     *
     * @param response 请求头
     * @param fileName 导出名称
     */
    public static void initResponse(HttpServletResponse response, String fileName) {
        // 最终文件名：文件名_(截止yyyy-MM-dd)  --> 这块地方得根据你们自己项目做更改了
        String finalFileName = fileName ;
        // 设置content—type 响应类型
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            // 这里URLEncoder.encode可以防止中文乱码
            finalFileName = URLEncoder.encode(finalFileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=" + finalFileName + ".xlsx");
    }
}
