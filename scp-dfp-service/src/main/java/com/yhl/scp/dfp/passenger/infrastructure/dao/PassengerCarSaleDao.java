package com.yhl.scp.dfp.passenger.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.passenger.infrastructure.po.PassengerCarSalePO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PassengerCarSaleDao extends BaseDao<PassengerCarSalePO, PassengerCarSaleVO> {

    List<PassengerCarSalePO> selectByVehicleModelCodes(@Param("vehicleModelCodeList") List<String> vehicleModelCodeList);

	void doUpdateEnableNo(@Param("ids") List<String> ids);

}
