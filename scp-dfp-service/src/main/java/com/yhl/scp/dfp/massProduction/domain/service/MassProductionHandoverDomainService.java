package com.yhl.scp.dfp.massProduction.domain.service;

import com.alibaba.excel.util.StringUtils;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dfp.massProduction.domain.entity.MassProductionHandoverDO;
import com.yhl.scp.dfp.massProduction.enums.ApprovalStatusEnum;
import com.yhl.scp.dfp.massProduction.infrastructure.dao.MassProductionHandoverDao;
import com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverPO;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MassProductionHandoverDomainService</code>
 * <p>
 * 量产移交信息主表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:14
 */
@Service
public class MassProductionHandoverDomainService {

    @Resource
    private MassProductionHandoverDao massProductionHandoverDao;

    /**
     * 数据校验
     *
     * @param massProductionHandoverDO 领域对象
     */
    public void validation(MassProductionHandoverDO massProductionHandoverDO) {
        checkNotNull(massProductionHandoverDO);
        checkUniqueCode(massProductionHandoverDO);
        // TODO 补充其他校验逻辑
        checkCanUpdate(massProductionHandoverDO);
    }
    
    public void checkCanUpdate(MassProductionHandoverDO massProductionHandoverDO) {
        if(StringUtils.isNotBlank(massProductionHandoverDO.getId())) {
        	MassProductionHandoverPO old = massProductionHandoverDao.selectByPrimaryKey(massProductionHandoverDO.getId());
        	if(!ApprovalStatusEnum.NOT_SUBMITTED.getCode().equals(old.getApprovalStatus())) {
        		throw new BusinessException("当前状态不支持编辑,删除操作");
        	}
        }
    }

    /**
     * 非空检验
     *
     * @param massProductionHandoverDO 领域对象
     */
    private void checkNotNull(MassProductionHandoverDO massProductionHandoverDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param massProductionHandoverDO 领域对象
     */
    private void checkUniqueCode(MassProductionHandoverDO massProductionHandoverDO) {
        // TODO
    }

}
