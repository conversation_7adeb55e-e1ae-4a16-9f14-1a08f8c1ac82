package com.yhl.scp.dfp.global.domain.service;

import com.yhl.scp.dfp.global.domain.entity.GlobalCarSaleDO;
import com.yhl.scp.dfp.global.infrastructure.dao.GlobalCarSaleDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>GlobalCarSaleDomainService</code>
 * <p>
 * 全球汽车销量领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Service
public class GlobalCarSaleDomainService {

    @Resource
    private GlobalCarSaleDao globalCarSaleDao;

    /**
     * 数据校验
     *
     * @param globalCarSaleDO 领域对象
     */
    public void validation(GlobalCarSaleDO globalCarSaleDO) {
        checkNotNull(globalCarSaleDO);
        checkUniqueCode(globalCarSaleDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param globalCarSaleDO 领域对象
     */
    private void checkNotNull(GlobalCarSaleDO globalCarSaleDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param globalCarSaleDO 领域对象
     */
    private void checkUniqueCode(GlobalCarSaleDO globalCarSaleDO) {
        // TODO
    }

}
