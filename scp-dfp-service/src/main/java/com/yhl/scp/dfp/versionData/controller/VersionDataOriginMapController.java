package com.yhl.scp.dfp.versionData.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.versionData.dto.VersionDataOriginMapDTO;
import com.yhl.scp.dfp.versionData.service.VersionDataOriginMapService;
import com.yhl.scp.dfp.versionData.vo.VersionDataOriginMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VersionDataOriginMapController</code>
 * <p>
 * 原始版本数据映射关系控制器
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:29:03
 */
@Slf4j
@Api(tags = "原始版本数据映射关系控制器")
@RestController
@RequestMapping("versionDataOriginMap")
public class VersionDataOriginMapController extends BaseController {

    @Resource
    private VersionDataOriginMapService versionDataOriginMapService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<VersionDataOriginMapVO>> page() {
        List<VersionDataOriginMapVO> versionDataOriginMapList = versionDataOriginMapService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VersionDataOriginMapVO> pageInfo = new PageInfo<>(versionDataOriginMapList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VersionDataOriginMapDTO versionDataOriginMapDTO) {
        return versionDataOriginMapService.doCreate(versionDataOriginMapDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VersionDataOriginMapDTO versionDataOriginMapDTO) {
        return versionDataOriginMapService.doUpdate(versionDataOriginMapDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        versionDataOriginMapService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<VersionDataOriginMapVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, versionDataOriginMapService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        versionDataOriginMapService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
