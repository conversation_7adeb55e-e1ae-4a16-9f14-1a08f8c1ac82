package com.yhl.scp.dfp.clean.domain.service;

import com.yhl.scp.dfp.clean.domain.entity.CleanAlgorithmDataDO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanAlgorithmDataDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>CleanAlgorithmDataDomainService</code>
 * <p>
 * 预测算法数据领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:46
 */
@Service
public class CleanAlgorithmDataDomainService {

    @Resource
    private CleanAlgorithmDataDao cleanAlgorithmDataDao;

    /**
     * 数据校验
     *
     * @param cleanAlgorithmDataDO 领域对象
     */
    public void validation(CleanAlgorithmDataDO cleanAlgorithmDataDO) {
        checkNotNull(cleanAlgorithmDataDO);
        checkUniqueCode(cleanAlgorithmDataDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param cleanAlgorithmDataDO 领域对象
     */
    private void checkNotNull(CleanAlgorithmDataDO cleanAlgorithmDataDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param cleanAlgorithmDataDO 领域对象
     */
    private void checkUniqueCode(CleanAlgorithmDataDO cleanAlgorithmDataDO) {
        // TODO
    }

}
