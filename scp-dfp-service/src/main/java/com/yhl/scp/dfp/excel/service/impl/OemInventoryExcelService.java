package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dfp.oem.convertor.OemInventoryConvertor;
import com.yhl.scp.dfp.oem.dto.OemInventoryDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemInventoryDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemInventoryPO;
import com.yhl.scp.dfp.oem.service.OemInventoryService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemInventoryVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemVehicleModelMapExcelService</code>
 * <p>
 * 主机厂与库存点关系excel导出
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-24 22:05:10
 */
@Service
public class OemInventoryExcelService extends AbstractExcelService<OemInventoryDTO, OemInventoryPO, OemInventoryVO> {

    @Resource
    private OemInventoryDao oemInventoryDao;

    @Resource
    private OemInventoryService oemInventoryService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Override
    public BaseDao<OemInventoryPO, OemInventoryVO> getBaseDao() {
        return oemInventoryDao;
    }

    @Override
    public Function<OemInventoryDTO, OemInventoryPO> getDTO2POConvertor() {
        return OemInventoryConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<OemInventoryDTO> getDTOClass() {
        return OemInventoryDTO.class;
    }

    @Override
    public BaseService<OemInventoryDTO, OemInventoryVO> getBaseService() {
        return oemInventoryService;
    }

    @Override
    protected void fillIdForUpdateData(List<OemInventoryDTO> updateList, Map<String, OemInventoryPO> existingDataMap) {
        for (OemInventoryDTO oemInventoryDTO : updateList) {
            OemInventoryPO oemInventoryPO = existingDataMap.get(oemInventoryDTO.getOemCode() + "&" + oemInventoryDTO.getOemVehicleModelCode() + "&" + DateUtils.dateToString(oemInventoryDTO.getInventoryTime()));
            if (Objects.isNull(oemInventoryPO)) {
                continue;
            }
            oemInventoryDTO.setId(oemInventoryPO.getId());
        }
    }

    @Override
    protected ImportRelatedDataHolder<OemInventoryPO> prepareData(List<OemInventoryDTO> oemVehicleModelMapDTOS) {
        // 找到数据库现在所有的数据
        List<OemInventoryPO> alreadyExitData = oemInventoryDao.selectByParams(new HashMap<>(2));
        Map<String, OemInventoryPO> codeToPOMap = alreadyExitData.stream()
                .collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getOemVehicleModelCode() + "&" + DateUtils.dateToString(x.getInventoryTime(), DateUtils.YEAR_MONTH),
                        Function.identity(),
                        (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("oemCode", "oemVehicleModelCode", "inventoryTime");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<OemInventoryPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    @Override
    protected void processData(List<OemInventoryDTO> oemInventoryDTOS, ImportContext importContext, ImportAnalysisResultHolder<OemInventoryDTO, OemInventoryPO> resultHolder) {
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(oemInventoryDTOS)) {
            Map<String, Object> extraMap = importContext.getExtraMap();
            String yearMonth = extraMap.get("yearMonth").toString();
            Date currentDate = DateUtils.stringToDate(yearMonth, DateUtils.YEAR_MONTH);
            oemInventoryDTOS.forEach(oemInventoryDTO -> oemInventoryDTO.setInventoryTime(currentDate));
        }
        ImportRelatedDataHolder<OemInventoryPO> relatedDataHolder = this.prepareData(oemInventoryDTOS);
        classificationData(importContext, oemInventoryDTOS, resultHolder, relatedDataHolder);
        fillIdForUpdateData(resultHolder.getUpdateList(), relatedDataHolder.getExistingDataMap());
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<OemInventoryDTO, OemInventoryPO> resultHolder, ImportContext importContext) {
        List<OemInventoryDTO> insertList = resultHolder.getInsertList();
        List<OemInventoryDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<OemInventoryDTO> checkList, List<DataImportInfo> importLogList) {
        Map<String, Object> queryParams = MapUtil.newHashMap();
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(queryParams);
        Map<String, OemVehicleModelVO> oemVehicleModelMap = CollectionUtils.isEmpty(oemVehicleModelVOS) ? MapUtil.newHashMap() :
                oemVehicleModelVOS.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getOemVehicleModelCode(), Function.identity(), (v1, v2) -> v1));
        Iterator<OemInventoryDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            OemInventoryDTO oemVehicleModelMapDTO = iterator.next();
            if (oemVehicleModelMap.containsKey(oemVehicleModelMapDTO.getOemCode() + "&" + oemVehicleModelMapDTO.getOemVehicleModelCode())) {
                continue;
            }
            // 主机厂车型信息不存在
            DataImportInfo dataImportInfo = new DataImportInfo();
            dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
            dataImportInfo.setRemark("行数：" + oemVehicleModelMapDTO.getRowIndex() + ";主机厂车型信息不存在");
            dataImportInfo.setDisplayIndex(oemVehicleModelMapDTO.getRowIndex());
            importLogList.add(dataImportInfo);
            iterator.remove();
        }
    }
}
