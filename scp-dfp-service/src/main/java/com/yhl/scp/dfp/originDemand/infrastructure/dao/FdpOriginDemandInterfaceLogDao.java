package com.yhl.scp.dfp.originDemand.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>FdpOriginDemandInterfaceLogDao</code>
 * <p>
 * Edi装车需求接口同步记录表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:25:08
 */
public interface FdpOriginDemandInterfaceLogDao extends BaseDao<FdpOriginDemandInterfaceLogPO, FdpOriginDemandInterfaceLogVO> {
    List<FdpOriginDemandInterfaceLogPO> selectByRelIds(@Param("relIds") List<String> relIds,@Param("importType") String importType);

}
