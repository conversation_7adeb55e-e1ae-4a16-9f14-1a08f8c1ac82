package com.yhl.scp.dfp.clean.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataPO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>CleanForecastDataDao</code>
 * <p>
 * 滚动预测数据DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
public interface CleanForecastDataDao extends BaseDao<CleanForecastDataPO, CleanForecastDataVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link CleanForecastDataVO}
     */
    List<CleanForecastDataVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据版本批量删除
     *
     * @param versionDTOList 删除版本列表
     * @return int
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据版本ID删除
     *
     * @param versionId 版本ID
     * @return int
     */
    int deleteByVersionId(@Param("versionId") String versionId);

}