package com.yhl.scp.dfp.deliverydockingorder.domain.factory;

import com.yhl.scp.dfp.deliverydockingorder.domain.entity.DeliveryDockingOrderDO;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDTO;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>DeliveryDockingOrderFactory</code>
 * <p>
 * 发货对接单管理领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 10:34:52
 */
@Component
public class DeliveryDockingOrderFactory {

    @Resource
    private DeliveryDockingOrderDao deliveryDockingOrderDao;

    DeliveryDockingOrderDO create(DeliveryDockingOrderDTO dto) {
        // TODO
        return null;
    }

}
