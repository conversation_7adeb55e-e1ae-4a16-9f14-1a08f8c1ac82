<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.stock.infrastructure.dao.CurrentBatchQuantityDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO">
        <!--@Table fdp_current_batch_quantity-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization" jdbcType="VARCHAR" property="organization"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_code_desc" jdbcType="VARCHAR" property="productCodeDesc"/>
        <result column="sub_inventory" jdbcType="VARCHAR" property="subInventory"/>
        <result column="sub_inventory_desc" jdbcType="VARCHAR" property="subInventoryDesc"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="location_desc" jdbcType="VARCHAR" property="locationDesc"/>
        <result column="barcode" jdbcType="VARCHAR" property="barcode"/>
        <result column="batch_number" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="per_roll_or_box_quantity" jdbcType="VARCHAR" property="perRollOrBoxQuantity"/>
        <result column="batch_expiration_date" jdbcType="VARCHAR" property="batchExpirationDate"/>
        <result column="classification_code" jdbcType="VARCHAR" property="classificationCode"/>
        <result column="classification_desc" jdbcType="VARCHAR" property="classificationDesc"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="storage_age" jdbcType="VARCHAR" property="storageAge"/>
        <result column="storage_age_days" jdbcType="VARCHAR" property="storageAgeDays"/>
        <result column="shelf_life" jdbcType="VARCHAR" property="shelfLife"/>
        <result column="days_to_expiry" jdbcType="VARCHAR" property="daysToExpiry"/>
        <result column="feature_desc" jdbcType="VARCHAR" property="featureDesc"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.CurrentBatchQuantityVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,organization,product_code,product_code_desc,sub_inventory,sub_inventory_desc,location,location_desc,barcode,batch_number,quantity,unit,area,weight,per_roll_or_box_quantity,batch_expiration_date,classification_code,classification_desc,storage_time,last_update_time,storage_age,storage_age_days,shelf_life,days_to_expiry,feature_desc,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.organization != null and params.organization != ''">
                and organization = #{params.organization,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeDesc != null and params.productCodeDesc != ''">
                and product_code_desc = #{params.productCodeDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.subInventory != null and params.subInventory != ''">
                and sub_inventory = #{params.subInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.subInventoryDesc != null and params.subInventoryDesc != ''">
                and sub_inventory_desc = #{params.subInventoryDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.location != null and params.location != ''">
                and location = #{params.location,jdbcType=VARCHAR}
            </if>
            <if test="params.locationDesc != null and params.locationDesc != ''">
                and location_desc = #{params.locationDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.barcode != null and params.barcode != ''">
                and barcode = #{params.barcode,jdbcType=VARCHAR}
            </if>
            <if test="params.batchNumber != null and params.batchNumber != ''">
                and batch_number = #{params.batchNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=INTEGER}
            </if>
            <if test="params.unit != null and params.unit != ''">
                and unit = #{params.unit,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.weight != null">
                and weight = #{params.weight,jdbcType=VARCHAR}
            </if>
            <if test="params.perRollOrBoxQuantity != null">
                and per_roll_or_box_quantity = #{params.perRollOrBoxQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.batchExpirationDate != null and params.batchExpirationDate != ''">
                and batch_expiration_date = #{params.batchExpirationDate,jdbcType=VARCHAR}
            </if>
            <if test="params.classificationCode != null and params.classificationCode != ''">
                and classification_code = #{params.classificationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.classificationDesc != null and params.classificationDesc != ''">
                and classification_desc = #{params.classificationDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.storageTime != null">
                and storage_time = #{params.storageTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.storageAge != null and params.storageAge != ''">
                and storage_age = #{params.storageAge,jdbcType=VARCHAR}
            </if>
            <if test="params.storageAgeDays != null">
                and storage_age_days = #{params.storageAgeDays,jdbcType=VARCHAR}
            </if>
            <if test="params.shelfLife != null">
                and shelf_life = #{params.shelfLife,jdbcType=VARCHAR}
            </if>
            <if test="params.daysToExpiry != null">
                and days_to_expiry = #{params.daysToExpiry,jdbcType=VARCHAR}
            </if>
            <if test="params.featureDesc != null and params.featureDesc != ''">
                and feature_desc = #{params.featureDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_current_batch_quantity
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_current_batch_quantity
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_current_batch_quantity
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_current_batch_quantity
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectNotExistInventoryRealTimeData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_current_batch_quantity a
        where not exists(select 1
        from fdp_inventory_real_time_data b
        where a.id = b.id)
    </select>
    <select id="selectExistInventoryRealTimeData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_current_batch_quantity a
        where exists(select 1
        from fdp_inventory_real_time_data b
        where a.id = b.id) and a.last_update_time > DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH);
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_current_batch_quantity(
        id,
        organization,
        product_code,
        product_code_desc,
        sub_inventory,
        sub_inventory_desc,
        location,
        location_desc,
        barcode,
        batch_number,
        quantity,
        unit,
        area,
        weight,
        per_roll_or_box_quantity,
        batch_expiration_date,
        classification_code,
        classification_desc,
        storage_time,
        last_update_time,
        storage_age,
        storage_age_days,
        shelf_life,
        days_to_expiry,
        feature_desc,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{organization,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productCodeDesc,jdbcType=VARCHAR},
        #{subInventory,jdbcType=VARCHAR},
        #{subInventoryDesc,jdbcType=VARCHAR},
        #{location,jdbcType=VARCHAR},
        #{locationDesc,jdbcType=VARCHAR},
        #{barcode,jdbcType=VARCHAR},
        #{batchNumber,jdbcType=VARCHAR},
        #{quantity,jdbcType=INTEGER},
        #{unit,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR},
        #{perRollOrBoxQuantity,jdbcType=VARCHAR},
        #{batchExpirationDate,jdbcType=VARCHAR},
        #{classificationCode,jdbcType=VARCHAR},
        #{classificationDesc,jdbcType=VARCHAR},
        #{storageTime,jdbcType=TIMESTAMP},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{storageAge,jdbcType=VARCHAR},
        #{storageAgeDays,jdbcType=VARCHAR},
        #{shelfLife,jdbcType=VARCHAR},
        #{daysToExpiry,jdbcType=VARCHAR},
        #{featureDesc,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO">
        insert into fdp_current_batch_quantity(id,
                                               organization,
                                               product_code,
                                               product_code_desc,
                                               sub_inventory,
                                               sub_inventory_desc,
                                               location,
                                               location_desc,
                                               barcode,
                                               batch_number,
                                               quantity,
                                               unit,
                                               area,
                                               weight,
                                               per_roll_or_box_quantity,
                                               batch_expiration_date,
                                               classification_code,
                                               classification_desc,
                                               storage_time,
                                               last_update_time,
                                               storage_age,
                                               storage_age_days,
                                               shelf_life,
                                               days_to_expiry,
                                               feature_desc,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,version_value)
        values (#{id,jdbcType=VARCHAR},
                #{organization,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productCodeDesc,jdbcType=VARCHAR},
                #{subInventory,jdbcType=VARCHAR},
                #{subInventoryDesc,jdbcType=VARCHAR},
                #{location,jdbcType=VARCHAR},
                #{locationDesc,jdbcType=VARCHAR},
                #{barcode,jdbcType=VARCHAR},
                #{batchNumber,jdbcType=VARCHAR},
                #{quantity,jdbcType=INTEGER},
                #{unit,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR},
                #{weight,jdbcType=VARCHAR},
                #{perRollOrBoxQuantity,jdbcType=VARCHAR},
                #{batchExpirationDate,jdbcType=VARCHAR},
                #{classificationCode,jdbcType=VARCHAR},
                #{classificationDesc,jdbcType=VARCHAR},
                #{storageTime,jdbcType=TIMESTAMP},
                #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{storageAge,jdbcType=VARCHAR},
                #{storageAgeDays,jdbcType=VARCHAR},
                #{shelfLife,jdbcType=VARCHAR},
                #{daysToExpiry,jdbcType=VARCHAR},
                #{featureDesc,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_current_batch_quantity(
        id,
        organization,
        product_code,
        product_code_desc,
        sub_inventory,
        sub_inventory_desc,
        location,
        location_desc,
        barcode,
        batch_number,
        quantity,
        unit,
        area,
        weight,
        per_roll_or_box_quantity,
        batch_expiration_date,
        classification_code,
        classification_desc,
        storage_time,
        last_update_time,
        storage_age,
        storage_age_days,
        shelf_life,
        days_to_expiry,
        feature_desc,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.organization,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productCodeDesc,jdbcType=VARCHAR},
            #{entity.subInventory,jdbcType=VARCHAR},
            #{entity.subInventoryDesc,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.locationDesc,jdbcType=VARCHAR},
            #{entity.barcode,jdbcType=VARCHAR},
            #{entity.batchNumber,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=INTEGER},
            #{entity.unit,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.perRollOrBoxQuantity,jdbcType=VARCHAR},
            #{entity.batchExpirationDate,jdbcType=VARCHAR},
            #{entity.classificationCode,jdbcType=VARCHAR},
            #{entity.classificationDesc,jdbcType=VARCHAR},
            #{entity.storageTime,jdbcType=TIMESTAMP},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.storageAge,jdbcType=VARCHAR},
            #{entity.storageAgeDays,jdbcType=VARCHAR},
            #{entity.shelfLife,jdbcType=VARCHAR},
            #{entity.daysToExpiry,jdbcType=VARCHAR},
            #{entity.featureDesc,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_current_batch_quantity(
        id,
        organization,
        product_code,
        product_code_desc,
        sub_inventory,
        sub_inventory_desc,
        location,
        location_desc,
        barcode,
        batch_number,
        quantity,
        unit,
        area,
        weight,
        per_roll_or_box_quantity,
        batch_expiration_date,
        classification_code,
        classification_desc,
        storage_time,
        last_update_time,
        storage_age,
        storage_age_days,
        shelf_life,
        days_to_expiry,
        feature_desc,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.organization,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productCodeDesc,jdbcType=VARCHAR},
            #{entity.subInventory,jdbcType=VARCHAR},
            #{entity.subInventoryDesc,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.locationDesc,jdbcType=VARCHAR},
            #{entity.barcode,jdbcType=VARCHAR},
            #{entity.batchNumber,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=INTEGER},
            #{entity.unit,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.perRollOrBoxQuantity,jdbcType=VARCHAR},
            #{entity.batchExpirationDate,jdbcType=VARCHAR},
            #{entity.classificationCode,jdbcType=VARCHAR},
            #{entity.classificationDesc,jdbcType=VARCHAR},
            #{entity.storageTime,jdbcType=TIMESTAMP},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.storageAge,jdbcType=VARCHAR},
            #{entity.storageAgeDays,jdbcType=VARCHAR},
            #{entity.shelfLife,jdbcType=VARCHAR},
            #{entity.daysToExpiry,jdbcType=VARCHAR},
            #{entity.featureDesc,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO">
        update fdp_current_batch_quantity
        set organization             = #{organization,jdbcType=VARCHAR},
            product_code             = #{productCode,jdbcType=VARCHAR},
            product_code_desc        = #{productCodeDesc,jdbcType=VARCHAR},
            sub_inventory            = #{subInventory,jdbcType=VARCHAR},
            sub_inventory_desc       = #{subInventoryDesc,jdbcType=VARCHAR},
            location                 = #{location,jdbcType=VARCHAR},
            location_desc            = #{locationDesc,jdbcType=VARCHAR},
            barcode                  = #{barcode,jdbcType=VARCHAR},
            batch_number             = #{batchNumber,jdbcType=VARCHAR},
            quantity                 = #{quantity,jdbcType=INTEGER},
            unit                     = #{unit,jdbcType=VARCHAR},
            area                     = #{area,jdbcType=VARCHAR},
            weight                   = #{weight,jdbcType=VARCHAR},
            per_roll_or_box_quantity = #{perRollOrBoxQuantity,jdbcType=VARCHAR},
            batch_expiration_date    = #{batchExpirationDate,jdbcType=VARCHAR},
            classification_code      = #{classificationCode,jdbcType=VARCHAR},
            classification_desc      = #{classificationDesc,jdbcType=VARCHAR},
            storage_time             = #{storageTime,jdbcType=TIMESTAMP},
            last_update_time         = #{lastUpdateTime,jdbcType=TIMESTAMP},
            storage_age              = #{storageAge,jdbcType=VARCHAR},
            storage_age_days         = #{storageAgeDays,jdbcType=VARCHAR},
            shelf_life               = #{shelfLife,jdbcType=VARCHAR},
            days_to_expiry           = #{daysToExpiry,jdbcType=VARCHAR},
            feature_desc             = #{featureDesc,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=VARCHAR},
            enabled                  = #{enabled,jdbcType=VARCHAR},
            modifier                 = #{modifier,jdbcType=VARCHAR},
            modify_time              = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_current_batch_quantity set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO">
        update fdp_current_batch_quantity
        <set>
            <if test="item.organization != null and item.organization != ''">
                organization = #{item.organization,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCodeDesc != null and item.productCodeDesc != ''">
                product_code_desc = #{item.productCodeDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.subInventory != null and item.subInventory != ''">
                sub_inventory = #{item.subInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.subInventoryDesc != null and item.subInventoryDesc != ''">
                sub_inventory_desc = #{item.subInventoryDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.location != null and item.location != ''">
                location = #{item.location,jdbcType=VARCHAR},
            </if>
            <if test="item.locationDesc != null and item.locationDesc != ''">
                location_desc = #{item.locationDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.barcode != null and item.barcode != ''">
                barcode = #{item.barcode,jdbcType=VARCHAR},
            </if>
            <if test="item.batchNumber != null and item.batchNumber != ''">
                batch_number = #{item.batchNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=INTEGER},
            </if>
            <if test="item.unit != null and item.unit != ''">
                unit = #{item.unit,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.weight != null">
                weight = #{item.weight,jdbcType=VARCHAR},
            </if>
            <if test="item.perRollOrBoxQuantity != null">
                per_roll_or_box_quantity = #{item.perRollOrBoxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.batchExpirationDate != null and item.batchExpirationDate != ''">
                batch_expiration_date = #{item.batchExpirationDate,jdbcType=VARCHAR},
            </if>
            <if test="item.classificationCode != null and item.classificationCode != ''">
                classification_code = #{item.classificationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.classificationDesc != null and item.classificationDesc != ''">
                classification_desc = #{item.classificationDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.storageTime != null">
                storage_time = #{item.storageTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.storageAge != null and item.storageAge != ''">
                storage_age = #{item.storageAge,jdbcType=VARCHAR},
            </if>
            <if test="item.storageAgeDays != null">
                storage_age_days = #{item.storageAgeDays,jdbcType=VARCHAR},
            </if>
            <if test="item.shelfLife != null">
                shelf_life = #{item.shelfLife,jdbcType=VARCHAR},
            </if>
            <if test="item.daysToExpiry != null">
                days_to_expiry = #{item.daysToExpiry,jdbcType=VARCHAR},
            </if>
            <if test="item.featureDesc != null and item.featureDesc != ''">
                feature_desc = #{item.featureDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_current_batch_quantity set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_current_batch_quantity
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="organization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organization,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCodeDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sub_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sub_inventory_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subInventoryDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.location,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.locationDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="barcode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.barcode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batchNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.weight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="per_roll_or_box_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.perRollOrBoxQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_expiration_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batchExpirationDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="classification_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.classificationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="classification_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.classificationDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="storage_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="storage_age = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageAge,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="storage_age_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageAgeDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shelf_life = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shelfLife,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="days_to_expiry = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.daysToExpiry,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="feature_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.featureDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_current_batch_quantity set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_current_batch_quantity
            <set>
                <if test="item.organization != null and item.organization != ''">
                    organization = #{item.organization,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCodeDesc != null and item.productCodeDesc != ''">
                    product_code_desc = #{item.productCodeDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.subInventory != null and item.subInventory != ''">
                    sub_inventory = #{item.subInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.subInventoryDesc != null and item.subInventoryDesc != ''">
                    sub_inventory_desc = #{item.subInventoryDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.location != null and item.location != ''">
                    location = #{item.location,jdbcType=VARCHAR},
                </if>
                <if test="item.locationDesc != null and item.locationDesc != ''">
                    location_desc = #{item.locationDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.barcode != null and item.barcode != ''">
                    barcode = #{item.barcode,jdbcType=VARCHAR},
                </if>
                <if test="item.batchNumber != null and item.batchNumber != ''">
                    batch_number = #{item.batchNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=INTEGER},
                </if>
                <if test="item.unit != null and item.unit != ''">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.area != null">
                    area = #{item.area,jdbcType=VARCHAR},
                </if>
                <if test="item.weight != null">
                    weight = #{item.weight,jdbcType=VARCHAR},
                </if>
                <if test="item.perRollOrBoxQuantity != null">
                    per_roll_or_box_quantity = #{item.perRollOrBoxQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.batchExpirationDate != null and item.batchExpirationDate != ''">
                    batch_expiration_date = #{item.batchExpirationDate,jdbcType=VARCHAR},
                </if>
                <if test="item.classificationCode != null and item.classificationCode != ''">
                    classification_code = #{item.classificationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.classificationDesc != null and item.classificationDesc != ''">
                    classification_desc = #{item.classificationDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.storageTime != null">
                    storage_time = #{item.storageTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastUpdateTime != null">
                    last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.storageAge != null and item.storageAge != ''">
                    storage_age = #{item.storageAge,jdbcType=VARCHAR},
                </if>
                <if test="item.storageAgeDays != null">
                    storage_age_days = #{item.storageAgeDays,jdbcType=VARCHAR},
                </if>
                <if test="item.shelfLife != null">
                    shelf_life = #{item.shelfLife,jdbcType=VARCHAR},
                </if>
                <if test="item.daysToExpiry != null">
                    days_to_expiry = #{item.daysToExpiry,jdbcType=VARCHAR},
                </if>
                <if test="item.featureDesc != null and item.featureDesc != ''">
                    feature_desc = #{item.featureDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
                and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_current_batch_quantity set
                version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
                and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_current_batch_quantity
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_current_batch_quantity where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
	
	<!-- 批量id+版本删除 -->
	<delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_current_batch_quantity where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
