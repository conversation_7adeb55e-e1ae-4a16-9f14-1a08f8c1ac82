package com.yhl.scp.dfp.global.domain.service;

import com.yhl.scp.dfp.global.domain.entity.GlobalCarSaleDetailDO;
import com.yhl.scp.dfp.global.infrastructure.dao.GlobalCarSaleDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>GlobalCarSaleDetailDomainService</code>
 * <p>
 * 全球汽车销量详情领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Service
public class GlobalCarSaleDetailDomainService {

    @Resource
    private GlobalCarSaleDetailDao globalCarSaleDetailDao;

    /**
     * 数据校验
     *
     * @param globalCarSaleDetailDO 领域对象
     */
    public void validation(GlobalCarSaleDetailDO globalCarSaleDetailDO) {
        checkNotNull(globalCarSaleDetailDO);
        checkUniqueCode(globalCarSaleDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param globalCarSaleDetailDO 领域对象
     */
    private void checkNotNull(GlobalCarSaleDetailDO globalCarSaleDetailDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param globalCarSaleDetailDO 领域对象
     */
    private void checkUniqueCode(GlobalCarSaleDetailDO globalCarSaleDetailDO) {
        // TODO
    }

}
