package com.yhl.scp.dfp.versionData.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>VersionDataDailyMapDO</code>
 * <p>
 * 日需求版本数据映射关系DO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:25:59
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class VersionDataDailyMapDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 628714133819383549L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 版本ID
     */
    private String versionId;
    /**
     * 数据ID
     */
    private String demandId;

}
