package com.yhl.scp.dfp.clean.infrastructure.dao;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>CleanDemandDataDao</code>
 * <p>
 * 日需求数据DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
public interface CleanDemandDataDao extends BaseDao<CleanDemandDataPO, CleanDemandDataVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link CleanDemandDataVO}
     */
    List<CleanDemandDataVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    void deleteByVersionId(String versionId);

    List<LabelValue<String>> selectOemDropdown(String versionId);

}
