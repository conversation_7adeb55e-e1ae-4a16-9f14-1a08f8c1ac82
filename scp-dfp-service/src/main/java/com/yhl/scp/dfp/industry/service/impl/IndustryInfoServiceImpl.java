package com.yhl.scp.dfp.industry.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.industry.convertor.IndustryInfoConvertor;
import com.yhl.scp.dfp.industry.domain.entity.IndustryInfoDO;
import com.yhl.scp.dfp.industry.domain.service.IndustryInfoDomainService;
import com.yhl.scp.dfp.industry.dto.IndustryInfoDTO;
import com.yhl.scp.dfp.industry.infrastructure.dao.IndustryInfoDao;
import com.yhl.scp.dfp.industry.infrastructure.po.IndustryInfoPO;
import com.yhl.scp.dfp.industry.service.IndustryInfoService;
import com.yhl.scp.dfp.industry.vo.IndustryInfoVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>IndustryInfoServiceImpl</code>
 * <p>
 * 行业资讯搜索应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-25 15:41:27
 */
@Slf4j
@Service
public class IndustryInfoServiceImpl extends AbstractService implements IndustryInfoService {

    @Resource
    private IndustryInfoDao industryInfoDao;

    @Resource
    private IndustryInfoDomainService industryInfoDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(IndustryInfoDTO industryInfoDTO) {
        // 0.数据转换
        IndustryInfoDO industryInfoDO = IndustryInfoConvertor.INSTANCE.dto2Do(industryInfoDTO);
        IndustryInfoPO industryInfoPO = IndustryInfoConvertor.INSTANCE.dto2Po(industryInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        industryInfoDomainService.validation(industryInfoDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(industryInfoPO);
        industryInfoDao.insert(industryInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(IndustryInfoDTO industryInfoDTO) {
        // 0.数据转换
        IndustryInfoDO industryInfoDO = IndustryInfoConvertor.INSTANCE.dto2Do(industryInfoDTO);
        IndustryInfoPO industryInfoPO = IndustryInfoConvertor.INSTANCE.dto2Po(industryInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        industryInfoDomainService.validation(industryInfoDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(industryInfoPO);
        industryInfoDao.update(industryInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<IndustryInfoDTO> list) {
        List<IndustryInfoPO> newList = IndustryInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        industryInfoDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<IndustryInfoDTO> list) {
        List<IndustryInfoPO> newList = IndustryInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        industryInfoDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return industryInfoDao.deleteBatch(idList);
        }
        return industryInfoDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public IndustryInfoVO selectByPrimaryKey(String id) {
        IndustryInfoPO po = industryInfoDao.selectByPrimaryKey(id);
        return IndustryInfoConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "INDUSTRY_INFO")
    public List<IndustryInfoVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "INDUSTRY_INFO")
    public List<IndustryInfoVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<IndustryInfoVO> dataList = industryInfoDao.selectByCondition(sortParam, queryCriteriaParam);
        IndustryInfoServiceImpl target = SpringBeanUtils.getBean(IndustryInfoServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<IndustryInfoVO> selectByParams(Map<String, Object> params) {
        List<IndustryInfoPO> list = industryInfoDao.selectByParams(params);
        return IndustryInfoConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<IndustryInfoVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.INDUSTRY_INFO.getCode();
    }

    @Override
    public List<IndustryInfoVO> invocation(List<IndustryInfoVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        industryInfoDomainService.checkDelete(versionDTOList);
        return industryInfoDao.deleteBatchVersion(versionDTOList);
    }
}
