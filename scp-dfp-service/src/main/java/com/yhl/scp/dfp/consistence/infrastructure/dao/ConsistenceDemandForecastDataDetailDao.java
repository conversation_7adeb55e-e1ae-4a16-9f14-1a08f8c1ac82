package com.yhl.scp.dfp.consistence.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ConsistenceDemandForecastDataDetailDao</code>
 * <p>
 * 一致性业务预测数据明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 19:56:39
 */
public interface ConsistenceDemandForecastDataDetailDao extends BaseDao<ConsistenceDemandForecastDataDetailPO,
        ConsistenceDemandForecastDataDetailVO> {

    List<ConsistenceDemandForecastDataDetailPO> selectByConsistenceDemandForecastDataIds(
            @Param("demandForecastIds") List<String> demandForecastIds);

    List<ConsistenceDemandForecastDataDetailPO> selectByParentIdsAndMonth(@Param("ids")List<String> ids,
                                                                          @Param("month") Date month);
    /**
     * 根据版本批量删除
     *
     * @param versionDTOList 待删除列表
     * @return int
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByStockPointId(String id);

    String selectLatestPublishedVersionCode();

    List<ConsistenceDemandForecastDataDetailPO> selectDataByIds(@Param("parentIdList") List<String> parentIdList);

    List<ConsistenceDemandForecastDataDetailVO>  selectForecastQuantitySumByOemCodesAndMonths(
            @Param("params") Map<String, Object> params);

}