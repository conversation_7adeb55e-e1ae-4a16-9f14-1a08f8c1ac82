package com.yhl.scp.dfp.warehouse.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordLogDTO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordLogService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "控制器")
@RestController
@RequestMapping("dfpWarehouseReleaseRecordLog")
public class WarehouseReleaseRecordLogController extends BaseController {

    @Resource
    private WarehouseReleaseRecordLogService warehouseReleaseRecordLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<WarehouseReleaseRecordLogVO>> page() {
        List<WarehouseReleaseRecordLogVO> warehouseReleaseRecordLogVOS = warehouseReleaseRecordLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<WarehouseReleaseRecordLogVO> pageInfo = new PageInfo<>(warehouseReleaseRecordLogVOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody WarehouseReleaseRecordLogDTO warehouseReleaseRecordLogDTO) {
        return warehouseReleaseRecordLogService.doCreate(warehouseReleaseRecordLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody WarehouseReleaseRecordLogDTO warehouseReleaseRecordLogDTO) {
        return warehouseReleaseRecordLogService.doUpdate(warehouseReleaseRecordLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        warehouseReleaseRecordLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<WarehouseReleaseRecordLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, warehouseReleaseRecordLogService.selectByPrimaryKey(id));
    }

}
