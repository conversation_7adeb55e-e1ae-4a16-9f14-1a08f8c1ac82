package com.yhl.scp.dfp.supplier.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.supplier.dto.SupplierSubmissionDetailDTO;
import com.yhl.scp.dfp.supplier.service.SupplierSubmissionDetailService;
import com.yhl.scp.dfp.supplier.vo.SupplierSubmissionDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>SupplierSubmissionDetailController</code>
 * <p>
 * 车型全供应商提报明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-10 09:32:33
 */
@Slf4j
@Api(tags = "车型全供应商提报明细控制器")
@RestController
@RequestMapping("supplierSubmissionDetail")
public class SupplierSubmissionDetailController extends BaseController {

    @Resource
    private SupplierSubmissionDetailService supplierSubmissionDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<SupplierSubmissionDetailVO>> page() {
        List<SupplierSubmissionDetailVO> supplierSubmissionDetailList = supplierSubmissionDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<SupplierSubmissionDetailVO> pageInfo = new PageInfo<>(supplierSubmissionDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody SupplierSubmissionDetailDTO supplierSubmissionDetailDTO) {
        return supplierSubmissionDetailService.doCreate(supplierSubmissionDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody SupplierSubmissionDetailDTO supplierSubmissionDetailDTO) {
        return supplierSubmissionDetailService.doUpdate(supplierSubmissionDetailDTO);
    }

    @ApiOperation(value = "批量修改")
    @PostMapping(value = "updateBatch")
    public BaseResponse<Void> update(@RequestBody List<SupplierSubmissionDetailDTO> list) {
       supplierSubmissionDetailService.doUpdateBatch(list);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        supplierSubmissionDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<SupplierSubmissionDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, supplierSubmissionDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        supplierSubmissionDetailService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}
