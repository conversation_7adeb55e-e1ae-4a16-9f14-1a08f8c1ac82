package com.yhl.scp.dfp.passenger.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.passenger.infrastructure.po.PassengerCarSaleImportPO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleImportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>PassengerCarSaleImportDao</code>
 * <p>
 * 乘用车市场信息导入临时表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25 15:20:15
 */
public interface PassengerCarSaleImportDao extends BaseDao<PassengerCarSaleImportPO, PassengerCarSaleImportVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link PassengerCarSaleImportVO}
     */
    List<PassengerCarSaleImportVO> selectVOByParams(@Param("params") Map<String, Object> params);

	void deleteByYearMonth(@Param("yearMonth") String yearMonth);

}
