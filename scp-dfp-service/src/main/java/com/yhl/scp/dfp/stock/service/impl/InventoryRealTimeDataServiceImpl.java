package com.yhl.scp.dfp.stock.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.vo.RealTimeInventoryVO;
import com.yhl.scp.dfp.stock.convertor.InventoryRealTimeDataConvertor;
import com.yhl.scp.dfp.stock.domain.entity.InventoryRealTimeDataDO;
import com.yhl.scp.dfp.stock.domain.service.InventoryRealTimeDataDomainService;
import com.yhl.scp.dfp.stock.dto.InventoryDataDTO;
import com.yhl.scp.dfp.stock.dto.InventoryRealTimeDataDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.CurrentBatchQuantityDao;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryRealTimeDataDao;
import com.yhl.scp.dfp.stock.infrastructure.dao.OriginalFilmCurrentBatchQuantityDao;
import com.yhl.scp.dfp.stock.infrastructure.po.CurrentBatchQuantityPO;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO;
import com.yhl.scp.dfp.stock.infrastructure.po.OriginalFilmCurrentBatchQuantityPO;
import com.yhl.scp.dfp.stock.service.InventoryRealTimeDataService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <code>InventoryRealTimeDataServiceImpl</code>
 * <p>
 * 库存实时数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:40:18
 */
@Slf4j
@Service
public class InventoryRealTimeDataServiceImpl extends AbstractService implements InventoryRealTimeDataService {

    @Resource
    private InventoryRealTimeDataDao inventoryRealTimeDataDao;

    @Resource
    private InventoryRealTimeDataDomainService inventoryRealTimeDataDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private CurrentBatchQuantityDao currentBatchQuantityDao;

    @Resource
    private OriginalFilmCurrentBatchQuantityDao originalFilmCurrentBatchQuantityDao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(InventoryRealTimeDataDTO inventoryRealTimeDataDTO) {
        // 0.数据转换
        InventoryRealTimeDataDO inventoryRealTimeDataDO = InventoryRealTimeDataConvertor.INSTANCE.dto2Do(inventoryRealTimeDataDTO);
        InventoryRealTimeDataPO inventoryRealTimeDataPO = InventoryRealTimeDataConvertor.INSTANCE.dto2Po(inventoryRealTimeDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryRealTimeDataDomainService.validation(inventoryRealTimeDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryRealTimeDataPO);
        inventoryRealTimeDataDao.insert(inventoryRealTimeDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(InventoryRealTimeDataDTO inventoryRealTimeDataDTO) {
        // 0.数据转换
        InventoryRealTimeDataDO inventoryRealTimeDataDO = InventoryRealTimeDataConvertor.INSTANCE.dto2Do(inventoryRealTimeDataDTO);
        InventoryRealTimeDataPO inventoryRealTimeDataPO = InventoryRealTimeDataConvertor.INSTANCE.dto2Po(inventoryRealTimeDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryRealTimeDataDomainService.validation(inventoryRealTimeDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryRealTimeDataPO);
        inventoryRealTimeDataDao.update(inventoryRealTimeDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryRealTimeDataDTO> list) {
        List<InventoryRealTimeDataPO> newList = InventoryRealTimeDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryRealTimeDataDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryRealTimeDataDTO> list) {
        List<InventoryRealTimeDataPO> newList = InventoryRealTimeDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryRealTimeDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryRealTimeDataDao.deleteBatch(idList);
        }
        return inventoryRealTimeDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryRealTimeDataVO selectByPrimaryKey(String id) {
        InventoryRealTimeDataPO po = inventoryRealTimeDataDao.selectByPrimaryKey(id);
        return InventoryRealTimeDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "INVENTORY_REAL_TIME_DATA")
    public List<InventoryRealTimeDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "INVENTORY_REAL_TIME_DATA")
    public List<InventoryRealTimeDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryRealTimeDataVO> dataList = inventoryRealTimeDataDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryRealTimeDataServiceImpl target = SpringBeanUtils.getBean(InventoryRealTimeDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryRealTimeDataVO> selectByParams(Map<String, Object> params) {
        List<InventoryRealTimeDataPO> list = inventoryRealTimeDataDao.selectByParams(params);
        return InventoryRealTimeDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryRealTimeDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.INVENTORY_REAL_TIME_DATA.getCode();
    }

    @Override
    public List<InventoryRealTimeDataVO> invocation(List<InventoryRealTimeDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    /**
     * todo 批次现有量数据和原片库存批次明细数据表更新时要刷新实时库存数据
     * 合并批次现有量数据和原片库存批次明细数据
     */
    @Override
    public void doMergeStockBatchData() {
        String dataSource = DynamicDataSourceContextHolder.getDataSource();
        int degree = getInvocationName().split("#").length;
        ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.getDegreeThreadPool(degree);

        // 1.查询不存在实时库存数据表中的批次现有量数据
        CompletableFuture<List<CurrentBatchQuantityPO>> notExistxCurrentBatchQuantityPOListCompletableFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            return currentBatchQuantityDao.selectNotExistInventoryRealTimeData();
        }, threadPoolExecutor);

        // 2.查询存在实时库存数据表中的批次现有量数据
        CompletableFuture<List<CurrentBatchQuantityPO>> existxCurrentBatchQuantityPOListCompletableFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            return currentBatchQuantityDao.selectExistInventoryRealTimeData();
        }, threadPoolExecutor);

        // 3.查询不存在实时库存数据表中的原片库存批次明细数据
        CompletableFuture<List<OriginalFilmCurrentBatchQuantityPO>> notExistsOriginalFilmCurrentBatchQuantityPOListCompletableFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            return originalFilmCurrentBatchQuantityDao.selectNotExistInventoryRealTimeData();
        }, threadPoolExecutor);

        // 4.查询不存在实时库存数据表中的原片库存批次明细数据
        CompletableFuture<List<OriginalFilmCurrentBatchQuantityPO>> existsOriginalFilmCurrentBatchQuantityPOListCompletableFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            return originalFilmCurrentBatchQuantityDao.selectExistInventoryRealTimeData();
        }, threadPoolExecutor);

        List<InventoryRealTimeDataPO> insertList = getInventoryRealTimeData(notExistxCurrentBatchQuantityPOListCompletableFuture.join(), notExistsOriginalFilmCurrentBatchQuantityPOListCompletableFuture.join());
        if (CollectionUtils.isNotEmpty(insertList)) {
            Lists.partition(insertList, 500).forEach(inventoryRealTimeDataDao::insertBatchWithPrimaryKey);
        }

        List<InventoryRealTimeDataPO> updateList = getInventoryRealTimeData(existxCurrentBatchQuantityPOListCompletableFuture.join(), existsOriginalFilmCurrentBatchQuantityPOListCompletableFuture.join());
        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 500).forEach(inventoryRealTimeDataDao::updateBatch);
        }
    }


    private List<InventoryRealTimeDataPO> getInventoryRealTimeData(List<CurrentBatchQuantityPO> currentBatchQuantityPoList, List<OriginalFilmCurrentBatchQuantityPO> originalFilmCurrentBatchQuantityVOList) {
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String defaultScenario = defaultScenario1.getData();
        List<InventoryRealTimeDataPO> inventoryRealTimeDataPoList = new ArrayList<>();

        // 合并循环，通过引入一个辅助方法简化对象的创建和属性设置过程
        if (CollectionUtils.isNotEmpty(currentBatchQuantityPoList)) {
            inventoryRealTimeDataPoList.addAll(createInventoryDataPos(currentBatchQuantityPoList));
        }

        if (CollectionUtils.isNotEmpty(originalFilmCurrentBatchQuantityVOList)) {
            inventoryRealTimeDataPoList.addAll(createInventoryDataPos(originalFilmCurrentBatchQuantityVOList));
        }

        if (CollectionUtils.isNotEmpty(inventoryRealTimeDataPoList)){
            //设置库存点编码
            List<String> productCodes = inventoryRealTimeDataPoList.stream().map(InventoryRealTimeDataPO::getProductCode).distinct().collect(Collectors.toList());
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(defaultScenario, productCodes);
            Map<String, String> stockPointMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getStockPointCode, (v1, v2) -> v1));
            inventoryRealTimeDataPoList.forEach(x->{
                x.setStockPointCode(stockPointMap.getOrDefault(x.getProductCode(),null));
            });
        }
        return inventoryRealTimeDataPoList;
    }

    // 辅助方法，用于创建InventoryRealTimeDataPO对象
    private List<InventoryRealTimeDataPO> createInventoryDataPos(List<?> sourceList) {
        List<InventoryRealTimeDataPO> inventoryDataPos = new ArrayList<>();

        for (Object obj : sourceList) {
            if (obj instanceof CurrentBatchQuantityPO) {
                CurrentBatchQuantityPO po = (CurrentBatchQuantityPO) obj;
                InventoryRealTimeDataPO inventoryDataPO = createInventoryDataPO(po);
                inventoryDataPos.add(inventoryDataPO);
            } else if (obj instanceof OriginalFilmCurrentBatchQuantityPO) {
                OriginalFilmCurrentBatchQuantityPO po = (OriginalFilmCurrentBatchQuantityPO) obj;
                InventoryRealTimeDataPO inventoryDataPO = createInventoryDataPO(po);
                inventoryDataPos.add(inventoryDataPO);
            }
        }

        return inventoryDataPos;
    }

    // 为CurrentBatchQuantityPO创建InventoryRealTimeDataPO
    private InventoryRealTimeDataPO createInventoryDataPO(CurrentBatchQuantityPO po) {
        InventoryRealTimeDataPO inventoryDataPO = new InventoryRealTimeDataPO();
        // 确保不为null的安全访问
        inventoryDataPO.setId(po.getId());
        inventoryDataPO.setProductCode(po.getProductCode());
        String operationCode = null;
        if (StringUtils.isNotEmpty(po.getProductCode()) && po.getProductCode().contains("-")) {
            operationCode = po.getProductCode().split("-")[0];
        }
        inventoryDataPO.setOperationCode(operationCode);
        inventoryDataPO.setAvailableQuantity(po.getQuantity());
        inventoryDataPO.setUnit(po.getUnit());
        inventoryDataPO.setUpdateTime(po.getLastUpdateTime());
        BasePOUtils.insertFiller(inventoryDataPO);
        return inventoryDataPO;
    }

    // 为OriginalFilmCurrentBatchQuantityPO创建InventoryRealTimeDataPO
    private InventoryRealTimeDataPO createInventoryDataPO(OriginalFilmCurrentBatchQuantityPO po) {
        InventoryRealTimeDataPO inventoryDataPO = new InventoryRealTimeDataPO();
        inventoryDataPO.setProductCode(po.getOriginalFilmCode());
        inventoryDataPO.setAvailableQuantity(po.getCurrentPieces());
        inventoryDataPO.setUnit(po.getPiecesPerBox() + "片/箱");
        inventoryDataPO.setUpdateTime(po.getLastUpdateTime());
        BasePOUtils.insertFiller(inventoryDataPO);
        return inventoryDataPO;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return inventoryRealTimeDataDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<InventoryDataDTO> selectInventoryByProductCodes(List<String> productCodes) {
        return inventoryRealTimeDataDao.selectInventoryByProductCodes(productCodes);
    }
}
