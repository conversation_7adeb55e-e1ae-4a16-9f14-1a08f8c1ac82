<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO">
        <!--@Table fdp_new_product_trial_submission_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="submission_id" jdbcType="VARCHAR" property="submissionId"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="feeding_quantity" jdbcType="VARCHAR" property="feedingQuantity"/>
        <result column="feeding_unit" jdbcType="VARCHAR" property="feedingUnit"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_code" jdbcType="VARCHAR" property="partCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="loading_position" jdbcType="VARCHAR" property="loadingPosition"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="pretreatment" jdbcType="INTEGER" property="pretreatment"/>
        <result column="shaping" jdbcType="INTEGER" property="shaping"/>
        <result column="lamination" jdbcType="INTEGER" property="lamination"/>
        <result column="assembly" jdbcType="INTEGER" property="assembly"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO">
        <!-- TODO -->
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="project_number" jdbcType="VARCHAR" property="projectNumber"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="trial_product" jdbcType="VARCHAR" property="trialProduct"/>
        <result column="trial_purpose" jdbcType="VARCHAR" property="trialPurpose"/>
        <result column="trial_process" jdbcType="VARCHAR" property="trialProcess"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="cutting_drawing_number" jdbcType="VARCHAR" property="cuttingDrawingNumber"/>
        <result column="planner" jdbcType="VARCHAR" property="planner"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="approve_status" jdbcType="VARCHAR" property="approveStatus"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="finished_flag" jdbcType="VARCHAR" property="finishedFlag"/>
        <result column="erp_customer_id" jdbcType="VARCHAR" property="erpCustomerId"/>
        <result column="erp_ship_to_site_use_id" jdbcType="VARCHAR" property="erpShipToSiteUseId"/>
        <result column="erp_site_code" jdbcType="VARCHAR" property="erpSiteCode"/>
    </resultMap>
    <sql id="Base_Column_List">
id,submission_id,demand_time,demand_quantity,feeding_quantity,feeding_unit,remark,enabled,creator,create_time,modifier,modify_time,version_value,product_code,part_code,product_name,loading_position,work_order_id,pretreatment,shaping,lamination,assembly
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,customer_code,erp_ship_to_site_use_id,erp_customer_id,erp_site_code,oem_code,oem_name,project_number,project_name,trial_product,trial_purpose,trial_process,bill_no,company_name,
        dept_name,user_name,cutting_drawing_number,planner,product_type,approve_status,plan_status,finished_flag
    </sql>
    <sql id="VO_Column_List_2">
        <!-- TODO -->
        <include refid="Base_Column_List" />,customer_code,oem_code,oem_name,project_number,project_name,trial_product,trial_purpose,trial_process,bill_no,company_name,
        dept_name,user_name,cutting_drawing_number,planner,product_type,approve_status,plan_status,finished_flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.submissionId != null and params.submissionId != ''">
                and submission_id = #{params.submissionId,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionIds != null and params.submissionIds != ''">
                and submission_id in
                <foreach collection="params.submissionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.demandTime != null">
                and demand_time = #{params.demandTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.feedingQuantity != null">
                and feeding_quantity = #{params.feedingQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.feedingUnit != null and params.feedingUnit != ''">
                and feeding_unit = #{params.feedingUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.partCode != null and params.partCode != ''">
                and part_code = #{params.partCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPosition != null and params.loadingPosition != ''">
                and loading_position = #{params.loadingPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.workOrderId != null and params.workOrderId != ''">
                and work_order_id = #{params.workOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.workOrderIds != null and params.workOrderIds.size() > 0">
                and work_order_id in
                <foreach collection="params.workOrderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.pretreatment != null">
                and pretreatment = #{params.pretreatment,jdbcType=INTEGER}
            </if>
            <if test="params.shaping != null">
                and shaping = #{params.shaping,jdbcType=INTEGER}
            </if>
            <if test="params.lamination != null">
                and lamination = #{params.lamination,jdbcType=INTEGER}
            </if>
            <if test="params.assembly != null">
                and assembly = #{params.assembly,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_product_trial_submission_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_product_trial_submission_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List_2" />
        from v_fdp_new_product_trial_submission_detail_2
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_product_trial_submission_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_new_product_trial_submission_detail
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectBySubmissionIdList" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_fdp_new_product_trial_submission_detail where submission_id in
        <foreach collection="submissionIdList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_new_product_trial_submission_detail(
        id,
        submission_id,
        demand_time,
        demand_quantity,
        feeding_quantity,
        feeding_unit,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        product_code,
        part_code,
        product_name,
        loading_position,
        work_order_id,
        pretreatment,
        shaping,
        lamination,
        assembly)
        values (
        #{id,jdbcType=VARCHAR},
        #{submissionId,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{feedingQuantity,jdbcType=VARCHAR},
        #{feedingUnit,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{productCode,jdbcType=VARCHAR},
        #{partCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{loadingPosition,jdbcType=VARCHAR},
        #{workOrderId,jdbcType=VARCHAR},
        #{pretreatment,jdbcType=INTEGER},
        #{shaping,jdbcType=INTEGER},
        #{lamination,jdbcType=INTEGER},
        #{assembly,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO">
        insert into fdp_new_product_trial_submission_detail(
        id,
        submission_id,
        demand_time,
        demand_quantity,
        feeding_quantity,
        feeding_unit,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        product_code,
        part_code,
        product_name,
        loading_position,
        work_order_id,
        pretreatment,
        shaping,
        lamination,
        assembly)
        values (
        #{id,jdbcType=VARCHAR},
        #{submissionId,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{feedingQuantity,jdbcType=VARCHAR},
        #{feedingUnit,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{productCode,jdbcType=VARCHAR},
        #{partCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{loadingPosition,jdbcType=VARCHAR},
        #{workOrderId,jdbcType=VARCHAR},
        #{pretreatment,jdbcType=INTEGER},
        #{shaping,jdbcType=INTEGER},
        #{lamination,jdbcType=INTEGER},
        #{assembly,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_new_product_trial_submission_detail(
        id,
        submission_id,
        demand_time,
        demand_quantity,
        feeding_quantity,
        feeding_unit,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        product_code,
        part_code,
        product_name,
        loading_position,
        work_order_id,
        pretreatment,
        shaping,
        lamination,
        assembly)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.submissionId,jdbcType=VARCHAR},
        #{entity.demandTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.feedingQuantity,jdbcType=VARCHAR},
        #{entity.feedingUnit,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.loadingPosition,jdbcType=VARCHAR},
        #{entity.workOrderId,jdbcType=VARCHAR},
        #{entity.pretreatment,jdbcType=INTEGER},
        #{entity.shaping,jdbcType=INTEGER},
        #{entity.lamination,jdbcType=INTEGER},
        #{entity.assembly,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_new_product_trial_submission_detail(
        id,
        submission_id,
        demand_time,
        demand_quantity,
        feeding_quantity,
        feeding_unit,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        product_code,
        part_code,
        product_name,
        loading_position,
        work_order_id,
        pretreatment,
        shaping,
        lamination,
        assembly)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.submissionId,jdbcType=VARCHAR},
        #{entity.demandTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.feedingQuantity,jdbcType=VARCHAR},
        #{entity.feedingUnit,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.loadingPosition,jdbcType=VARCHAR},
        #{entity.workOrderId,jdbcType=VARCHAR},
        #{entity.pretreatment,jdbcType=INTEGER},
        #{entity.shaping,jdbcType=INTEGER},
        #{entity.lamination,jdbcType=INTEGER},
        #{entity.assembly,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO">
        update fdp_new_product_trial_submission_detail set
        submission_id = #{submissionId,jdbcType=VARCHAR},
        demand_time = #{demandTime,jdbcType=TIMESTAMP},
        demand_quantity = #{demandQuantity,jdbcType=VARCHAR},
        feeding_quantity = #{feedingQuantity,jdbcType=VARCHAR},
        feeding_unit = #{feedingUnit,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        product_code = #{productCode,jdbcType=VARCHAR},
        part_code = #{partCode,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        loading_position = #{loadingPosition,jdbcType=VARCHAR},
        work_order_id = #{workOrderId,jdbcType=VARCHAR},
        pretreatment = #{pretreatment,jdbcType=INTEGER},
        shaping = #{shaping,jdbcType=INTEGER},
        lamination = #{lamination,jdbcType=INTEGER},
        assembly = #{assembly,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_new_product_trial_submission_detail set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO">
        update fdp_new_product_trial_submission_detail
        <set>
            <if test="item.submissionId != null and item.submissionId != ''">
                submission_id = #{item.submissionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.feedingQuantity != null">
                feeding_quantity = #{item.feedingQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.feedingUnit != null and item.feedingUnit != ''">
                feeding_unit = #{item.feedingUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partCode != null and item.partCode != ''">
                part_code = #{item.partCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPosition != null and item.loadingPosition != ''">
                loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.workOrderId != null and item.workOrderId != ''">
                work_order_id = #{item.workOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.pretreatment != null">
                pretreatment = #{item.pretreatment,jdbcType=INTEGER},
            </if>
            <if test="item.shaping != null">
                shaping = #{item.shaping,jdbcType=INTEGER},
            </if>
            <if test="item.lamination != null">
                lamination = #{item.lamination,jdbcType=INTEGER},
            </if>
            <if test="item.assembly != null">
                assembly = #{item.assembly,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_new_product_trial_submission_detail set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_new_product_trial_submission_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="submission_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="feeding_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.feedingQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="feeding_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.feedingUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loading_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.loadingPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.workOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pretreatment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pretreatment,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="shaping = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shaping,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="lamination = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lamination,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="assembly = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.assembly,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_new_product_trial_submission_detail set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_new_product_trial_submission_detail
        <set>
            <if test="item.submissionId != null and item.submissionId != ''">
                submission_id = #{item.submissionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.feedingQuantity != null">
                feeding_quantity = #{item.feedingQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.feedingUnit != null and item.feedingUnit != ''">
                feeding_unit = #{item.feedingUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partCode != null and item.partCode != ''">
                part_code = #{item.partCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPosition != null and item.loadingPosition != ''">
                loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.workOrderId != null and item.workOrderId != ''">
                work_order_id = #{item.workOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.pretreatment != null">
                pretreatment = #{item.pretreatment,jdbcType=INTEGER},
            </if>
            <if test="item.shaping != null">
                shaping = #{item.shaping,jdbcType=INTEGER},
            </if>
            <if test="item.lamination != null">
                lamination = #{item.lamination,jdbcType=INTEGER},
            </if>
            <if test="item.assembly != null">
                assembly = #{item.assembly,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_new_product_trial_submission_detail set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_new_product_trial_submission_detail where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_new_product_trial_submission_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteBatchBySubmissionIds">
        delete from fdp_new_product_trial_submission_detail where submission_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteBySubmissionId">
        delete from fdp_new_product_trial_submission_detail where submission_id = #{submissionId,jdbcType=VARCHAR}
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_new_product_trial_submission_detail where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
