package com.yhl.scp.dfp.demand.infrastructure.dao;

import com.yhl.scp.dfp.demand.vo.DemandForecastOemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>DemandForecastEstablishmentDao</code>
 * <p>
 * 业务预测编制DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 09:35:29
 */
public interface DemandForecastOemDao {

    List<DemandForecastOemVO> selectOemDropdown(@Param("versionId") String versionId, @Param("demandCategory") String demandCategory);

}
