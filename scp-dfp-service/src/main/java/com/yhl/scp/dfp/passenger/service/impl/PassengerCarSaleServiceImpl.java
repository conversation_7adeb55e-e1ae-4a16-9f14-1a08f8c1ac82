package com.yhl.scp.dfp.passenger.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.PageUtils;
import com.yhl.scp.dfp.car.dto.CarPriceListImportDTO;
import com.yhl.scp.dfp.car.service.CarPriceListImportService;
import com.yhl.scp.dfp.car.service.CarPriceListService;
import com.yhl.scp.dfp.car.vo.CarPriceListImportVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.dfp.passenger.convertor.PassengerCarSaleConvertor;
import com.yhl.scp.dfp.passenger.domain.entity.PassengerCarSaleDO;
import com.yhl.scp.dfp.passenger.domain.service.PassengerCarSaleDomainService;
import com.yhl.scp.dfp.passenger.dto.PassengerCarSaleDTO;
import com.yhl.scp.dfp.passenger.dto.PassengerCarSaleImportDTO;
import com.yhl.scp.dfp.passenger.infrastructure.dao.PassengerCarSaleDao;
import com.yhl.scp.dfp.passenger.infrastructure.po.PassengerCarSalePO;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleImportService;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleService;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleDetailVO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.demand.vo.DemandEarlyWarningVO;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>PassengerCarSaleServiceImpl</code>
 * <p>
 * 乘用车市场信息应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:37
 */
@Slf4j
@Service
public class PassengerCarSaleServiceImpl extends AbstractService implements PassengerCarSaleService {

    @Resource
    private PassengerCarSaleDao passengerCarSaleDao;

    @Resource
    private PassengerCarSaleDomainService passengerCarSaleDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Resource
    private CarPriceListService carPriceListService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;
    
    @Resource
    private PassengerCarSaleImportService passengerCarSaleImportService;

    @Resource
    private CarPriceListImportService carPriceListImportService;

    @Override
    public BaseResponse<Void> doCreate(PassengerCarSaleDTO passengerCarSaleDTO) {
        // 0.数据转换
        PassengerCarSaleDO passengerCarSaleDO = PassengerCarSaleConvertor.INSTANCE.dto2Do(passengerCarSaleDTO);
        PassengerCarSalePO passengerCarSalePO = PassengerCarSaleConvertor.INSTANCE.dto2Po(passengerCarSaleDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        passengerCarSaleDomainService.validation(passengerCarSaleDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(passengerCarSalePO);
        passengerCarSaleDao.insertWithPrimaryKey(passengerCarSalePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PassengerCarSaleDTO passengerCarSaleDTO) {
        // 0.数据转换
        PassengerCarSaleDO passengerCarSaleDO = PassengerCarSaleConvertor.INSTANCE.dto2Do(passengerCarSaleDTO);
        PassengerCarSalePO passengerCarSalePO = PassengerCarSaleConvertor.INSTANCE.dto2Po(passengerCarSaleDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        passengerCarSaleDomainService.validation(passengerCarSaleDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(passengerCarSalePO);
        passengerCarSaleDao.update(passengerCarSalePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PassengerCarSaleDTO> list) {
        List<PassengerCarSalePO> newList = PassengerCarSaleConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        passengerCarSaleDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<PassengerCarSaleDTO> list) {
        List<PassengerCarSalePO> newList = PassengerCarSaleConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        passengerCarSaleDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return passengerCarSaleDao.deleteBatch(idList);
        }
        return passengerCarSaleDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PassengerCarSaleVO selectByPrimaryKey(String id) {
        PassengerCarSalePO po = passengerCarSaleDao.selectByPrimaryKey(id);
        return PassengerCarSaleConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_passenger_car_sale")
    public List<PassengerCarSaleVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_passenger_car_sale")
    public List<PassengerCarSaleVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PassengerCarSaleVO> dataList = passengerCarSaleDao.selectByCondition(sortParam, queryCriteriaParam);
        PassengerCarSaleServiceImpl target = springBeanUtils.getBean(PassengerCarSaleServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PassengerCarSaleVO> selectByParams(Map<String, Object> params) {
        List<PassengerCarSalePO> list = passengerCarSaleDao.selectByParams(params);
        return PassengerCarSaleConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PassengerCarSaleVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PASSENGER_CAR_SALE.getCode();
    }

    @Override
    public List<PassengerCarSaleVO> invocation(List<PassengerCarSaleVO> dataList, Map<String, Object> params,
                                               String invocation) {
//        if (CollectionUtils.isEmpty(dataList)) {
//            return dataList;
//        }
//        Date currentDate = new Date();
//        List<PassengerCarSaleVO> passengerCarSaleVOS = this.selectAll();
//        passengerCarSaleVOS = passengerCarSaleVOS.stream().filter( e-> YesOrNoEnum.YES.getCode().equals(e.getEnabled()))
//        		.collect(Collectors.toList());
//        Map<String, Map<String, Integer>> yearMonthQuantityMap = CollectionUtils.isEmpty(passengerCarSaleVOS) ?
//                MapUtil.newHashMap() :
//                passengerCarSaleVOS.stream().collect(Collectors.groupingBy(x -> StringUtils.joinWith("_",
//                                x.getOemCode(), x.getVehicleModelCode()),
//                        Collectors.groupingBy(x -> DateUtils.dateToString(x.getSaleTime(), DateUtils.YEAR_MONTH),
//                                Collectors.summingInt(PassengerCarSaleVO::getSaleQuantity))));
//        for (PassengerCarSaleVO passengerCarSaleVO : dataList) {
//            Map<String, Integer> quantityMap = yearMonthQuantityMap.getOrDefault(StringUtils.joinWith("_",
//                    passengerCarSaleVO.getOemCode(), passengerCarSaleVO.getVehicleModelCode()), MapUtil.newHashMap());
//            List<PassengerCarSaleDetailVO> detailVOList = Lists.newArrayList();
//            for (int i = 0; i < 12; i++) {
//                String columnName =
//                        DateUtils.dateToString(org.apache.commons.lang3.time.DateUtils.addMonths(currentDate, -i),
//                                DateUtils.YEAR_MONTH);
//                PassengerCarSaleDetailVO detailVO = new PassengerCarSaleDetailVO();
//                detailVO.setColumnName(columnName);
//                Integer quantity = quantityMap.getOrDefault(columnName, 0);
//                detailVO.setSaleQuantity(quantity);
//                detailVOList.add(detailVO);
//            }
//            passengerCarSaleVO.setDetailVOList(detailVOList.stream().sorted(Comparator
//                    .comparing(PassengerCarSaleDetailVO::getColumnName)).collect(Collectors.toList()));
//        }
        return dataList;
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String,
            String> extMap) {
        List<OemVehicleModelMapVO> oemVehicleModelMapVOS = oemVehicleModelMapService.selectAll();
        if (CollectionUtils.isEmpty(oemVehicleModelMapVOS)) {
            return;
        }
        String importType = extMap.get("importType");
        String yearMonth = extMap.get("yearMonth");
        if ("SALE".equals(importType)) {
            // 乘联品牌2，乘联车型号9，价格10
            Map<String, OemVehicleModelMapVO> vehicleBrandMap = oemVehicleModelMapVOS.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getVehicleModelBrand()) && StringUtils.isNotBlank(x.getVehicleModel()))
                    .collect(Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelBrand(), x.getVehicleModel()),
                            Function.identity(), (v1, v2) -> v1));
            List<CarPriceListImportVO> carPriceListVOS = carPriceListImportService.selectAll();
            Map<String, CarPriceListImportVO> oldCarPriceMap = CollectionUtils.isEmpty(carPriceListVOS) ?
                    MapUtil.newHashMap() :
                    carPriceListVOS.stream().collect(
                            Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelBrand(), x.getVehicleModel()
                                            , DateUtils.dateToString(x.getPriceDate(), "yyyyMM")),
                                    Function.identity(), (v1, v2) -> v1));
            List<CarPriceListImportDTO> insertCarPriceListImport = Lists.newArrayList();
            String preVehicleModelBrand = "";
            for (Map<Integer, String> datum : data) {
                CarPriceListImportDTO carPriceListImportDTO = new CarPriceListImportDTO();
                String vehicleModelBrand = datum.get(2);// 品牌
                String model = datum.get(3);// 型号
                String vehicleModel = datum.get(9);// 车型型号
                String msrp = datum.get(10);// 价格
                if (StringUtils.isNotBlank(vehicleModelBrand)) {
                    preVehicleModelBrand = vehicleModelBrand;
                }
                String dataKey = StringUtils.join("_", preVehicleModelBrand, model);
                carPriceListImportDTO.setVehicleModelBrand(preVehicleModelBrand);
                carPriceListImportDTO.setVehicleModel(vehicleModel);
                carPriceListImportDTO.setPriceDate(DateUtil.parse(yearMonth, "yyyyMM"));
                carPriceListImportDTO.setMsrp(msrp);
                OemVehicleModelMapVO oemVehicleModelMapVO = vehicleBrandMap.get(dataKey);

                if (Objects.nonNull(oemVehicleModelMapVO)) {
                    carPriceListImportDTO.setOemCode(oemVehicleModelMapVO.getOemCode());
                    carPriceListImportDTO.setVehicleModelCode(oemVehicleModelMapVO.getVehicleModelCode());
                }
                CarPriceListImportVO oldCarPriceListVO = oldCarPriceMap.get(dataKey);
                if (Objects.nonNull(oldCarPriceListVO)) {
                    carPriceListImportDTO.setId(oldCarPriceListVO.getId());
                    insertCarPriceListImport.add(carPriceListImportDTO);
                } else {
                    insertCarPriceListImport.add(carPriceListImportDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(insertCarPriceListImport)) {
                carPriceListImportService.deleteByYearMonth(yearMonth);
                carPriceListImportService.doCreateBatch(insertCarPriceListImport);
                //执行更新车型价格信息
                CarPriceListImportDTO carPriceListImportDTO = new CarPriceListImportDTO();
                carPriceListImportDTO.setStatrYearMonth(yearMonth);
                carPriceListImportDTO.setEndYearMonth(yearMonth);
                carPriceListImportService.updateCarData(carPriceListImportDTO);
            }
        } else if ("QUANTITY".equals(importType)) {
            Map<String, List<OemVehicleModelMapVO>> vehicleNameMap = oemVehicleModelMapVOS.stream()
                    .filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x.getVehicleModelName()))
                    .collect(Collectors.groupingBy(OemVehicleModelMapVO::getVehicleModelName));
            Set<String> uniqueInsertKeys = new HashSet<>();
            List<PassengerCarSaleImportDTO> insertPassengerCarSales = Lists.newArrayList();
            // 乘联车型名称2，销量3，产量11
            for (int i = 8; i < data.size(); i++) {
                Map<Integer, String> dataMap = data.get(i);
                String vehicleModelName = dataMap.get(2);
                if (StringUtils.isBlank(vehicleModelName) || vehicleModelName.contains("小计") || vehicleModelName.contains("合计")) {
                    continue;
                }
                int productQuantity = 0;
                if (Objects.nonNull(dataMap.get(3)) && StringUtils.isNotBlank(dataMap.get(3))) {
                    productQuantity = Integer.parseInt(dataMap.get(3));
                }

                int saleQuantity = 0;
                if (Objects.nonNull(dataMap.get(11)) && StringUtils.isNotBlank(dataMap.get(11))) {
                    saleQuantity = Integer.parseInt(dataMap.get(11));
                }
                // 获取当前车型名称对应的多个车型映射
                PassengerCarSaleImportDTO passengerCarSaleDTO = new PassengerCarSaleImportDTO();
                passengerCarSaleDTO.setSaleTime(DateUtil.parse(yearMonth, "yyyyMM"));
                passengerCarSaleDTO.setSaleQuantity(saleQuantity);
                passengerCarSaleDTO.setVehicleModelName(vehicleModelName);
                passengerCarSaleDTO.setProductQuantity(productQuantity);
                String key = StringUtils.join("_", vehicleModelName, yearMonth);
                if (uniqueInsertKeys.add(key)) {
                    insertPassengerCarSales.add(passengerCarSaleDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(insertPassengerCarSales)) {
            	//按照年月删除数据
            	passengerCarSaleImportService.deleteByYearMonth(yearMonth);
            	passengerCarSaleImportService.doCreateBatch(insertPassengerCarSales);
            	//执行更新车型销量数据
            	PassengerCarSaleImportDTO passengerCarSaleImportDTO = new PassengerCarSaleImportDTO();
            	passengerCarSaleImportDTO.setStatrYearMonth(yearMonth);
            	passengerCarSaleImportDTO.setEndYearMonth(yearMonth);
                passengerCarSaleImportService.updateSaleData(passengerCarSaleImportDTO);
            }
        }
    }

	@Override
	public void doUpdateEnableNo(List<String> ids) {
		passengerCarSaleDao.doUpdateEnableNo(ids);
	}

	@Override
	public PageInfo<PassengerCarSaleVO> selectByNewPage(PassengerCarSaleDTO dto) {
		Map<String, Object> params = new HashMap<>();
		params.put("oemCode", dto.getOemCode());
		params.put("oemName", dto.getOemName());
		params.put("vehicleModelCode", dto.getVehicleModelCode());
		params.put("vehicleModelNameLike", dto.getVehicleModelName());
		Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1); // 前一年
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // 第一个月
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startSaleTime = calendar.getTime();
        Date endSaleTime = DateUtils.getMonthLastDay(new Date());
		params.put("startSaleTime", startSaleTime);
		params.put("endSaleTime", endSaleTime);
		List<PassengerCarSaleVO> passengerCarSaleList = passengerCarSaleDao.selectVOByParams(params);
		if(CollectionUtils.isEmpty(passengerCarSaleList)) {
			throw new BusinessException("未查询到乘用车数据信息");
		}
		//按照主机厂，内部车型，内部车型名称分组
		Map<String, List<PassengerCarSaleVO>> passengerCarSaleMap = passengerCarSaleList.stream()
				.collect(Collectors.groupingBy(e -> String.join("&", e.getOemCode(), e.getVehicleModelCode(), e.getVehicleModelName())));
		List<String> yearMonthsBetween = getYearMonthsBetween(startSaleTime, endSaleTime);
		String saleFlag = dto.getSaleFlag();
		List<PassengerCarSaleVO> resultList = new ArrayList<>();
		for (List<PassengerCarSaleVO> detailList : passengerCarSaleMap.values()) {
			PassengerCarSaleVO firstVO = detailList.get(0);
			PassengerCarSaleVO passengerCarSaleVO = new PassengerCarSaleVO();
			passengerCarSaleVO.setOemCode(StringUtils.isEmpty(firstVO.getOemCode()) ? "" : firstVO.getOemCode());
			passengerCarSaleVO.setOemName(firstVO.getOemName());
			passengerCarSaleVO.setVehicleModelCode(StringUtils.isEmpty(firstVO.getVehicleModelCode()) ? "" : firstVO.getVehicleModelCode());
			passengerCarSaleVO.setVehicleModelName(StringUtils.isEmpty(firstVO.getVehicleModelName()) ? "" : firstVO.getVehicleModelName());
			Map<String, Integer> detailMap = new HashMap<>();
			if(YesOrNoEnum.YES.getCode().equals(saleFlag)) {
				detailMap = detailList.stream()
						.collect(Collectors.toMap(e -> DateUtils.dateToString(e.getSaleTime(), DateUtils.YEAR_MONTH),PassengerCarSaleVO::getSaleQuantity,(v1, v2) -> v1));
			}else {
				detailMap = detailList.stream()
						.collect(Collectors.toMap(e -> DateUtils.dateToString(e.getSaleTime(), DateUtils.YEAR_MONTH),PassengerCarSaleVO::getProductQuantity,(v1, v2) -> v1));
			}
			List<PassengerCarSaleDetailVO> detailVOList = new ArrayList<>();
			for (String yearMonth : yearMonthsBetween) {
				PassengerCarSaleDetailVO detail = new PassengerCarSaleDetailVO();
				detail.setColumnName(yearMonth);
				detail.setSaleQuantity(detailMap.getOrDefault(yearMonth, 0));
				detailVOList.add(detail);
			}
			passengerCarSaleVO.setDetailVOList(detailVOList);
			resultList.add(passengerCarSaleVO);
		}
		resultList.sort(Comparator.comparing(PassengerCarSaleVO::getOemCode).reversed()
				.thenComparing(PassengerCarSaleVO::getVehicleModelCode)
				.thenComparing(PassengerCarSaleVO::getVehicleModelName)
				);
		return PageUtils.getPageInfo(resultList, dto.getPageNum(),
				dto.getPageSize());
	}

	 public static List<String> getYearMonthsBetween(Date start, Date end) {
	        List<String> result = new ArrayList<>();
	        LocalDate startDate = start.toInstant()
	                .atZone(ZoneId.systemDefault())
	                .toLocalDate();
	        LocalDate endDate = end.toInstant()
	                .atZone(ZoneId.systemDefault())
	                .toLocalDate();
	        // 确保开始时间不晚于结束时间
	        LocalDate current = startDate.isAfter(endDate) ? endDate : startDate;
	        LocalDate stop = startDate.isAfter(endDate) ? startDate : endDate;

	        while (!current.isAfter(stop)) {
	            result.add(current.format(java.time.format.DateTimeFormatter.ofPattern(DateUtils.YEAR_MONTH)));
	            current = current.plusMonths(1);
	        }
	        return result;
	    }
}