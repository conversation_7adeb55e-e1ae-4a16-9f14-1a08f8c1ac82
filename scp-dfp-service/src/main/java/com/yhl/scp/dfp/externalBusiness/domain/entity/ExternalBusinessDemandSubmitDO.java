package com.yhl.scp.dfp.externalBusiness.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <code>ExternalBusinessDemandSubmitDO</code>
 * <p>
 * 外事业部需求提报DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-30 10:37:49
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalBusinessDemandSubmitDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 321578831981865795L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 本厂编码
     */
    private String plantCode;
    /**
     * 需求类型
     */
    private String demandType;
    /**
     * 外事业部需求提报明细
     */
    private List<ExternalBusinessDemandSubmitDetailDO> detailList;

}
