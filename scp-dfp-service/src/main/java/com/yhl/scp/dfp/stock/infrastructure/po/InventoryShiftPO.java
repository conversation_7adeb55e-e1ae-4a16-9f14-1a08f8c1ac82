package com.yhl.scp.dfp.stock.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryShiftPO</code>
 * <p>
 * 库存推移表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 10:27:01
 */
public class InventoryShiftPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 540290952458194412L;

    /**
     * 发货计划版本ID
     */
    private String versionId;
    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 运输路径ID
     */
    private String routingId;
    /**
     * 总供应链天数
     */
    private Integer totalSupplyChainDays;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 市场属性
     */
    private String marketType;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 零件名称
     */
    private String partName;
    /**
     * 计划日期
     */
    private Date plannedDate;

    /**
     * 发货日期
     */
    private Date deliveryDate;
    /**
     * 期初库存
     */
    private Integer openingInventory;

    /**
     * 主机厂期初库存
     */
    private Integer oemOpeningInventory;
    /**
     * 客户需求
     */
    private Integer customerDemand;
    /**
     * 到货计划
     */
    private Integer arrivalPlan;
    /**
     * 发货计划
     */
    private Integer deliveryPlan;
    /**
     * 旧物料发货
     */
    private Integer oldDeliveryPlan;
    /**
     * 新物料发货
     */
    private Integer newDeliveryPlan;
    /**
     * 在途数量
     */
    private Integer receive;
    /**
     * 旧物料在途
     */
    private Integer oldReceive;
    /**
     * 新物料在途
     */
    private Integer newReceive;
    /**
     * 期末库存
     */
    private Integer endingInventory;

    /**
     * 期末库存最小安全库存差值
     */
    private Integer endingInventoryMinSafeDiff;
    /**
     * 主机厂期末库存
     */
    private Integer oemEndingInventory;
    /**
     * 期末库存天数
     */
    private BigDecimal endingInventoryDays;
    /**
     * 旧物料期末库存天数
     */
    private BigDecimal oldEndingInventoryDays;
    /**
     * 新物料期末库存天数
     */
    private BigDecimal newEndingInventoryDays;
    /**
     * 标准安全库存水位
     */
    private Integer standardSafetyInventoryLevel;
    /**
     * 标准安全库存天数
     */
    private BigDecimal standardSafetyInventoryDays;
    /**
     * 最小安全库存天数
     */
    private BigDecimal minimumSafetyInventoryDays;
    /**
     * 累计库存缺口
     */
    private Integer accumulatedInventoryGap;
    /**
     * 目标期末库存天数
     */
    private BigDecimal targetEndingInventoryDays;
    /**
     * 目标期末库存
     */
    private Integer targetEndingInventory;
    /**
     * 目标到货计划
     */
    private Integer targetArrivalPlan;
    
    /**
     * 旧期初库存
     */
    private Integer oldOpeningInventory;
	
	/**
     * 旧到货计划
     */
    private Integer oldArrivalPlan;
	
	/**
     * 旧期末库存
     */
    private Integer oldEndingInventory;
    
	/**
     * 新期初库存
     */
    private Integer newOpeningInventory;
	
	/**
     * 新到货计划
     */
    private Integer newArrivalPlan;
	
	/**
     * 新期末库存
     */
    private Integer newEndingInventory;
    /**
     * 旧物料需求
     */
    private Integer oldDemand;
    /**
     * 新物料需求
     */
    private Integer newDemand;
    /**
     * 旧物料主机厂期初库存
     */
    private Integer oldOemOpeningInventory;
    /**
     * 新物料主机厂期初库存
     */
    private Integer newOemOpeningInventory;
    /**
     * 旧物料主机厂期末库存
     */
    private Integer oldOemEndingInventory;
    /**
     * 新物料主机厂期末库存
     */
    private Integer newOemEndingInventory;
    /**
     * 转换标志
     */
    private String switchSign;

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getRoutingId() {
        return routingId;
    }

    public void setRoutingId(String routingId) {
        this.routingId = routingId;
    }

    public Integer getTotalSupplyChainDays() {
        return totalSupplyChainDays;
    }

    public void setTotalSupplyChainDays(Integer totalSupplyChainDays) {
        this.totalSupplyChainDays = totalSupplyChainDays;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOemName() {
        return oemName;
    }

    public void setOemName(String oemName) {
        this.oemName = oemName;
    }

    public String getMarketType() {
        return marketType;
    }

    public void setMarketType(String marketType) {
        this.marketType = marketType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public Date getPlannedDate() {
        return plannedDate;
    }

    public void setPlannedDate(Date plannedDate) {
        this.plannedDate = plannedDate;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Integer getOpeningInventory() {
        return openingInventory;
    }

    public void setOpeningInventory(Integer openingInventory) {
        this.openingInventory = openingInventory;
    }

    public Integer getCustomerDemand() {
        return customerDemand;
    }

    public void setCustomerDemand(Integer customerDemand) {
        this.customerDemand = customerDemand;
    }

    public Integer getArrivalPlan() {
        return arrivalPlan;
    }

    public void setArrivalPlan(Integer arrivalPlan) {
        this.arrivalPlan = arrivalPlan;
    }

    public Integer getDeliveryPlan() {
        return deliveryPlan;
    }

    public void setDeliveryPlan(Integer deliveryPlan) {
        this.deliveryPlan = deliveryPlan;
    }

    public Integer getOldDeliveryPlan() {
        return oldDeliveryPlan;
    }

    public void setOldDeliveryPlan(Integer oldDeliveryPlan) {
        this.oldDeliveryPlan = oldDeliveryPlan;
    }

    public Integer getNewDeliveryPlan() {
        return newDeliveryPlan;
    }

    public void setNewDeliveryPlan(Integer newDeliveryPlan) {
        this.newDeliveryPlan = newDeliveryPlan;
    }

    public Integer getReceive() {
        return receive;
    }

    public void setReceive(Integer receive) {
        this.receive = receive;
    }

    public Integer getOldReceive() {
        return oldReceive;
    }

    public void setOldReceive(Integer oldReceive) {
        this.oldReceive = oldReceive;
    }

    public Integer getNewReceive() {
        return newReceive;
    }

    public void setNewReceive(Integer newReceive) {
        this.newReceive = newReceive;
    }

    public Integer getEndingInventory() {
        return endingInventory;
    }

    public void setEndingInventory(Integer endingInventory) {
        this.endingInventory = endingInventory;
    }

    public BigDecimal getEndingInventoryDays() {
        return endingInventoryDays;
    }

    public Integer getEndingInventoryMinSafeDiff() {
        return endingInventoryMinSafeDiff;
    }

    public BigDecimal getOldEndingInventoryDays() {
        return oldEndingInventoryDays;
    }

    public void setOldEndingInventoryDays(BigDecimal oldEndingInventoryDays) {
        this.oldEndingInventoryDays = oldEndingInventoryDays;
    }

    public BigDecimal getNewEndingInventoryDays() {
        return newEndingInventoryDays;
    }

    public void setNewEndingInventoryDays(BigDecimal newEndingInventoryDays) {
        this.newEndingInventoryDays = newEndingInventoryDays;
    }

    public void setEndingInventoryMinSafeDiff(Integer endingInventoryMinSafeDiff) {
        this.endingInventoryMinSafeDiff = endingInventoryMinSafeDiff;
    }

    public void setEndingInventoryDays(BigDecimal endingInventoryDays) {
        this.endingInventoryDays = endingInventoryDays;
    }

    public Integer getStandardSafetyInventoryLevel() {
        return standardSafetyInventoryLevel;
    }

    public void setStandardSafetyInventoryLevel(Integer standardSafetyInventoryLevel) {
        this.standardSafetyInventoryLevel = standardSafetyInventoryLevel;
    }

    public BigDecimal getStandardSafetyInventoryDays() {
        return standardSafetyInventoryDays;
    }

    public void setStandardSafetyInventoryDays(BigDecimal standardSafetyInventoryDays) {
        this.standardSafetyInventoryDays = standardSafetyInventoryDays;
    }

    public BigDecimal getMinimumSafetyInventoryDays() {
        return minimumSafetyInventoryDays;
    }

    public void setMinimumSafetyInventoryDays(BigDecimal minimumSafetyInventoryDays) {
        this.minimumSafetyInventoryDays = minimumSafetyInventoryDays;
    }

    public Integer getAccumulatedInventoryGap() {
        return accumulatedInventoryGap;
    }

    public void setAccumulatedInventoryGap(Integer accumulatedInventoryGap) {
        this.accumulatedInventoryGap = accumulatedInventoryGap;
    }

    public BigDecimal getTargetEndingInventoryDays() {
        return targetEndingInventoryDays;
    }

    public void setTargetEndingInventoryDays(BigDecimal targetEndingInventoryDays) {
        this.targetEndingInventoryDays = targetEndingInventoryDays;
    }

    public Integer getTargetEndingInventory() {
        return targetEndingInventory;
    }

    public void setTargetEndingInventory(Integer targetEndingInventory) {
        this.targetEndingInventory = targetEndingInventory;
    }

    public Integer getTargetArrivalPlan() {
        return targetArrivalPlan;
    }

    public void setTargetArrivalPlan(Integer targetArrivalPlan) {
        this.targetArrivalPlan = targetArrivalPlan;
    }

    public Integer getOemOpeningInventory() {
        return oemOpeningInventory;
    }

    public void setOemOpeningInventory(Integer oemOpeningInventory) {
        this.oemOpeningInventory = oemOpeningInventory;
    }

    public Integer getOemEndingInventory() {
        return oemEndingInventory;
    }

    public void setOemEndingInventory(Integer oemEndingInventory) {
        this.oemEndingInventory = oemEndingInventory;
    }

	public Integer getOldOpeningInventory() {
		return oldOpeningInventory;
	}

	public void setOldOpeningInventory(Integer oldOpeningInventory) {
		this.oldOpeningInventory = oldOpeningInventory;
	}

	public Integer getOldArrivalPlan() {
		return oldArrivalPlan;
	}

	public void setOldArrivalPlan(Integer oldArrivalPlan) {
		this.oldArrivalPlan = oldArrivalPlan;
	}

	public Integer getOldEndingInventory() {
		return oldEndingInventory;
	}

	public void setOldEndingInventory(Integer oldEndingInventory) {
		this.oldEndingInventory = oldEndingInventory;
	}

	public Integer getNewOpeningInventory() {
		return newOpeningInventory;
	}

	public void setNewOpeningInventory(Integer newOpeningInventory) {
		this.newOpeningInventory = newOpeningInventory;
	}

	public Integer getNewArrivalPlan() {
		return newArrivalPlan;
	}

	public void setNewArrivalPlan(Integer newArrivalPlan) {
		this.newArrivalPlan = newArrivalPlan;
	}

	public Integer getNewEndingInventory() {
		return newEndingInventory;
	}

	public void setNewEndingInventory(Integer newEndingInventory) {
		this.newEndingInventory = newEndingInventory;
	}

    public Integer getOldDemand() {
        return oldDemand;
    }

    public void setOldDemand(Integer oldDemand) {
        this.oldDemand = oldDemand;
    }

    public Integer getNewDemand() {
        return newDemand;
    }

    public void setNewDemand(Integer newDemand) {
        this.newDemand = newDemand;
    }

    public Integer getOldOemOpeningInventory() {
        return oldOemOpeningInventory;
    }

    public void setOldOemOpeningInventory(Integer oldOemOpeningInventory) {
        this.oldOemOpeningInventory = oldOemOpeningInventory;
    }

    public Integer getNewOemOpeningInventory() {
        return newOemOpeningInventory;
    }

    public void setNewOemOpeningInventory(Integer newOemOpeningInventory) {
        this.newOemOpeningInventory = newOemOpeningInventory;
    }

    public Integer getOldOemEndingInventory() {
        return oldOemEndingInventory;
    }

    public void setOldOemEndingInventory(Integer oldOemEndingInventory) {
        this.oldOemEndingInventory = oldOemEndingInventory;
    }

    public Integer getNewOemEndingInventory() {
        return newOemEndingInventory;
    }

    public void setNewOemEndingInventory(Integer newOemEndingInventory) {
        this.newOemEndingInventory = newOemEndingInventory;
    }

    public String getSwitchSign() {
        return switchSign;
    }

    public void setSwitchSign(String switchSign) {
        this.switchSign = switchSign;
    }
}
