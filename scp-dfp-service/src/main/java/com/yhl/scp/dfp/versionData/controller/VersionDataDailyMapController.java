package com.yhl.scp.dfp.versionData.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.versionData.dto.VersionDataDailyMapDTO;
import com.yhl.scp.dfp.versionData.service.VersionDataDailyMapService;
import com.yhl.scp.dfp.versionData.vo.VersionDataDailyMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VersionDataDailyMapController</code>
 * <p>
 * 日需求版本数据映射关系控制器
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:25:58
 */
@Slf4j
@Api(tags = "日需求版本数据映射关系控制器")
@RestController
@RequestMapping("versionDataDailyMap")
public class VersionDataDailyMapController extends BaseController {

    @Resource
    private VersionDataDailyMapService versionDataDailyMapService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<VersionDataDailyMapVO>> page() {
        List<VersionDataDailyMapVO> versionDataDailyMapList = versionDataDailyMapService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VersionDataDailyMapVO> pageInfo = new PageInfo<>(versionDataDailyMapList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VersionDataDailyMapDTO versionDataDailyMapDTO) {
        return versionDataDailyMapService.doCreate(versionDataDailyMapDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VersionDataDailyMapDTO versionDataDailyMapDTO) {
        return versionDataDailyMapService.doUpdate(versionDataDailyMapDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        versionDataDailyMapService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<VersionDataDailyMapVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, versionDataDailyMapService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        versionDataDailyMapService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
