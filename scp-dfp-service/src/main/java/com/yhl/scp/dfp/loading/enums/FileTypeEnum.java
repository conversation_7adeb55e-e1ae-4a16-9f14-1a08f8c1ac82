package com.yhl.scp.dfp.loading.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>FileTypeEnum</code>
 * <p>
 * 文件类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-29 10:59:16
 */
public enum FileTypeEnum implements CommonEnum {

	SOURCE_FILE("SOURCE_FILE", "源文件"),
	IMPORT_FILE("IMPORT_FILE", "导入文件"),
    ;

    private String code;
    private String desc;

    FileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
